SELECT
  'Buy' as `Feature`,
  IFNULL(SUM(profit), 0) AS `Profit`,
  IFNULL(SUM(amount), 0) AS `Volume`,
  Count(1) AS `Count`,
  Count(DISTINCT uid) AS `User`  
FROM `kryptogo-wallet-app-staging.MySQL.asset_pro_revenue` 
WHERE organization_id = $organization_id 
  AND $__timeFilter(timestamp) 
  AND type = 'buy_crypto'

UNION ALL

SELECT
  'Swap (Gas (TRON))' as `Feature`,
  IFNULL(SUM(profit), 0) AS `Revenue`,
  IFNULL(SUM(amount), 0) AS `Volume`,
  Count(1) AS `Count`,
  Count(DISTINCT uid) AS `User`  
FROM `kryptogo-wallet-app-staging.MySQL.asset_pro_revenue` 
WHERE organization_id = $organization_id 
  AND $__timeFilter(timestamp) 
  AND type = 'gas_swap'

UNION ALL

SELECT
  'Swap (Defi (EVM))' as `Feature`,
  IFNULL(SUM(profit), 0) AS `Revenue`,
  IFNULL(SUM(amount), 0) AS `Volume`,
  Count(1) AS `Count`,
  Count(DISTINCT uid) AS `User`  
FROM `kryptogo-wallet-app-staging.MySQL.asset_pro_revenue` 
WHERE organization_id = $organization_id AND $__timeFilter(timestamp) AND type = 'defi_swap'

UNION ALL

SELECT
  'Send (Gasless(TRON))' as `Feature`,
  IFNULL(SUM(profit), 0) AS `Revenue`,
  IFNULL(SUM(amount), 0) AS `Volume`,
  Count(1) AS `Count`,
  Count(DISTINCT uid) AS `User`  
FROM `kryptogo-wallet-app-staging.MySQL.asset_pro_revenue` 
WHERE organization_id = $organization_id AND $__timeFilter(timestamp) AND type = 'gasless_send'

UNION ALL

SELECT
  'Send (TRON)' as `Feature`,
  IFNULL(SUM(profit), 0) AS `Revenue`,
  IFNULL(SUM(amount), 0) AS `Volume`,
  Count(1) AS `Count`,
  Count(DISTINCT uid) AS `User`  
FROM `kryptogo-wallet-app-staging.MySQL.asset_pro_revenue` 
WHERE organization_id = $organization_id AND $__timeFilter(timestamp) AND type = 'send'

UNION ALL

SELECT
  'Bridge' as `Feature`,
  IFNULL(SUM(profit), 0) AS `Revenue`,
  IFNULL(SUM(amount), 0) AS `Volume`,
  Count(1) AS `Count`,
  Count(DISTINCT uid) AS `User`  
FROM `kryptogo-wallet-app-staging.MySQL.asset_pro_revenue` 
WHERE organization_id = $organization_id AND $__timeFilter(timestamp) AND type = 'bridge'

ORDER BY (
    CASE 
        WHEN Feature = 'Buy' THEN 1
        WHEN Feature = 'Swap (Gas (TRON))' THEN 2
        WHEN Feature = 'Swap (Defi (EVM))' THEN 3
        WHEN Feature = 'Send (TRON)' THEN 4
        WHEN Feature = 'Send (Gasless(TRON))' THEN 5
        WHEN Feature = 'Bridge' THEN 6
    END
);