# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
```bash
# Set environment variables
source config/local.sh

# Initialize dependencies and database
make setup          # Complete setup (db, redis, firebase, env)
make setup-db       # MySQL database only
make setup-redis    # Redis only  
make setup-firebase # Firebase emulator

# Database migration
make mig-db
```

### Build and Run
```bash
# Build the application
make build

# Run the main API server
make run            # Regular run (port 8030)
make live           # Live reload with gin (port 8029->8030)

# Run auxiliary servers
make run-kms        # Key management service (port 8102)
make run-signing    # Signing service (port 8101)
```

### Testing and Quality
```bash
# Run all tests
make test

# Test coverage
make test-coverage

# Run specific test types
make test-rego      # OPA policy tests

# Code quality checks
make lint           # golangci-lint
make vet           # go vet
make format        # go fmt
make gosec         # Security analysis
make shellcheck    # Shell script linting
make check-vulnerability  # Vulnerability scanning
make misspell      # Spell checking
```

### Database Operations
```bash
# Generate database models from GORM
make gen-db

# Compare database schemas
make diff-db

# Fresh database setup
make fresh-setup-db

# Test database migration
make test-mig
```

### Deployment
```bash
# Deploy to specific environment
make deploy CLUSTER_ENV=dev

# Generate deployment configs
make deploy-config
```

## Code Architecture

### Clean Architecture Layers

1. **Domain Layer** (`/domain/`): Core business logic, entities, and repository interfaces
2. **API Layer** (`/api/`): HTTP handlers using Gin framework
3. **Service Layer** (`/service/`): Business logic orchestration
4. **Repository Layer** (`/pkg/db/model/`, `/pkg/service/rdb/`): Data access with GORM
5. **Chain Layer** (`/chain/`): Blockchain client implementations

### Key Directories

- `api/`: HTTP endpoints organized by feature (payment, user, nft, etc.)
- `domain/`: Domain models and repository interfaces
- `service/`: Business logic services
- `pkg/`: Shared packages and utilities
- `chain/`: Blockchain-specific client implementations (EVM, Solana, Tron, Bitcoin)
- `router/`: API routing and middleware configuration
- `cmd/`: Entry points for different services

### Database Models

Two model packages exist:
- `pkg/db/model/`: MySQL GORM models (generated)
- `pkg/model/`: Firestore models

## Development Practices

### Logging
Use `kglog` package instead of standard logging:
```go
kglog.InfoCtx(ctx, message)
kglog.ErrorWithDataCtx(ctx, message, map[string]interface{}{
    "error": err.Error(), // Always use .Error() method
})
```

### Error Handling
- Repository layer: Return domain errors
- Service layer: Use `code.NewKGError` for HTTP responses
- API layer: Use `response.KGError(c, err)`

### Testing
- Unit tests alongside implementation files
- Integration tests use `rdb.Reset()` and real database
- API tests define separate request/response structs matching OpenAPI spec
- Use gomock for mocking: `//go:generate mockgen -package=domain -destination=mock.go . Interface`

### Service Initialization
Add new services in `router/common.go` InitDependencies function in proper dependency order.

### Common Patterns
- Use dependency injection with interfaces
- Context propagation through all layers  
- Transaction handling for multi-record operations
- OAuth scopes for API authorization
- Blockchain address validation and normalization

## Special Considerations

- Multiple blockchain support (Ethereum, Solana, Tron, Bitcoin)
- ERC-4337 account abstraction implementation
- Payment intent system with multiple providers
- Real-time WebSocket connections
- Firebase/Firestore integration
- Comprehensive monitoring and tracing with OpenTelemetry
- Multi-environment deployment (local, dev, staging, prod)

## Prerequisites

- Go 1.23+
- Docker Desktop
- MySQL 8.0
- Redis
- gcloud SDK (authenticated)
- Firebase permissions