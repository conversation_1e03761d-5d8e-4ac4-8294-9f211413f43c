SELECT 
  COUNT(*) AS total_transactions,

  -- Count specific event types separately
  COUNT(CASE WHEN event_name = 'app_send_transaction' THEN 1 END) AS total_send_transactions,
  COUNT(CASE WHEN event_name = 'app_swap_transaction' THEN 1 END) AS total_one_inch_transactions,
  COUNT(CASE WHEN event_name = 'app_cex_swap' THEN 1 END) AS total_cex_swap_transactions,
  COUNT(CASE WHEN event_name = 'app_stable_coin_supply_transaction' THEN 1 END) AS total_stable_coin_supply_transactions,
  COUNT(CASE WHEN event_name = 'app_aave_supply_transaction' THEN 1 END) AS total_aave_supply_transactions,
  COUNT(CASE WHEN event_name = 'app_dapp_transaction' THEN 1 END) AS total_dapp_transactions,
  COUNT(CASE WHEN event_name = 'app_wc_transaction' THEN 1 END) AS total_wc_transactions,
  COUNT(CASE WHEN event_name = 'app_xyz_swap_transaction' THEN 1 END) AS total_xyz_transactions,
  COUNT(CASE WHEN event_name = 'app_hyperliquid_deposit_click' THEN 1 END) AS total_hyperliquid_transactions,

FROM `kryptogo-wallet-app.analytics_data.transation_events`
WHERE 
  PARSE_DATE('%Y%m%d', event_date) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE) 
                                         AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
  AND app_info.id = @selected_app_id;
