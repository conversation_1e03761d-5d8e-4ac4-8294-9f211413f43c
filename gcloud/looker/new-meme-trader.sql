WITH new_users AS (
  -- Find users who initialized a wallet
  SELECT DISTINCT user_id
  FROM `kryptogo-wallet-app.analytics_291142762.events_*`
  WHERE 
    _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_START_DATE)) 
                      AND FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_END_DATE))
    AND event_name = 'app_init_wallet_group'
    AND app_info.id = @selected_app_id
),

meme_traders AS (
  -- Find users who traded memes (ONLY app_xyz_swap_transaction)
  SELECT DISTINCT user_id
  FROM `kryptogo-wallet-app.analytics_291142762.events_*`
  WHERE 
    _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_START_DATE)) 
                      AND FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_END_DATE))
    AND event_name = 'app_xyz_swap_transaction'
    AND app_info.id = @selected_app_id
)

-- Count how many new users also traded memes
SELECT 
  COUNT(DISTINCT new_users.user_id) AS new_users_who_traded_memes
FROM new_users
JOIN meme_traders 
ON new_users.user_id = meme_traders.user_id;
