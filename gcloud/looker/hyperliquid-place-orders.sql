SELECT 
  PARSE_DATE('%Y%m%d', _TABLE_SUFFIX) AS event_date,
  COUNT(*) AS total_orders,
  SUM(
    SAFE_CAST(
      (SELECT ep.value.string_value 
       FROM UNNEST(event_params) ep 
       WHERE ep.key = 'price') AS FLOAT64
    ) * 
    SAFE_CAST(
      (SELECT ep.value.string_value 
       FROM UNNEST(event_params) ep 
       WHERE ep.key = 'size') AS FLOAT64
    )
  ) AS total_order_value
FROM `kryptogo-wallet-app.analytics_291142762.events_*`
WHERE _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', DATE_SUB(CURRENT_DATE(), INTERVAL 3 MONTH))
                        AND FORMAT_DATE('%Y%m%d', CURRENT_DATE())
  AND event_name = 'app_hyperliquid_place_order'
GROUP BY event_date
ORDER BY event_date ASC;