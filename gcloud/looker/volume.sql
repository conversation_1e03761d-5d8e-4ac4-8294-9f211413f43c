SELECT
  SUM(CASE 
        WHEN event_name = 'app_send_transaction' AND ep.key = 'fromAmountUsd' 
        THEN CAST(ep.value.string_value AS FLOAT64) 
        ELSE 0 
      END) AS total_send_transaction_usd,
  SUM(CASE 
        WHEN event_name = 'app_swap_transaction' AND ep.key = 'amount_usd' 
        THEN CAST(ep.value.string_value AS FLOAT64) 
        ELSE 0 
      END) AS total_swap_transaction_usd,
  SUM(CASE 
        WHEN event_name = 'app_cex_swap' AND ep.key = 'amount_usd' 
        THEN CAST(ep.value.string_value AS FLOAT64) 
        ELSE 0 
      END) AS total_cex_swap_transaction_usd,
  SUM(CASE 
        WHEN event_name = 'app_xyz_swap_transaction' AND ep.key = 'fromAmountUsd' 
        THEN CAST(ep.value.string_value AS FLOAT64) 
        ELSE 0 
      END) AS total_xyz_transaction_usd
FROM
  `kryptogo-wallet-app.analytics_291142762.events_*`,
  UNNEST(event_params) AS ep
WHERE
  _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_START_DATE))
                   AND FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_END_DATE))
  AND event_name IN ('app_send_transaction', 'app_swap_transaction', 'app_cex_swap', 'app_xyz_swap_transaction')
  AND app_info.id = @selected_app_id
