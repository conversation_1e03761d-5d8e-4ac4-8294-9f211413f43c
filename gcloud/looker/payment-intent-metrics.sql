-- payment_intent_metrics.sql
SELECT
  COUNT(DISTINCT org_id)                          AS unique_organizations,
  COUNT(DISTINCT payer_address)                   AS unique_users,
  COUNT(*)                                        AS success_intent_count,
  SUM(
    SAFE_CAST(crypto_amount * crypto_price AS FLOAT64)
  )                                               AS success_intent_volume_usd
FROM
  `kryptogo-wallet-app.MySQL.payment_intents_raw`
WHERE
  status = 'success'
  AND DATE(intent_timestamp)
      BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE)
          AND PARSE_DATE('%Y%m%d', @DS_END_DATE);