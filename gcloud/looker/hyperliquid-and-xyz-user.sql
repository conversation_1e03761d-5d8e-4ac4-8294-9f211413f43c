WITH web_events AS (
  SELECT
    (SELECT ep.value.string_value
     FROM UNNEST(event_params) ep
     WHERE ep.key = 'wallet_address') AS wallet_address
  FROM `kryptogo-wallet-app.analytics_291142762.events_*`
  WHERE 
    _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_START_DATE)) 
                      AND FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_END_DATE))
    AND event_name IN ('app_xyz_buy_success', 'app_xyz_sell_success')
)

SELECT 
  COUNT(DISTINCT CASE WHEN event_name = 'app_xyz_swap_transaction' THEN user_id END) AS distinct_users_app_swap,
  COUNT(DISTINCT CASE WHEN event_name = 'app_hyperliquid_deposit_click' THEN user_id END) AS distinct_users_deposit,
  (SELECT COUNT(DISTINCT wallet_address)
   FROM web_events
   WHERE wallet_address IS NOT NULL AND wallet_address != '') AS distinct_users_web_swap
FROM `kryptogo-wallet-app.analytics_291142762.events_*`
WHERE 
  _TABLE_SUFFIX BETWEEN FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_START_DATE)) 
                    AND FORMAT_DATE('%Y%m%d', PARSE_DATE('%Y%m%d', @DS_END_DATE))
  AND app_info.id = @selected_app_id
