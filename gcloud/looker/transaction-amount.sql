SELECT 
    PARSE_DATE('%Y%m%d', event_date) AS transaction_date,
    app_info.id AS white_label_wallet,
    ep_token.value.string_value AS token_name,
    SUM(CAST(ep_amount.value.string_value AS FLOAT64)) AS total_amount_usd
FROM kryptogo-wallet-app.analytics_data.transation_events,
UNNEST(event_params) AS ep_amount
LEFT JOIN UNNEST(event_params) AS ep_token ON ep_token.key = 'fromToken'
WHERE event_name = 'app_send_transaction'
AND ep_amount.key = 'fromAmountUsd'
AND PARSE_DATE('%Y%m%d', event_date) BETWEEN PARSE_DATE('%Y%m%d', "20250101") 
                                         AND PARSE_DATE('%Y%m%d', "20250407")
GROUP BY transaction_date, white_label_wallet, token_name
ORDER BY transaction_date ASC, white_label_wallet, total_amount_usd DESC;