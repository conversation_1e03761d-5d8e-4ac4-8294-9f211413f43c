SELECT
  type,
  SUM(
    CASE
      WHEN organization_id = 1 THEN SAFE_CAST(profit AS FLOAT64) + SAFE_CAST(kg_profit AS FLOAT64)
      ELSE SAFE_CAST(profit AS FLOAT64)
    END
  ) AS total_profit,
  SUM(
    CASE
      WHEN organization_id = 1 THEN SAFE_CAST(profit AS FLOAT64) + SAFE_CAST(kg_profit AS FLOAT64)
      ELSE SAFE_CAST(kg_profit AS FLOAT64)
    END
  ) AS kg_profit,
  -- Add the sum of the 'amount' field for volume, casting for safety
  SUM(SAFE_CAST(amount AS FLOAT64)) AS volume,
  -- Count of all transactions for this type
  COUNT(*) AS transaction_count,
  -- Count of distinct users for this type
  COUNT(DISTINCT uid) AS unique_users
FROM
  `kryptogo-wallet-app.MySQL.asset_pro_revenue` -- Added backticks
WHERE
  (
    (@selected_app_id = 'com.kryptogo.walletapp' AND organization_id = 1) OR
    ((@selected_app_id = 'com.tag.walletapp' OR @selected_app_id = 'com.tag.walletapp2') AND organization_id = 2) OR
    (@selected_app_id = 'com.tongbao.walletapp' AND organization_id = 16)
  )
  AND DATE(timestamp) BETWEEN PARSE_DATE('%Y%m%d', @DS_START_DATE)
                          AND PARSE_DATE('%Y%m%d', @DS_END_DATE)
GROUP BY
  type -- Grouping remains the same
ORDER BY
  total_profit DESC -- Ordering remains the same