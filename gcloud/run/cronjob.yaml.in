apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: cronjob
spec:
  template:
    spec:
      containers:
        - image: {{ .Env.API_IMAGE }}
          env:
            - name: MODE
              value: CRONJOB
            - name: OTEL_COLLECTOR_SERVICE
              value: cronjob
{{ tpl (file.Read "gcloud/run/env.yaml.in") . | indent 12 }}
          ports:
            - containerPort: 8080
          resources:
            limits:
              cpu: 1000m
              memory: 1024Mi
      serviceAccountName: {{ .Env.SERVICE_ACCOUNT_EMAIL }}
      timeoutSeconds: 3600
    metadata:
      annotations:
        run.googleapis.com/vpc-access-connector: cloudrun-proxy-connector
        run.googleapis.com/vpc-access-egress: all-traffic
        run.googleapis.com/cpu-throttling: "false"  # This ensures CPU is always allocated
        autoscaling.knative.dev/maxScale: "3" # Limits the maximum number of instances to control costs