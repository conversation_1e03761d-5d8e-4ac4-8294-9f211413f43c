- name: ENV
  value: {{ .Env.CLUSTER_ENV }}
- name: GIN_MODE
  value: release
- name: APP_PORT
  value: "8080"
- name: PROJECT_ID
  value: {{ .Env.PROJECT_ID }}
- name: CORS_ALLOW_ORIGINS
  value: '{{ .Env.CORS_ALLOW_ORIGINS }}'
- name: REGION
  value: {{ .Env.REGION }}
- name: MYSQL_HOST
  value: {{ .Env.MYSQL_HOST }}
- name: MYSQL_PORT
  value: "3306"
- name: MYSQL_USERNAME
  value: {{ .Env.MYSQL_USERNAME }}
- name: MYSQL_PASSWORD
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: MYSQL_PASSWORD
- name: MYSQL_OPTIONS
  value: {{ .Env.MYSQL_OPTIONS }}
- name: MYSQL_DATABASE
  value: {{ .Env.MYSQL_DATABASE }}
- name: MY<PERSON>QL_SLOW_THRESHOLD
  value: "{{ .Env.MYSQL_SLOW_THRESHOLD }}"
- name: MYSQL_DSN
  value: "{{ .Env.MYSQL_DSN }}"
- name: DEBUG_DATABASE
  value: "{{ .Env.DEBUG_DATABASE }}"
- name: REDIS_HOST
  value: {{ .Env.REDIS_HOST }}
- name: REDIS_PORT
  value: "{{ .Env.REDIS_PORT }}"
- name: REDIS_AUTH
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: REDIS_AUTH
- name: CDN_HOST
  value: {{ .Env.CDN_HOST }}
- name: GS_BUCKET_NAME
  value: {{ .Env.GS_BUCKET_NAME }}
- name: FIREBASE_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: FIREBASE_API_KEY
- name: JWT_SECRET_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: JWT_SECRET_KEY
- name: STUDIO_JWT_SECRET_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STUDIO_JWT_SECRET_KEY
- name: ACCESS_TOKEN_EXP_MINUTES
  value: "{{ .Env.ACCESS_TOKEN_EXP_MINUTES }}"
- name: OAUTH_CLIENTS
  value: '{{ .Env.OAUTH_CLIENTS }}'
- name: CLEF_HOST
  value: {{ .Env.CLEF_HOST }}
- name: CLEF_PORT_MATIC
  value: "{{ .Env.CLEF_PORT_MATIC }}"
- name: CLEF_PORT_POLYGON
  value: "{{ .Env.CLEF_PORT_POLYGON }}"
- name: CLEF_PORT_GOERLI
  value: "{{ .Env.CLEF_PORT_GOERLI }}"
- name: CLEF_PORT_MUMBAI
  value: "{{ .Env.CLEF_PORT_MUMBAI }}"
- name: TWILIO_ACCOUNT_SID
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TWILIO_SID
- name: TWILIO_AUTH_TOKEN
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TWILIO_AUTH_TOKEN
- name: TWILIO_SENDER
  value: "{{ .Env.TWILIO_SENDER }}"
- name: ENABLE_TWILIO_VERIFY
  value: "{{ .Env.ENABLE_TWILIO_VERIFY }}"
- name: TWM_USERNAME
  value: "{{ .Env.TWM_USERNAME }}"
- name: TWM_SRC_ADDR
  value: "{{ .Env.TWM_SRC_ADDR }}"
- name: TWM_PASSWORD
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TWM_PASSWORD
- name: SMS_TESTERS
  value: '{{ .Env.SMS_TESTERS }}'
- name: SMS_QA
  value: '{{ .Env.SMS_QA }}'
- name: DYNAMIC_LINK_HOST
  value: {{ .Env.DYNAMIC_LINK_HOST }}
- name: COMPLIANCE_HOST
  value: {{ .Env.COMPLIANCE_HOST }}
- name: SELF_HOST
  value: {{ .Env.SELF_HOST }}
- name: ALCHEMY_WEBHOOK_ID_MAP
  value: '{{ .Env.ALCHEMY_WEBHOOK_ID_MAP }}'
- name: ALCHEMY_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: ALCHEMY_API_KEY
- name: STICKEY_ALCHEMY_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STICKEY_ALCHEMY_API_KEY
- name: ALCHEMY_TOKEN
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: ALCHEMY_TOKEN
- name: ARBISCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: ARBISCAN_API_KEY
- name: STICKEY_ARBISCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STICKEY_ARBISCAN_API_KEY
- name: BINANCE_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: BINANCE_API_KEY
- name: BINANCE_SECRET_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: BINANCE_SECRET_KEY
- name: BLOCKCHAIR_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: BLOCKCHAIR_API_KEY
- name: BSCSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: BSCSCAN_API_KEY
- name: STICKEY_BSCSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STICKEY_BSCSCAN_API_KEY
- name: COVALENT_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: COVALENT_API_KEY
- name: ENCRYPTION_KEY
  valueFrom:
    secretKeyRef:
      # TODO: change to "latest" after GCP fix the bug of latest version not found
      key: "1"
      name: ENCRYPTION_KEY
- name: ETHERSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: ETHERSCAN_API_KEY
- name: STICKEY_ETHERSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STICKEY_ETHERSCAN_API_KEY
- name: INFURA_PROJECT_ID
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: INFURA_PROJECT_ID
- name: STICKEY_INFURA_PROJECT_ID
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STICKEY_INFURA_PROJECT_ID
- name: JUMIO_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: JUMIO_API_KEY
- name: KCC_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: KCC_API_KEY
- name: MORALIS_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: MORALIS_API_KEY
- name: OPENSEA_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: OPENSEA_API_KEY
- name: POLYGONSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: POLYGONSCAN_API_KEY
- name: STICKEY_POLYGONSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STICKEY_POLYGONSCAN_API_KEY
- name: STRIPE_SECRET_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STRIPE_SECRET_KEY
- name: STRIPE_WEBHOOK_SECRET
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: STRIPE_WEBHOOK_SECRET
- name: ZERION_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: ZERION_API_KEY
- name: COMPLIANCE_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: COMPLIANCE_API_KEY
- name: COMPLIANCE_SHARED_SECRET
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: COMPLIANCE_SHARED_SECRET
- name: ZERION_API_KEY_V2
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: ZERION_API_KEY_V2
- name: SOLSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: SOLSCAN_API_KEY
- name: SOLSCAN_API_KEY_V2
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: SOLSCAN_API_KEY_V2
- name: KG_EVM_PRIVATE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: KG_EVM_PRIVATE_KEY
- name: KG_EVM_ADDRESS
  value: "{{ .Env.KG_EVM_ADDRESS }}"
- name: KG_TRON_PRIVATE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: KG_TRON_PRIVATE_KEY
- name: KG_TRON_ADDRESS
  value: "{{ .Env.KG_TRON_ADDRESS }}"
- name: TAG_EVM_PRIVATE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TAG_EVM_PRIVATE_KEY
- name: TAG_EVM_ADDRESS
  value: "{{ .Env.TAG_EVM_ADDRESS }}"
- name: TAG_LOCALHOST_PRIVATE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TAG_LOCALHOST_PRIVATE_KEY
- name: TAG_LOCALHOST_ADDRESS
  value: "{{ .Env.TAG_LOCALHOST_ADDRESS }}"
- name: TAG_TRON_PRIVATE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TAG_TRON_PRIVATE_KEY
- name: TAG_TRON_ADDRESS
  value: "{{ .Env.TAG_TRON_ADDRESS }}"
- name: DD_TASK_SEARCH_SETTING_ID_TW
  value: "{{ .Env.DD_TASK_SEARCH_SETTING_ID_TW }}"
- name: DD_TASK_SEARCH_SETTING_ID_EN
  value: "{{ .Env.DD_TASK_SEARCH_SETTING_ID_EN }}"
- name: DASHBOARD_WHITELIST
  value: {{ .Env.DASHBOARD_WHITELIST }}
- name: WATCHING_NFT_CONTRACT_ADDRESS
  value: {{ .Env.WATCHING_NFT_CONTRACT_ADDRESS }}
- name: AIRDROP_EVENT_SETTING
  value: '{{ .Env.AIRDROP_EVENT_SETTING }}'
- name: AIRDROP_SHEET_INFO
  value: '{{ .Env.AIRDROP_SHEET_INFO }}'
- name: SLACK_WEBHOOK_URL
  value: {{ .Env.SLACK_WEBHOOK_URL }}
- name: ORDER_ENABLED
  value: "{{ .Env.ORDER_ENABLED }}"
- name: STRIPE_ENABLED
  value: "{{ .Env.STRIPE_ENABLED }}"
- name: BINANCE_ENABLED
  value: "{{ .Env.BINANCE_ENABLED }}"
- name: BUY_CRYPTO_URL
  value: {{ .Env.BUY_CRYPTO_URL }}
- name: ORDER_DETAIL_URL
  value: {{ .Env.ORDER_DETAIL_URL }}
- name: SINGLE_ORDER_LOWER_LIMIT
  value: "{{ .Env.SINGLE_ORDER_LOWER_LIMIT }}"
- name: WEEK_ORDER_UPPER_LIMIT
  value: "{{ .Env.WEEK_ORDER_UPPER_LIMIT }}"
- name: LARGE_TRANSACTION_AMOUNT
  value: "{{ .Env.LARGE_TRANSACTION_AMOUNT }}"
- name: OTEL_COLLECTOR_HOST
  value: {{ .Env.OTEL_COLLECTOR_HOST }}
- name: OTEL_COLLECTOR_PORT
  value: "{{ .Env.OTEL_COLLECTOR_PORT }}"
- name: EMAIL_TESTERS
  value: '{{ .Env.EMAIL_TESTERS }}'
- name: EMAIL_QA
  value: '{{ .Env.EMAIL_QA }}'
- name: KG_TOKEN_SECRET
  valueFrom:
    secretKeyRef:
      # TODO: change to "latest" after GCP fix the bug of latest version not found
      key: "1"
      name: KG_TOKEN_SECRET
- name: SENDGRID_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: SENDGRID_API_KEY
- name: DEVELOPER_PHONES
  value: '{{ .Env.DEVELOPER_PHONES }}'
- name: STICKEY_INTERNAL_HOST
  value: '{{ .Env.STICKEY_INTERNAL_HOST }}'
- name: WEB_HOST
  value: '{{ .Env.WEB_HOST }}'
- name: LINE_ACCOUNT_LINK_LOGIN_URL
  value: '{{ .Env.LINE_ACCOUNT_LINK_LOGIN_URL }}'
- name: LINE_BOT_CALLBACK_URL
  value: '{{ .Env.LINE_BOT_CALLBACK_URL }}'
- name: SENDBIRD_KRYPTOGO_API_TOKEN
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: SENDBIRD_KRYPTOGO_API_TOKEN
- name: SENDBIRD_STICKEY_API_TOKEN
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: SENDBIRD_STICKEY_API_TOKEN
- name: SENDBIRD_SESSION_TOKEN_TTL_IN_MINUTES
  value: '{{ .Env.SENDBIRD_SESSION_TOKEN_TTL_IN_MINUTES }}'
- name: SELF_INTERNAL_HOST
  value: '{{ .Env.SELF_INTERNAL_HOST }}'
- name: GOOGLE_OAUTH_CLIENT_ID
  value: '{{ .Env.GOOGLE_OAUTH_CLIENT_ID }}'
- name: GOOGLE_OAUTH_CLIENT_SECRET
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: GOOGLE_OAUTH_CLIENT_SECRET
- name: ACCOUNT_HOST
  value: '{{ .Env.ACCOUNT_HOST }}'
- name: STUDIO_HOST
  value: '{{ .Env.STUDIO_HOST }}'
- name: IOS_RECAPTCHA_SITE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: IOS_RECAPTCHA_SITE_KEY
- name: ANDROID_RECAPTCHA_SITE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: ANDROID_RECAPTCHA_SITE_KEY
- name: WEB_RECAPTCHA_SITE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: WEB_RECAPTCHA_SITE_KEY
- name: POAP_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: POAP_API_KEY
- name: BLOWFISH_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: BLOWFISH_API_KEY
- name: COINGECKO_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: COINGECKO_API_KEY
- name: CRYSTAL_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: CRYSTAL_API_KEY
- name: TRONSCAN_PRO_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TRONSCAN_PRO_API_KEY
- name: FEEE_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: FEEE_API_KEY
- name: PWA_HOST
  value: '{{ .Env.PWA_HOST }}'
- name: TRANSPOSE_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TRANSPOSE_API_KEY
- name: PROXY_SERVERS
  value: '{{ .Env.PROXY_SERVERS }}'
- name: PROXY_USERNAME
  value: '{{ .Env.PROXY_USERNAME }}'
- name: PROXY_PASSWORD
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: PROXY_PASSWORD
- name: KGSTORE_BASE_URL
  value: '{{ .Env.KGSTORE_BASE_URL }}'
- name: GAS_SWAP_CONTRACTS
  value: '{{ .Env.GAS_SWAP_CONTRACTS }}'
- name: GAS_SWAP_FEE_PERCENT
  value: '{{ .Env.GAS_SWAP_FEE_PERCENT }}'
- name: GASLESS_SEND_CONTRACTS
  value: '{{ .Env.GASLESS_SEND_CONTRACTS }}'
- name: EPHEMERAL_NOTES_CONTRACTS
  value: '{{ .Env.EPHEMERAL_NOTES_CONTRACTS }}'
- name: EPHEMERAL_NOTES_FEES
  value: '{{ .Env.EPHEMERAL_NOTES_FEES }}'
- name: SEND_WITH_FEE_CONTRACTS
  value: '{{ .Env.SEND_WITH_FEE_CONTRACTS }}'
- name: CLOUD_TASK_API_QUEUE
  value: '{{ .Env.CLOUD_TASK_API_QUEUE }}'
- name: CLOUD_TASK_TX_WATCH_QUEUE
  value: '{{ .Env.CLOUD_TASK_TX_WATCH_QUEUE }}'
- name: CLOUD_TASK_CHAIN_SYNC_QUEUE
  value: '{{ .Env.CLOUD_TASK_CHAIN_SYNC_QUEUE }}'
- name: TRON_ENERGY_RECHARGE_THRESHOLD
  value: '{{ .Env.TRON_ENERGY_RECHARGE_THRESHOLD }}'
- name: TRON_ENERGY_RECHARGE_AMOUNT
  value: '{{ .Env.TRON_ENERGY_RECHARGE_AMOUNT }}'
- name: BINANCE_PAY_SOURCE
  value: '{{ .Env.BINANCE_PAY_SOURCE }}'
- name: BINANCE_PAY_PRIVATE_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: BINANCE_PAY_PRIVATE_KEY
- name: BINANCE_PAY_REDIRECT_URL
  value: '{{ .Env.BINANCE_PAY_REDIRECT_URL }}'
- name: SEND_LINK_CAMPAIGN_ENABLED
  value: '{{ .Env.SEND_LINK_CAMPAIGN_ENABLED }}'
- name: SERVICE_ACCOUNT_EMAIL
  value: '{{ .Env.SERVICE_ACCOUNT_EMAIL }}'
- name: USER_REPO
  value: '{{ .Env.USER_REPO }}'
- name: ASSET_PRICE_UPDATE_JOB_BLANK_SEC
  value: '{{ .Env.ASSET_PRICE_UPDATE_JOB_BLANK_SEC }}'
- name: ASSET_PRICE_UPDATE_JOB_REGULAR_SEC
  value: '{{ .Env.ASSET_PRICE_UPDATE_JOB_REGULAR_SEC }}'
- name: TRONGRID_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: TRONGRID_API_KEY
- name: QUICKNODE_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: QUICKNODE_API_KEY
- name: QUICKNODE_API_KEY_V2
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: QUICKNODE_API_KEY_V2
- name: SCAM_SNIFFER_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: SCAM_SNIFFER_API_KEY
- name: OPSCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: OPSCAN_API_KEY
- name: BASESCAN_API_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: BASESCAN_API_KEY
- name: PM_EMAILS
  value: '{{ .Env.PM_EMAILS }}'
- name: SMS_WHITELIST
  value: '{{ .Env.SMS_WHITELIST }}'
- name: PAYMASTER_CONTRACTS
  value: '{{ .Env.PAYMASTER_CONTRACTS }}'
- name: ACCOUNT_FACTORY_ADDRESS
  value: '{{ .Env.ACCOUNT_FACTORY_ADDRESS }}'
- name: OKX_ACCESS_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: OKX_ACCESS_KEY
- name: OKX_SECRET_KEY
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: OKX_SECRET_KEY
- name: OKX_PASSPHRASE
  valueFrom:
    secretKeyRef:
      key: "latest"
      name: OKX_PASSPHRASE
- name: XYZ_TRADING_FEE
  value: '{{ .Env.XYZ_TRADING_FEE }}'
- name: XYZ_TRADING_VOLUME_PER_CREDIT
  value: '{{ .Env.XYZ_TRADING_VOLUME_PER_CREDIT }}'
- name: XYZ_CREDIT_SINGLE_PRICE
  value: '{{ .Env.XYZ_CREDIT_SINGLE_PRICE }}'
- name: XYZ_CREDIT_10_PACKAGE_PRICE
  value: '{{ .Env.XYZ_CREDIT_10_PACKAGE_PRICE }}'
- name: XYZ_CREDIT_50_PACKAGE_PRICE
  value: '{{ .Env.XYZ_CREDIT_50_PACKAGE_PRICE }}'