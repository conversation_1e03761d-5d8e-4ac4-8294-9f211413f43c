apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: kms
spec:
  template:
    spec:
      containers:
        - image: {{ .Env.KMS_IMAGE }}
          env:
            - name: MODE
              value: API
            - name: OTEL_COLLECTOR_SERVICE
              value: kms
{{ tpl (file.Read "gcloud/run/env.yaml.in") . | indent 12 }}
          ports:
            - containerPort: 8080
          resources:
            limits:
              cpu: 1000m
              memory: 512Mi
      serviceAccountName: {{ .Env.KMS_SERVICE_ACCOUNT_EMAIL }}
      timeoutSeconds: 3600
    metadata:
      annotations:
        run.googleapis.com/vpc-access-connector: cloudrun-proxy-connector
        run.googleapis.com/vpc-access-egress: all-traffic
        autoscaling.knative.dev/maxScale: "2" # Limits the maximum number of instances to control costs