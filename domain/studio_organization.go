//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=studio_organization_mock.go . StudioOrgRepo
//go:generate go-enum
package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	"github.com/shopspring/decimal"
)

// StudioOrgRepo interface for studio organization
type StudioOrgRepo interface {
	// organization
	CreateOrganization(ctx context.Context, params StudioOrganizationConfig) (int, *code.KGError)
	UpdateOrganization(ctx context.Context, orgID int, params StudioOrganizationConfig) *code.KGError
	SaveOrganization(ctx context.Context, orgID int, req SaveOrganizationRequest) *code.KGError
	GetOrganizationByID(ctx context.Context, orgID int) (*StudioOrganization, *code.KGError)
	GetOrganizationByOAuthClientID(ctx context.Context, clientID string) (*StudioOrganization, *code.KGError)
	GetOrganizationByComplianceOrgID(ctx context.Context, cmpOrgID int) (*StudioOrganization, *code.KGError)
	GetOrganizationsByActiveUser(ctx context.Context, userID string) ([]StudioOrganization, error)
	GetOrganizationsByUser(ctx context.Context, userID string) ([]StudioOrganization, *code.KGError)
	GetOrgEnabledModules(ctx context.Context, orgID int) (*StudioOrganizationModule, *code.KGError)
	SaveOrgEnabledModules(ctx context.Context, orgID int, modules *StudioOrganizationModule) error
	GetOrgWallets(ctx context.Context, orgID int) ([]*StudioOrganizationWallet, error)
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (*OrganizationWallets, *code.KGError)
	GetOrgWallet(ctx context.Context, orgID int, walletType string) (*StudioOrganizationWallet, error)

	// dapp
	CreateDapps(ctx context.Context, orgID int, dapps []StudioOrganizationDapp) *code.KGError
	GetActiveDapps(ctx context.Context, orgID int) ([]StudioOrganizationDapp, *code.KGError)

	// oauth client
	GetOAuthConfig(ctx context.Context, orgID int, applicationType OAuthApplicationType) (*OAuthConfig, *code.KGError)
	GetOAuthConfigByClientID(ctx context.Context, clientID string) (*OAuthConfig, *code.KGError)
	GetOAuthConfigByLineChannelID(ctx context.Context, channelID string) (*OAuthConfig, *code.KGError)
	IsOAuthClientInOrganization(ctx context.Context, orgID int, clientID string) (bool, *code.KGError)
	HasKgUserAuthorizedOAuthClient(ctx context.Context, kgUserID, clientID string) (bool, *code.KGError)

	// Exchange rate
	GetExchangeRates(ctx context.Context, orgID int) ([]*ExchangeRate, *code.KGError)
	UpsertExchangeRate(ctx context.Context, orgID int, rate ExchangeRate) (bool, *code.KGError)
	DeleteExchangeRate(ctx context.Context, orgID int, rateID string) *code.KGError

	// linebot
	GetOrgLinebotConfig(ctx context.Context, orgID int) (*StudioOrgLinebotConfig, *code.KGError)
	GetLinebotConfigByChannelID(ctx context.Context, channelID string) (*StudioOrgLinebotConfig, *code.KGError)
	UpsertOrgLinebotConfig(ctx context.Context, params StudioOrgLinebotConfig) (bool, *code.KGError)

	// user
	CreateStudioAdmin(ctx context.Context, orgID int, UID, adminName, email string) *code.KGError
	GetStudioAdmin(ctx context.Context, orgID int) (*StudioUser, *code.KGError)
	AcceptInvitation(ctx context.Context, orgID int, UID string) *code.KGError
	GetStudioUser(ctx context.Context, orgID int, UID string) (*StudioUser, *code.KGError)
	GetStudioUserByEmail(ctx context.Context, orgID int, email string) (*StudioUser, *code.KGError)
	CreateStudioUser(ctx context.Context, params SaveStudioUserDataParams) *code.KGError
	SaveStudioUserData(ctx context.Context, user SaveStudioUserDataParams) *code.KGError
	GetStudioUsers(ctx context.Context, orgID int, roles []StudioRole, query *paging.Query) ([]*StudioUser, *paging.Resp, *code.KGError)
	DisableStudioUser(ctx context.Context, orgID int, UID string) (*StudioUser, *code.KGError)

	// market
	GetMarkets(ctx context.Context) ([]*StudioMarket, *code.KGError)
	UpsertMarketInfo(ctx context.Context, req UpsertStudioMarketRequest) (bool, *code.KGError)
	SaveMarketInfo(ctx context.Context, req SaveStudioMarketInfoRequest) *code.KGError
	GetMarketByOrg(ctx context.Context, orgID int) (*StudioMarket, *code.KGError)
	GetMarketByMarketCode(ctx context.Context, marketCode string) (*StudioMarket, *code.KGError)

	// Imported Addresses
	CountImportedAddresses(ctx context.Context, orgID int, chain string) (int64, error)
	InsertImportedAddress(ctx context.Context, orgID int, chain string, address string, userID string) error
	GetImportedAddressesByOrgID(ctx context.Context, orgID int) ([]*ImportedAddress, error)
	GetDefaultImportedAddress(ctx context.Context, orgID int, chainID string) (*ImportedAddress, error)
	DeleteImportedAddress(ctx context.Context, orgID int, addressID int) error
	SetDefaultImportedAddress(ctx context.Context, orgID int, addressID int, isDefault bool) error
}

// AssetProApprovalConfig .
// ENUM(
//
//	trader,
//	trader-approver-finance_manager,
//	trader-approver
//
// )
type AssetProApprovalConfig string

// StudioOrganization is a domain model that represents a studio organization
type StudioOrganization struct {
	ID                       int
	Name                     string
	SignAlertThreshold       float64
	Wallets                  []*StudioOrganizationWallet
	AssetProApprovalConfig   *AssetProApprovalConfig
	CreatedAt                time.Time
	ComplianceOrganizationID int
	ComplianceAPIKey         *string
	IconURL                  *string
}

// StudioOrganizationConfig is a struct that represents the params for updating studio organization
type StudioOrganizationConfig struct {
	OrgName                  *string
	SignAlertThreshold       *float64
	ComplianceOrganizationID *int
	ComplianceAPIKey         *string
	IconURL                  *string
	Module                   *StudioOrganizationModule
	AssetProApprovalConfig   *AssetProApprovalConfig
}

// SaveOrganizationRequest is a struct that represents the params for saving studio organization
type SaveOrganizationRequest struct {
	Name    string
	IconURL *string
}

// StudioOrganizationWallet is a domain model that represents a studio organization wallet
type StudioOrganizationWallet struct {
	ID                  int
	OrganizationID      int
	WalletType          string
	WalletAddress       string
	EncryptedPrivateKey string
}

// OAuthConfig is a domain model that represents a oauth config
type OAuthConfig struct {
	ID            string  `json:"id"`
	Domain        string  `json:"domain"`
	IsPrivileged  bool    `json:"is_privileged"`
	Name          string  `json:"name"`
	LineChannelID *string `json:"line_channel_id"`
}

// StudioOrgLinebotConfig is a domain model that represents a studio organization line bot config
type StudioOrgLinebotConfig struct {
	OrganizationID     int
	ChannelID          *string
	ChannelSecret      *string
	ChannelAccessToken *string
	Enabled            *bool
	Status             *string
}

// StudioUser is a domain model that represents a studio user
type StudioUser struct {
	OrganizationID int
	UID            string
	Name           string
	MemberID       *string
	Status         StudioUserStatus
	Email          string
	Roles          []StudioRole
	InvitedAt      time.Time

	TransferLimitation StudioUserTransferLimitation
}

// StudioUserStatus defines the status of a studio user
// ENUM(pending, active, inactive, expired)
type StudioUserStatus string

// SaveStudioUserDataParams is a struct that represents the params for saving studio user data
type SaveStudioUserDataParams struct {
	OrganizationID            int
	UID                       string
	Name                      *string
	MemberID                  *string
	Status                    *StudioUserStatus
	Email                     *string
	InvitedAt                 *time.Time
	DailyTransferLimit        *decimal.Decimal
	TransferApprovalThreshold *decimal.Decimal
}

// ExchangeRate is a domain model that represents an exchange rate
type ExchangeRate struct {
	ID             int
	OrganizationID int
	Base           string
	Quote          string
	ImageURL       *string
	Description    *string
	BuyPrice       *decimal.Decimal
	SellPrice      *decimal.Decimal
}

// StudioOrganizationDapp is a domain model that represents a studio organization dapp
type StudioOrganizationDapp struct {
	Title    string
	Desc     string
	ImageURL string
	SiteURL  string
	IsActive bool
}

// ImportedAddress represents an address imported by a user for an organization,
// distinct from the organization's generated wallets.
type ImportedAddress struct {
	ID                    int       `json:"id"`
	OrganizationID        int       `json:"organization_id"`
	Chain                 string    `json:"chain"`
	Address               string    `json:"address"`
	AddedByUserID         string    `json:"added_by_user_id"`
	AddedAt               time.Time `json:"added_at"`
	DefaultReceiveAddress bool      `json:"default_receive_address"`
}

// StudioOrganizationClient is a domain model that represents a studio organization client
