// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: BridgeRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=bridge_repo_mock.go . BridgeRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockBridgeRepo is a mock of BridgeRepo interface.
type MockBridgeRepo struct {
	ctrl     *gomock.Controller
	recorder *MockBridgeRepoMockRecorder
}

// MockBridgeRepoMockRecorder is the mock recorder for MockBridgeRepo.
type MockBridgeRepoMockRecorder struct {
	mock *MockBridgeRepo
}

// NewMockBridgeRepo creates a new mock instance.
func NewMockBridgeRepo(ctrl *gomock.Controller) *MockBridgeRepo {
	mock := &MockBridgeRepo{ctrl: ctrl}
	mock.recorder = &MockBridgeRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBridgeRepo) EXPECT() *MockBridgeRepoMockRecorder {
	return m.recorder
}

// BatchGetTokenPrices mocks base method.
func (m *MockBridgeRepo) BatchGetTokenPrices(arg0 context.Context, arg1 []ChainToken) (map[ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockBridgeRepoMockRecorder) BatchGetTokenPrices(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockBridgeRepo)(nil).BatchGetTokenPrices), arg0, arg1)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockBridgeRepo) BatchGetTokenPricesIn24H(arg0 context.Context, arg1 []ChainToken) (map[ChainToken][]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken][]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockBridgeRepoMockRecorder) BatchGetTokenPricesIn24H(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockBridgeRepo)(nil).BatchGetTokenPricesIn24H), arg0, arg1)
}

// CreateBridgeRecord mocks base method.
func (m *MockBridgeRepo) CreateBridgeRecord(arg0 context.Context, arg1 *BridgeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeRecord indicates an expected call of CreateBridgeRecord.
func (mr *MockBridgeRepoMockRecorder) CreateBridgeRecord(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeRecord", reflect.TypeOf((*MockBridgeRepo)(nil).CreateBridgeRecord), arg0, arg1)
}

// GetAssetPrice mocks base method.
func (m *MockBridgeRepo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockBridgeRepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockBridgeRepo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetNativeAssetPrice mocks base method.
func (m *MockBridgeRepo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockBridgeRepoMockRecorder) GetNativeAssetPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockBridgeRepo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetProfitRate mocks base method.
func (m *MockBridgeRepo) GetProfitRate(arg0 context.Context, arg1 int, arg2 ProfitRateServiceType) (*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRate indicates an expected call of GetProfitRate.
func (mr *MockBridgeRepoMockRecorder) GetProfitRate(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRate", reflect.TypeOf((*MockBridgeRepo)(nil).GetProfitRate), arg0, arg1, arg2)
}

// GetProfitRates mocks base method.
func (m *MockBridgeRepo) GetProfitRates(arg0 context.Context, arg1 int) ([]*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRates", arg0, arg1)
	ret0, _ := ret[0].([]*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRates indicates an expected call of GetProfitRates.
func (mr *MockBridgeRepoMockRecorder) GetProfitRates(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRates", reflect.TypeOf((*MockBridgeRepo)(nil).GetProfitRates), arg0, arg1)
}

// GetTokenPrice mocks base method.
func (m *MockBridgeRepo) GetTokenPrice(arg0 context.Context, arg1 Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockBridgeRepoMockRecorder) GetTokenPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockBridgeRepo)(nil).GetTokenPrice), arg0, arg1, arg2)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockBridgeRepo) GetTokenPricesIn24H(arg0 context.Context, arg1 Chain, arg2 string) ([]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockBridgeRepoMockRecorder) GetTokenPricesIn24H(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockBridgeRepo)(nil).GetTokenPricesIn24H), arg0, arg1, arg2)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockBridgeRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockBridgeRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockBridgeRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// UpdateBridgeRecord mocks base method.
func (m *MockBridgeRepo) UpdateBridgeRecord(arg0 context.Context, arg1 string, arg2 *BridgeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBridgeRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBridgeRecord indicates an expected call of UpdateBridgeRecord.
func (mr *MockBridgeRepoMockRecorder) UpdateBridgeRecord(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBridgeRecord", reflect.TypeOf((*MockBridgeRepo)(nil).UpdateBridgeRecord), arg0, arg1, arg2)
}

// UpsertProfitRate mocks base method.
func (m *MockBridgeRepo) UpsertProfitRate(arg0 context.Context, arg1 UpsertProfitRateParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertProfitRate", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpsertProfitRate indicates an expected call of UpsertProfitRate.
func (mr *MockBridgeRepoMockRecorder) UpsertProfitRate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertProfitRate", reflect.TypeOf((*MockBridgeRepo)(nil).UpsertProfitRate), arg0, arg1)
}
