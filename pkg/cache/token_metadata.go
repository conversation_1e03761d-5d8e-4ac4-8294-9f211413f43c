package cache

import (
	"context"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

const IsSpamTokenKey = "is_spam_token:%s:%s"

// composeIsSpamTokenKey compose is spam token cache key
func composeIsSpamTokenKey(chainID string, contractAddress string) string {
	return fmt.Sprintf(IsSpamTokenKey, chainID, contractAddress)
}

// IsTokenSpam check if a token is spam. Returns ErrRecordNotFound if the token is not cached.
func (r *Repo) IsTokenSpam(ctx context.Context, chain domain.Chain, tokenID string) (bool, error) {
	// if already cached in repo, return directly
	cached, err := Bool(composeIsSpamTokenKey(chain.ID(), tokenID))
	if err != nil {
		kglog.ErrorfCtx(ctx, "get is spam token failed: %v", err)
		return false, err
	}
	if cached == nil {
		return false, domain.ErrRecordNotFound
	}
	return *cached, nil
}

// SetTokenSpam set a token as spam or not
func (r *Repo) SetTokenSpam(ctx context.Context, chain domain.Chain, tokenID string, isSpam bool) error {
	kglog.DebugfCtx(ctx, "set token is spam: %v, %s, %v", chain, tokenID, isSpam)
	err := Set(ctx, composeIsSpamTokenKey(chain.ID(), tokenID), isSpam, 0)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "set is spam token failed", map[string]interface{}{
			"chain":   chain.ID(),
			"token":   tokenID,
			"is_spam": isSpam,
		})
		return err
	}
	return nil
}
