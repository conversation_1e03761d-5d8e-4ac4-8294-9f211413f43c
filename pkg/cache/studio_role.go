package cache

import (
	"bytes"
	"context"
	"encoding/gob"
	"fmt"
	"net/http"

	"github.com/go-redis/redis/v8"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/pkg/errors"
)

var studioRoleCacheRepo *StudioRoleCacheRepo

// GetStudioRoleCacheRepo implements domain.StudioOrgRepo
func GetStudioRoleCacheRepo() domain.StudioRoleCacheRepo {
	return studioRoleCacheRepo
}

// NewRedisStudioRoleCacheRepo returns a new instance of StudioOrgRepo
func NewRedisStudioRoleCacheRepo(client *redis.Client) *StudioRoleCacheRepo {
	return &StudioRoleCacheRepo{client: client}
}

// StudioRoleCacheRepo implements domain.StudioRoleCacheRepo
type StudioRoleCacheRepo struct {
	client *redis.Client
}

// GetRolesByUser returns the roles of the user in the organization.
func (o *StudioRoleCacheRepo) GetRolesByUser(ctx context.Context,
	orgID int, userID string) ([]domain.StudioRole, *code.KGError) {
	if o.client == nil {
		panic("redis client is nil")
	}

	key := o.studioRoleKey(orgID, userID)

	bs, err := o.client.HGet(ctx, o.keyStudioRole(), key).Result()
	if err != nil {
		// special case: user not found
		if errors.Is(err, redis.Nil) {
			return []domain.StudioRole{}, nil
		}

		return nil, code.NewKGError(code.RedisError, http.StatusInternalServerError,
			errors.Wrapf(err, "redis HGet(%s) field(%s) failed", o.keyStudioRole(), key), nil)
	}

	var result []domain.StudioRole
	buffer := bytes.NewBufferString(bs)
	decoder := gob.NewDecoder(buffer)
	err = decoder.Decode(&result)
	if err != nil {
		return nil, code.NewKGError(code.GOBError, http.StatusInternalServerError,
			errors.Wrap(err, "gob decode failed"), nil)
	}

	return result, nil
}

// SetRoles sets all of the studio roles.
func (o *StudioRoleCacheRepo) SetRoles(ctx context.Context,
	roleBindings []domain.StudioRoleBinding) *code.KGError {
	if o.client == nil {
		panic("redis client is nil")
	}

	mUserRoles := make(map[string][]domain.StudioRole)
	for _, roleBinding := range roleBindings {
		key := o.studioRoleKey(roleBinding.OrganizationID, roleBinding.UID)
		mUserRoles[key] = append(mUserRoles[key], roleBinding.StudioRole)
	}

	mBytesUserRoles := make(map[string]any)
	for studioRoleKey, roles := range mUserRoles {
		buffer := &bytes.Buffer{}
		encoder := gob.NewEncoder(buffer)
		if err := encoder.Encode(roles); err != nil {
			return code.NewKGError(code.GOBError, http.StatusInternalServerError,
				errors.Wrap(err, "gob encode failed"), nil)
		}
		mBytesUserRoles[studioRoleKey] = buffer.Bytes()
	}

	if len(mBytesUserRoles) == 0 {
		return nil
	}

	if err := o.client.HSet(ctx, o.keyStudioRole(), mBytesUserRoles).Err(); err != nil {
		return code.NewKGError(code.RedisError, http.StatusInternalServerError,
			errors.Wrapf(err, "redis HSet(%s) failed", o.keyStudioRole()), nil)
	}

	return nil
}

// SetUserRoles sets bindings between user and studio role.
func (o *StudioRoleCacheRepo) SetUserRoles(ctx context.Context,
	orgID int, uid string, roles []domain.StudioRole) *code.KGError {
	if o.client == nil {
		panic("redis client is nil")
	}

	key := o.studioRoleKey(orgID, uid)

	buffer := &bytes.Buffer{}
	encoder := gob.NewEncoder(buffer)
	if err := encoder.Encode(roles); err != nil {
		return code.NewKGError(code.GOBError, http.StatusInternalServerError,
			errors.Wrap(err, "gob encode failed"), nil)
	}

	if err := o.client.HSet(ctx, o.keyStudioRole(), key, buffer.Bytes()).Err(); err != nil {
		return code.NewKGError(code.RedisError, http.StatusInternalServerError,
			errors.Wrapf(err, "redis HSet(%s) failed", o.keyStudioRole()), nil)
	}

	return nil
}

func (o *StudioRoleCacheRepo) keyStudioRole() string {
	return "studio:role"
}

func (o *StudioRoleCacheRepo) studioRoleKey(orgID int, uid string) string {
	return fmt.Sprintf("org(%d)-user(%s)", orgID, uid)
}
