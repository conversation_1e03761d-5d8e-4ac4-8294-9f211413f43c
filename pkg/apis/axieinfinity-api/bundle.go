package axieinfinityapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

type bundleVariables struct {
	From     int      `json:"from"`
	Size     int      `json:"size"`
	Sort     string   `json:"sort"`
	Criteria struct{} `json:"criteria"`
}

// GetBundleListReq .
type GetBundleListReq struct {
	OperationName string          `json:"operationName"`
	Variables     bundleVariables `json:"variables"`
	Query         string          `json:"query"`
}

// GetBundleListResp .
type GetBundleListResp struct {
	Data struct {
		Bundles struct {
			Total   int `json:"total"`
			Results []struct {
				Name         string `json:"name"`
				ListingIndex string `json:"listingIndex"`
				Order        struct {
					ID              int    `json:"id"`
					CurrentPrice    string `json:"currentPrice"`
					CurrentPriceUSD string `json:"currentPriceUsd"`
					TypeName        string `json:"__typename"`
				} `json:"order"`
				Items []struct {
					TokenID   string `json:"tokenId"`
					FigureURL string `json:"figureURL"`
					Rarity    string `json:"rarity"`
					ItemAlias string `json:"itemAlias"`
					TypeName  string `json:"__typename"`
				} `json:"items"`
				TypeName string `json:"__typename"`
			} `json:"results"`
			TypeName string `json:"__typename"`
		} `json:"bundles"`
	} `json:"data"`
}

// GetBundleList .
func (a *axieInfinityAPI) GetBundleList(ctx context.Context) (*GetBundleListResp, error) {
	respData := GetBundleListResp{}
	q := `
	query GetBundleList(
		$from: Int!, 
		$size: Int!, 
		$sort: SortBy, 
		$seller: String, 
		$criteria: BundleSearchCriteria
	) {
		bundles(
			from: $from
			size: $size
			criteria: $criteria
			sort: $sort
			seller: $seller
		) {
			total
			results {
				name
				listingIndex
				order {
					id
					currentPrice
					currentPriceUsd
					__typename
				}
				items {
					... on LandItem {
						tokenId
						figureURL
						rarity
						itemAlias
						__typename
					}
					... on LandPlot {
						tokenId
						landType
						__typename
					}
					__typename
				}
				__typename
			}
		__typename
		}
	}
	`
	req := GetBundleListReq{
		OperationName: "GetBundleList",
		Variables: bundleVariables{
			From: 0,
			Size: 100,
			Sort: "PriceAsc",
		},
		Query: q,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetBundleList")).
		SetHeader("Content-Type", "application/json").
		SetBody(&req).
		SetResult(&respData).
		Post("")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetBundleList error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetBundleList error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}
