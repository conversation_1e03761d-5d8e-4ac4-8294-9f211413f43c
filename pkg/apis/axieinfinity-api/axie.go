package axieinfinityapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type axieVariables struct {
	From        int      `json:"from"`
	Size        int      `json:"size"`
	Sort        string   `json:"sort"`
	AuctionType string   `json:"auctionType"`
	Criteria    struct{} `json:"criteria"`
	Owner       string   `json:"owner"`
}

type getAxieBriefListReq struct {
	OperationName string        `json:"operationName"`
	Variables     axieVariables `json:"variables"`
	Query         string        `json:"query"`
}

// GetAxieBriefListResp .
type GetAxieBriefListResp struct {
	Data struct {
		Axies struct {
			Total   int `json:"total"`
			Results []struct {
				ID         string `json:"id"`
				Name       string `json:"name"`
				Stage      int    `json:"stage"`
				Class      string `json:"class"`
				BreedCount int    `json:"breedCount"`
				Image      string `json:"image"`
				Title      string `json:"title"`
				Genes      string `json:"genes"`
				NewGenes   string `json:"newGenes"`
				BattleInfo struct {
					Banned   bool   `json:"banned"`
					TypeName string `json:"__typename"`
				} `json:"battleInfo"`
				Order struct {
					ID              int    `json:"id"`
					CurrentPrice    string `json:"currentPrice"`
					CurrentPriceUSD string `json:"currentPriceUsd"`
					Typename        string `json:"__typename"`
				} `json:"order"`
				Parts []struct {
					ID           string `json:"id"`
					Name         string `json:"name"`
					Class        string `json:"class"`
					Type         string `json:"type"`
					SpecialGenes string `json:"specialGenes"`
					TypeName     string `json:"__typename"`
				} `json:"parts"`
				TypeName string `json:"__typename"`
			} `json:"results"`
			TypeName string `json:"__typename"`
		} `json:"axies"`
	} `json:"data"`
}

// GetAxieBriefList .
func (a *axieInfinityAPI) GetAxieBriefList(ctx context.Context, address string, from int) (*GetAxieBriefListResp, error) {
	ctx, span := tracing.Start(ctx, "axie.GetAxieBriefList")
	defer span.End()

	respData := GetAxieBriefListResp{}
	q := `
	query GetAxieBriefList(
		$auctionType: AuctionType,
		$criteria: AxieSearchCriteria,
		$from: Int,
		$sort: SortBy,
		$size: Int,
		$owner: String
	) {
		axies(
			auctionType: $auctionType,
			criteria: $criteria, 
			from: $from, 
			sort: $sort, 
			size: $size, 
			owner: $owner
		) {
			total
			results {
				...AxieBrief
				__typename
			}
			__typename
		}
	}
	
	fragment AxieBrief on Axie {
		id
		name
		stage
		class
		breedCount
		image
		title
		battleInfo {
			banned
			__typename
		}
		parts {
			id
			name
			class
			type
			specialGenes
			__typename
		}
		__typename
	}
	`
	req := getAxieBriefListReq{
		OperationName: "GetAxieBriefList",
		Variables: axieVariables{
			From:        from,
			Size:        100,
			Sort:        "PriceAsc",
			AuctionType: "All",
			Owner:       address,
		},
		Query: q,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetAxieBriefList")).
		SetHeader("Content-Type", "application/json").
		SetBody(&req).
		SetResult(&respData).
		Post("")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetAxieBriefList error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetAxieBriefList error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}

type axieDetailVariables struct {
	AxieID string `json:"axieId"`
}

// GetAxieDetailReq .
type GetAxieDetailReq struct {
	OperationName string              `json:"operationName"`
	Variables     axieDetailVariables `json:"variables"`
	Query         string              `json:"query"`
}

// GetAxieDetailResp .
type GetAxieDetailResp struct {
	Data struct {
		Axie struct {
			ID          string `json:"id"`
			Image       string `json:"image"`
			Class       string `json:"class"`
			Chain       string `json:"chain"`
			Name        string `json:"name"`
			Genes       string `json:"genes"`
			NewGenes    string `json:"newGenes"`
			Owner       string `json:"owner"`
			BirthDate   int    `json:"birthDate"`
			BodyShape   string `json:"bodyShape"`
			SireID      int    `json:"sireId"`
			SireClass   string `json:"sireClass"`
			MatronID    int    `json:"matronId"`
			MatronClass string `json:"matronClass"`
			Stage       int    `json:"stage"`
			Title       string `json:"title"`
			BreedCount  int    `json:"breedCount"`
			Level       int    `json:"level"`
			Figure      struct {
				Atlas    string `json:"atlas"`
				Model    string `json:"model"`
				Image    string `json:"image"`
				TypeName string `json:"__typename"`
			} `json:"figure"`
			Parts []struct {
				ID           string `json:"id"`
				Name         string `json:"name"`
				Class        string `json:"class"`
				Type         string `json:"type"`
				SpecialGenes string `json:"specialGenes"`
				Stage        int    `json:"stage"`
				Abilities    []struct {
					ID            int    `json:"id"`
					Name          string `json:"name"`
					Attack        int    `json:"attack"`
					Defense       int    `json:"defense"`
					Energy        int    `json:"energy"`
					Description   string `json:"description"`
					BackgroundURL string `json:"backgroundUrl"`
					EffectIconURL string `json:"effectIconUrl"`
					TypeName      string `json:"__typename"`
				} `json:"abilities"`
				TypeName string `json:"__typename"`
			} `json:"parts"`
			Stats struct {
				HP       int    `json:"hp"`
				Speed    int    `json:"speed"`
				Skill    int    `json:"skill"`
				Morale   int    `json:"morale"`
				TypeName string `json:"__typename"`
			} `json:"stats"`
			Order        interface{} `json:"order"`
			OwnerProfile struct {
				Name     string `json:"name"`
				TypeName string `json:"__typename"`
			} `json:"ownerProfile"`
			BattleInfo struct {
				Banned   bool   `json:"banned"`
				BanUntil int    `json:"banUntil"`
				Level    int    `json:"level"`
				TypeName string `json:"__typename"`
			} `json:"battleInfo"`
			Children []struct {
				ID       string `json:"id"`
				Name     string `json:"name"`
				Class    string `json:"class"`
				Image    string `json:"image"`
				Title    string `json:"title"`
				Stage    int    `json:"stage"`
				TypeName string `json:"__typename"`
			} `json:"children"`
			PotentialPoints struct {
				Beast    int    `json:"beast"`
				Aquatic  int    `json:"aquatic"`
				Plant    int    `json:"plant"`
				Bug      int    `json:"bug"`
				Bird     int    `json:"bird"`
				Reptile  int    `json:"reptile"`
				Mech     int    `json:"mech"`
				Dawn     int    `json:"dawn"`
				Dusk     int    `json:"dusk"`
				TypeName string `json:"__typename"`
			} `json:"potentialPoints"`
			EquipmentInstances []interface{} `json:"equipmentInstances"`
			TypeName           string        `json:"__typename"`
		} `json:"axie"`
	} `json:"data"`
}

// GetAxieDetail .
func (a *axieInfinityAPI) GetAxieDetail(ctx context.Context, axieID string) (*GetAxieDetailResp, error) {
	var respData GetAxieDetailResp
	q := `
	query GetAxieDetail(
		$axieId: ID!
	) {
		axie(
			axieId: $axieId
		) { 
			...AxieDetail __typename 
		} 
	} 
	
	fragment AxieDetail on Axie {
		id 
		image 
		class 
		chain 
		name 
		genes 
		newGenes 
		owner 
		birthDate 
		bodyShape 
		class 
		sireId 
		sireClass 
		matronId 
		matronClass 
		stage 
		title 
		breedCount 
		level 
		figure { 
			atlas 
			model 
			image 
			__typename 
		} 
		parts { 
			...AxiePart 
			__typename 
		} 
		stats { 
			...AxieStats 
			__typename 
		} 
		order { 
			...OrderInfo 
			__typename 
		} 
		ownerProfile {
			name 
			__typename 
		} 
		battleInfo { 
			...AxieBattleInfo 
			__typename 
		} 
		children { 
			id 
			name 
			class 
			image 
			title 
			stage 
			__typename 
		} 
		potentialPoints { 
			beast 
			aquatic 
			plant 
			bug 
			bird 
			reptile 
			mech 
			dawn 
			dusk 
			__typename 
		} 
		equipmentInstances { 
			...EquipmentInstance 
			__typename 
		} 
		__typename 
	} 
	
	fragment AxieBattleInfo on AxieBattleInfo { 
		banned 
		banUntil 
		level 
		__typename 
	} 
	
	fragment AxiePart on AxiePart { 
		id 
		name 
		class 
		type 
		specialGenes 
		stage 
		abilities { 
			...AxieCardAbility 
			__typename 
		} 
		__typename 
	} 
	
	fragment AxieCardAbility on AxieCardAbility { 
		id 
		name 
		attack 
		defense 
		energy 
		description 
		backgroundUrl 
		effectIconUrl __typename 
	} 
	
	fragment AxieStats on AxieStats { 
		hp 
		speed 
		skill 
		morale 
		__typename 
	} 
	
	fragment OrderInfo on Order { 
		id 
		maker 
		kind 
		assets { 
			...AssetInfo 
			__typename 
		} 
		expiredAt 
		paymentToken 
		startedAt 
		basePrice 
		endedAt 
		endedPrice 
		expectedState 
		nonce 
		marketFeePercentage 
		signature 
		hash 
		duration 
		timeLeft 
		currentPrice 
		suggestedPrice 
		currentPriceUsd 
		__typename 
	} 
	
	fragment AssetInfo on Asset { 
		erc 
		address 
		id 
		quantity 
		orderId 
		__typename 
	} 
	
	fragment EquipmentInstance on EquipmentInstance { 
		id: tokenId 
		tokenId 
		owner 
		equipmentId 
		alias 
		equipmentType 
		slot 
		name 
		rarity 
		collections 
		equippedBy 
		__typename 
	}
	`

	req := GetAxieDetailReq{
		OperationName: "GetAxieDetail",
		Variables: axieDetailVariables{
			AxieID: axieID,
		},
		Query: q,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetAxieDetail")).
		SetHeader("Content-Type", "application/json").
		SetBody(&req).
		SetResult(&respData).
		Post("")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetAxieDetail error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetAxieDetail error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}
