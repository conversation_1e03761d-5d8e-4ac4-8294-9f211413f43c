package axieinfinityapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type landVariables struct {
	From          int         `json:"from"`
	Size          int         `json:"size"`
	Sort          string      `json:"sort"`
	AuctionType   string      `json:"auctionType"`
	Address       string      `json:"address"`
	OwnershipType string      `json:"ownershipType"`
	Criteria      interface{} `json:"criteria"`
}

// GetLandsGridReq request
type GetLandsGridReq struct {
	OperationName string        `json:"operationName"`
	Variables     landVariables `json:"variables"`
	Query         string        `json:"query"`
}

// GetLandsGridResp response
type GetLandsGridResp struct {
	Data struct {
		Lands struct {
			Total   int `json:"total"`
			Results []struct {
				TokenID  string `json:"tokenId"`
				Owner    string `json:"owner"`
				LandType string `json:"landType"`
				Row      int    `json:"row"`
				Col      int    `json:"col"`
				Order    struct {
					ID              int    `json:"id"`
					CurrentPrice    string `json:"currentPrice"`
					StartedAt       int    `json:"startedAt"`
					CurrentPriceUSD string `json:"currentPriceUsd"`
					TypeName        string `json:"__typename"`
				} `json:"order"`
				OwnerProfile struct {
					Name     string `json:"name"`
					TypeName string `json:"__typename"`
				} `json:"ownerProfile"`
				TypeName string `json:"__typename"`
			} `json:"results"`
			TypeName string `json:"__typename"`
		} `json:"lands"`
	} `json:"data"`
}

// GetLandsGrid returns lands grid
func (a *axieInfinityAPI) GetLandsGrid(ctx context.Context, address string, from int) (*GetLandsGridResp, error) {
	ctx, span := tracing.Start(ctx, "land.GetLandsGrid")
	defer span.End()

	respData := GetLandsGridResp{}
	q := `
	query GetLandsGrid(
		$from: Int!
		$size: Int!
		$sort: SortBy!
		$address: String!
		$ownershipType: OwnershipType!
		$criteria: LandSearchCriteria
		$auctionType: AuctionType
	) {
		lands(
			criteria: $criteria
			from: $from
			size: $size
			sort: $sort
			owner: { address: $address, ownerships: [$ownershipType] }
			auctionType: $auctionType
		) {
			total
			results {
				...LandBriefV2
				__typename
			}
			__typename
		}
	}
	
	fragment LandBriefV2 on LandPlot {
		tokenId
		owner
		landType
		row
		col
		order {
			id
			currentPrice
			startedAt
			currentPriceUsd
			__typename
		}
		ownerProfile {
			name
			__typename
		}
		__typename
	}	
	`
	req := GetLandsGridReq{
		OperationName: "GetLandsGrid",
		Variables: landVariables{
			From:          from,
			Size:          100,
			Sort:          "PriceAsc",
			AuctionType:   "All",
			Address:       address,
			OwnershipType: "Owned",
		},
		Query: q,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetLandsGrid")).
		SetHeader("Content-Type", "application/json").
		SetBody(&req).
		SetResult(&respData).
		Post("")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetLandsGrid error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetLandsGrid error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}
