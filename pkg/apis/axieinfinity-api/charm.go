package axieinfinityapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// GetCharmsResp .
type GetCharmsResp struct {
	Etag  string `json:"_etag"`
	Items []struct {
		Class          string   `json:"class"`
		PotentialPoint int      `json:"potentialPoint"`
		Code           string   `json:"code"`
		Craftable      bool     `json:"craftable"`
		Weight         int      `json:"weight"`
		Tags           []string `json:"tags"`
		Energy         int      `json:"energy"`
		HP             int      `json:"hp"`
		Damage         int      `json:"damage"`
		Shield         int      `json:"shield"`
		Heal           int      `json:"heal"`
		HPPct          int      `json:"hpPct"`
		DamagePct      int      `json:"damagePct"`
		ShieldPct      int      `json:"shieldPct"`
		HealPct        int      `json:"healPct"`
		Item           struct {
			ID            string `json:"id"`
			DisplayOrder  int    `json:"displayOrder"`
			Category      string `json:"category"`
			Rarity        string `json:"rarity"`
			Description   string `json:"description"`
			Name          string `json:"name"`
			TokenStandard string `json:"tokenStandard"`
			TokenAddress  string `json:"tokenAddress"`
			TokenID       string `json:"tokenId"`
			ImageURL      string `json:"imageUrl"`
		} `json:"item"`
		Season struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
		} `json:"season"`
		Etag string `json:"_etag"`
	} `json:"items"`
	Metadata interface{} `json:"_metadata"`
}

// GetCharms .
func (a *axieInfinityAPI) GetCharms(ctx context.Context) (*GetCharmsResp, error) {
	respData := GetCharmsResp{}
	resp, err := a.skyMavisClient.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetCharms")).
		SetResult(&respData).
		Get(charmsURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetCharms error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetCharms error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}
