package axieinfinityapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type itemVariables struct {
	From        int         `json:"from"`
	Size        int         `json:"size"`
	Sort        string      `json:"sort"`
	Owner       string      `json:"owner"`
	AuctionType string      `json:"auctionType"`
	Criteria    interface{} `json:"criteria"`
	Rarity      []string    `json:"rarity"`
	ItemAlias   []string    `json:"itemAlias"`
}

// GetItemBriefListReq .
type GetItemBriefListReq struct {
	OperationName string        `json:"operationName"`
	Variables     itemVariables `json:"variables"`
	Query         string        `json:"query"`
}

// GetItemBriefListResp .
type GetItemBriefListResp struct {
	Data struct {
		Items struct {
			Total   int `json:"total"`
			Results []struct {
				ItemID    int    `json:"itemId"`
				TokenType int    `json:"tokenType"`
				TokenID   string `json:"tokenId"`
				LandType  string `json:"landType"`
				Name      string `json:"name"`
				ItemAlias string `json:"itemAlias"`
				Rarity    string `json:"rarity"`
				FigureURL string `json:"figureURL"`
				Order     struct {
					ID     int    `json:"id"`
					Maker  string `json:"maker"`
					Kind   string `json:"kind"`
					Assets []struct {
						Erc      string `json:"erc"`
						Address  string `json:"address"`
						ID       string `json:"id"`
						Quantity string `json:"quantity"`
						OrderID  int    `json:"orderId"`
						TypeName string `json:"__typename"`
					} `json:"assets"`
					ExpiredAt           int    `json:"expiredAt"`
					PaymentToken        string `json:"paymentToken"`
					StartedAt           int    `json:"startedAt"`
					BasePrice           string `json:"basePrice"`
					EndedAt             int    `json:"endedAt"`
					EndedPrice          string `json:"endedPrice"`
					ExpectedState       string `json:"expectedState"`
					Nonce               int    `json:"nonce"`
					MarketFeePercentage int    `json:"marketFeePercentage"`
					Signature           string `json:"signature"`
					Hash                string `json:"hash"`
					Duration            int    `json:"duration"`
					TimeLeft            int    `json:"timeLeft"`
					CurrentPrice        string `json:"currentPrice"`
					SuggestedPrice      string `json:"suggestedPrice"`
					CurrentPriceUSD     string `json:"currentPriceUSD"`
					TypeName            string `json:"__typename"`
				} `json:"order"`
				TypeName string `json:"__typename"`
			} `json:"results"`
			TypeName string `json:"__typename"`
		} `json:"items"`
	} `json:"data"`
}

// GetItemBriefList .
func (a *axieInfinityAPI) GetItemBriefList(ctx context.Context, address string, from int) (*GetItemBriefListResp, error) {
	ctx, span := tracing.Start(ctx, "item.GetItemBriefList")
	defer span.End()

	respData := GetItemBriefListResp{}
	q := `
	query GetItemBriefList(
		$from: Int,
		$size: Int,
		$sort: SortBy,
		$auctionType: AuctionType,
		$owner: String,
		$criteria: ItemSearchCriteria
	) {
		items(
			from: $from
			size: $size
			sort: $sort
			auctionType: $auctionType
			owner: $owner
			criteria: $criteria
		) {
			total
			results {
				...ItemBrief
				__typename
			}
			__typename
		}
	}
	
	fragment ItemBrief on LandItem {
		itemId
		tokenType
		tokenId
		itemId
		landType
		name
		itemAlias
		rarity
		figureURL
		order {
			...OrderInfo
			__typename
		}
		__typename
	}
	
	fragment OrderInfo on Order {
		id
		maker
		kind
		assets {
			...AssetInfo
			__typename
		}
		expiredAt
		paymentToken
		startedAt
		basePrice
		endedAt
		endedPrice
		expectedState
		nonce
		marketFeePercentage
		signature
		hash
		duration
		timeLeft
		currentPrice
		suggestedPrice
		currentPriceUsd
		__typename
	}
	
	fragment AssetInfo on Asset {
		erc
		address
		id
		quantity
		orderId
		__typename
	}
	`
	req := GetItemBriefListReq{
		OperationName: "GetItemBriefList",
		Variables: itemVariables{
			From:        from,
			Size:        100,
			Sort:        "PriceAsc",
			AuctionType: "All",
			Owner:       address,
		},
		Query: q,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetItemBriefList")).
		SetHeader("Content-Type", "application/json").
		SetBody(&req).
		SetResult(&respData).
		Post("")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetItemBriefList error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetItemBriefList error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}
