package axieinfinityapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiHost      = "https://graphql-gateway.axieinfinity.com/graphql"
	skyMavisHost = "https://game-api-origin.skymavis.com"

	runesURI  = "/v2/runes"
	charmsURI = "/v2/charms"

	// AxieContractAddress .
	AxieContractAddress = "******************************************"
	// ItemContractAddress .
	ItemContractAddress = "******************************************"
	// LandContractAddress .
	LandContractAddress = "******************************************"
	// AccessoryContractAddress .
	AccessoryContractAddress = "******************************************"

	// LandTemplateURI .
	LandTemplateURI = "https://cdn.axieinfinity.com/avatars/land/rect/rect_%d_%d.png"
	// AccessoryTemplateURI .
	AccessoryTemplateURI = "https://cdn.axieinfinity.com/marketplace-website/accessories/%s.png"
)

var (
	timeout = time.Duration(120 * time.Second)
)

// IAxieInfinity defines the interface for axieInfinityAPI
type IAxieInfinity interface {
	GetAccessoriesMarketplace(ctx context.Context) (*GetAccessoriesMarketplaceResp, error)
	GetAccessories(ctx context.Context, address string, from, instancesFrom int) (*GetAccessoriesResp, error)
	GetAxieBriefList(ctx context.Context, address string, from int) (*GetAxieBriefListResp, error)
	GetAxieDetail(ctx context.Context, axieID string) (*GetAxieDetailResp, error)
	GetBundleList(ctx context.Context) (*GetBundleListResp, error)
	GetCharms(ctx context.Context) (*GetCharmsResp, error)
	GetItemBriefList(ctx context.Context, address string, from int) (*GetItemBriefListResp, error)
	GetLandsGrid(ctx context.Context, address string, from int) (*GetLandsGridResp, error)
	GetRunes(ctx context.Context) (*GetRunesResp, error)
}

type axieInfinityAPI struct {
	client         resty.Client
	skyMavisClient resty.Client
}

var axieObj IAxieInfinity

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	skyMavisClient := resty.NewRestyClient()
	axieObj = &axieInfinityAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).SetTimeout(timeout),
		skyMavisClient: skyMavisClient.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(skyMavisHost).SetTimeout(timeout),
	}
}

// Init inits with resty.Client for mocking
func Init(client, skyMavisClient resty.Client) {
	axieObj = &axieInfinityAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).SetTimeout(timeout),
		skyMavisClient: skyMavisClient.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(skyMavisHost).SetTimeout(timeout),
	}
}

// Get get KCC API singleton
func Get() IAxieInfinity {
	return axieObj
}
