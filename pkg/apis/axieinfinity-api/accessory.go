package axieinfinityapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type accessoryVariables struct {
	From        int    `json:"from"`
	Size        int    `json:"size"`
	AuctionType string `json:"auctionType"`
}

// GetAccessoriesMarketplaceReq .
type GetAccessoriesMarketplaceReq struct {
	OperationName string             `json:"operationName"`
	Variables     accessoryVariables `json:"variables"`
	Query         string             `json:"query"`
}

// GetAccessoriesMarketplaceResp .
type GetAccessoriesMarketplaceResp struct {
	Data struct {
		Equipments struct {
			Total   int `json:"total"`
			Results []struct {
				ID            string   `json:"id"`
				Alias         string   `json:"alias"`
				MinPrice      string   `json:"minPrice"`
				EquipmentType int      `json:"equipmentType"`
				Slot          int      `json:"slot"`
				Name          string   `json:"name"`
				Rarity        string   `json:"rarity"`
				Collections   []string `json:"collections"`
				TypeName      string   `json:"__typename"`
			} `json:"results"`
			TypeName string `json:"__typename"`
		} `json:"equipments"`
	} `json:"data"`
}

// GetAccessoriesMarketplace .
func (a *axieInfinityAPI) GetAccessoriesMarketplace(ctx context.Context) (*GetAccessoriesMarketplaceResp, error) {
	respData := GetAccessoriesMarketplaceResp{}
	q := `
	query GetAccessoriesMarketplace(
		$from: Int!,
		$size: Int!,
		$auctionType: AuctionType
	) {
		equipments(
			from: $from,
			size: $size,
			auctionType: $auctionType
		) {
			total
			results {
				id: alias
				alias
				minPrice
				equipmentType
				slot
				name
				rarity
				collections
				__typename
			}
			__typename
		}
	}
	`
	req := GetAccessoriesMarketplaceReq{
		OperationName: "GetAccessoriesMarketplace",
		Variables: accessoryVariables{
			From:        0,
			Size:        32,
			AuctionType: "All",
		},
		Query: q,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetAccessoriesMarketplace")).
		SetHeader("Content-Type", "application/json").
		SetBody(&req).
		SetResult(&respData).
		Post("")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetAccessoriesMarketplace error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetAccessoriesMarketplace error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}

type accessoriesVariables struct {
	IncludeInstances     bool   `json:"includeInstances"`
	IncludeEquippedTotal bool   `json:"includeEquippedTotal"`
	IncludeEquippedBy    bool   `json:"includeEquippedBy"`
	Owner                string `json:"owner"`
	From                 int    `json:"from"`
	Size                 int    `json:"size"`
	Sort                 string `json:"sort"`
	InstancesSize        int    `json:"instancesSize"`
	InstancesFrom        int    `json:"instancesFrom"`
	AuctionType          string `json:"auctionType"`
}

// GetAccessoriesReq .
type GetAccessoriesReq struct {
	OperationName string               `json:"operationName"`
	Variables     accessoriesVariables `json:"variables"`
	Query         string               `json:"query"`
}

// GetAccessoriesResp .
type GetAccessoriesResp struct {
	Data struct {
		Equipments struct {
			Total   int `json:"total"`
			Results []struct {
				Alias                   string   `json:"alias"`
				MinPrice                string   `json:"minPrice"`
				EquipmentType           int      `json:"equipmentType"`
				Slot                    string   `json:"slot"`
				Name                    string   `json:"name"`
				Rarity                  string   `json:"rarity"`
				Collections             []string `json:"collections"`
				Total                   int      `json:"total"`
				EquippedTotal           int      `json:"equippedTotal"`
				LowestUnequippedTokenID string   `json:"lowestUnequippedTokenId"`
				TypeName                string   `json:"__typename"`
				Instances               []struct {
					ID            string      `json:"id"`
					TokenID       string      `json:"tokenId"`
					Owner         string      `json:"owner"`
					EquipmentID   int         `json:"equipmentId"`
					Alias         string      `json:"alias"`
					EquipmentType int         `json:"equipmentType"`
					Slot          string      `json:"slot"`
					Name          string      `json:"name"`
					Rarity        string      `json:"rarity"`
					Collections   []string    `json:"collections"`
					EquippedBy    interface{} `json:"equippedBy"`
					TypeName      string      `json:"__typename"`
				}
			} `json:"results"`
			TypeName string `json:"__typename"`
		} `json:"equipments"`
	} `json:"data"`
}

// GetAccessories .
func (a *axieInfinityAPI) GetAccessories(ctx context.Context, address string, from, instancesFrom int) (*GetAccessoriesResp, error) {
	ctx, span := tracing.Start(ctx, "accessory.GetAccessories")
	defer span.End()

	respData := GetAccessoriesResp{}
	q := `
	query GetAccessories(
		$owner: String, 
		$from: Int!, 
		$size: Int!, 
		$instancesSize: Int, 
		$instancesFrom: Int, 
		$sort: SortBy!, 
		$auctionType: AuctionType, 
		$includeInstances: Boolean = false, 
		$includeEquippedTotal: Boolean = false, 
		$includeEquippedBy: Boolean = false
	) { 
		equipments(
			owner: $owner, 
			from: $from, 
			size: $size, 
			auctionType: $auctionType
		) { 
			total 
			results { 
				...Equipment 
				instances(
					size: $instancesSize, 
					from: $instancesFrom, 
					sort: $sort
				) @include(
					if: $includeInstances
				) { 
					id: tokenId 
					tokenId 
					owner 
					equipmentId 
					alias 
					equipmentType 
					slot 
					name 
					rarity 
					collections 
					equippedBy 
					@include(
						if: $includeEquippedBy
					) 
					__typename 
				} 
				__typename 
			} 
			__typename 
		} 
	} 
	
	fragment Equipment on Equipment { 
		alias 
		minPrice 
		equipmentType 
		slot 
		name 
		rarity 
		collections 
		total 
		equippedTotal 
		@include(
			if: $includeEquippedTotal
		) 
		lowestUnequippedTokenId 
		@include(
			if: $includeEquippedTotal
		) 
		__typename 
	}
	`
	req := GetAccessoriesReq{
		OperationName: "GetAccessories",
		Variables: accessoriesVariables{
			From:                 from,
			Size:                 20,
			Sort:                 "PriceAsc",
			Owner:                address,
			IncludeInstances:     true,
			IncludeEquippedTotal: true,
			IncludeEquippedBy:    true,
			InstancesSize:        100,
			InstancesFrom:        instancesFrom,
			AuctionType:          "All",
		},
		Query: q,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetAccessories")).
		SetHeader("Content-Type", "application/json").
		SetBody(&req).
		SetResult(&respData).
		Post("")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetAccessories error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetAccessories error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}
