package axieinfinityapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// GetRunesResp response
type GetRunesResp struct {
	Etag  string `json:"_etag"`
	Items []struct {
		Rune      string `json:"rune"`
		Class     string `json:"class"`
		Craftable bool   `json:"craftable"`
		Weight    int    `json:"weight"`
		HP        int    `json:"hp"`
		HPPct     int    `json:"hpPct"`
		Item      struct {
			ID            string `json:"id"`
			DisplayOrder  int    `json:"displayOrder"`
			Category      string `json:"category"`
			Rarity        string `json:"rarity"`
			Description   string `json:"description"`
			Name          string `json:"name"`
			TokenStandard string `json:"tokenStandard"`
			TokenAddress  string `json:"tokenAddress"`
			TokenID       string `json:"tokenId"`
			ImageURL      string `json:"imageUrl"`
		} `json:"item"`
		Season struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
		} `json:"season"`
		Etag string `json:"_etag"`
	} `json:"items"`
	Metadata interface{} `json:"_metadata"`
}

// GetRunes get runes
func (a *axieInfinityAPI) GetRunes(ctx context.Context) (*GetRunesResp, error) {
	respData := GetRunesResp{}
	resp, err := a.skyMavisClient.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "GetRunes")).
		SetResult(&respData).
		Get(runesURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetCharms error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "GetCharms error", resp.String())
		return nil, errors.New(resp.String())
	}

	return &respData, nil
}
