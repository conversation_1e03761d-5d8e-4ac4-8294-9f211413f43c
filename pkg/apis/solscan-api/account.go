package solscanapi

import (
	"context"
	"errors"
	"strconv"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/samber/lo"
)

const (
	DefaultPageLimit = 60
)

// ActivityType is the activity type
type ActivityType string

const (
	ActivityTypeSPLTransfer      ActivityType = "ACTIVITY_SPL_TRANSFER"
	ActivityTypeSPLBurn          ActivityType = "ACTIVITY_SPL_BURN"
	ActivityTypeSPLMint          ActivityType = "ACTIVITY_SPL_MINT"
	ActivityTypeSPLCreateAccount ActivityType = "ACTIVITY_SPL_CREATE_ACCOUNT"
)

// TransferRequestParams for GetTransfers
type TransferRequestParams struct {
	// Below are required
	Address   string
	Page      int
	PageSize  int
	SortOrder string
	// Below are optional
	ActivityType      []ActivityType
	TokenAccount      *string
	From              *string
	To                *string
	Token             *string
	Amount            []int
	BlockTime         []int64
	ExcludeAmountZero *bool
	Flow              *string
}

// Transfer represents a transfer data structure
type Transfer struct {
	BlockID       uint32       `json:"block_id"`
	TransID       string       `json:"trans_id"`
	BlockTime     int64        `json:"block_time"`
	Time          string       `json:"time"`
	ActivityType  ActivityType `json:"activity_type"`
	FromAddress   string       `json:"from_address"`
	ToAddress     string       `json:"to_address"`
	TokenAddress  string       `json:"token_address"`
	TokenDecimals int          `json:"token_decimals"`
	Amount        int          `json:"amount"`
	Flow          string       `json:"flow"`
	Value         float64      `json:"value"`
}

// TokenMetadata is the token metadata
type TokenMetadata struct {
	TokenAddress string `json:"token_address"`
	TokenName    string `json:"token_name"`
	TokenSymbol  string `json:"token_symbol"`
	TokenIcon    string `json:"token_icon"`
}

// GetTransfers fetches transfer data from the Solscan API v2 and categorizes them into SOL and SPL transfers
func (s *solscanAPI) GetTransfers(ctx context.Context, param *TransferRequestParams) ([]*Transfer, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "account.GetTransfers")
	defer span.End()

	if param.Address == "" {
		return nil, 0, errors.New("address is required")
	}
	if param.Page <= 0 {
		return nil, 0, errors.New("page cannot be less than 1")
	}
	availablePageSize := []int{10, 20, 30, 40, 60, 100}
	if !lo.Contains(availablePageSize, param.PageSize) {
		return nil, 0, errors.New("page_size must be one of [10, 20, 30, 40, 60, 100]")
	}
	if param.SortOrder == "" {
		param.SortOrder = "desc"
	}

	respData := struct {
		Success  bool        `json:"success"`
		Data     []*Transfer `json:"data"`
		Metadata struct {
			Tokens map[string]TokenMetadata `json:"tokens"`
		} `json:"metadata"`
	}{}

	req := s.client.R().SetContext(ctx)

	// Set individual query parameters
	req.SetQueryParam("sort_by", "block_time")
	req.SetQueryParam("address", param.Address)
	req.SetQueryParam("page", strconv.Itoa(param.Page))
	req.SetQueryParam("page_size", strconv.Itoa(param.PageSize))
	req.SetQueryParam("sort_order", param.SortOrder)
	req.SetQueryParam("activity_type[]", string(ActivityTypeSPLTransfer))
	if param.TokenAccount != nil {
		req.SetQueryParam("token_account", *param.TokenAccount)
	}
	if param.From != nil {
		req.SetQueryParam("from", *param.From)
	}
	if param.To != nil {
		req.SetQueryParam("to", *param.To)
	}
	if param.Token != nil {
		req.SetQueryParam("token", *param.Token)
	}
	if param.ExcludeAmountZero != nil {
		req.SetQueryParam("exclude_amount_zero", strconv.FormatBool(*param.ExcludeAmountZero))
	}
	if param.Flow != nil {
		req.SetQueryParam("flow", *param.Flow)
	}

	req.SetResult(&respData)

	resp, err := req.Get("/account/transfer")
	if err != nil {
		return nil, 0, err
	}

	return respData.Data, resp.Time(), nil
}
