//go:generate mockgen -package=solscanapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/solscan-api -destination=common_mock.go . ISolscan
package solscanapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

// pro api
const (
	apiHost = "https://pro-api.solscan.io/v2.0"
)

var (
	proAPIKey string
	timeout   = time.Duration(120 * time.Second)
)

// ISolscan is the interface for solscanAPI
type ISolscan interface {
	GetTransfers(ctx context.Context, param *TransferRequestParams) ([]*Transfer, time.Duration, error)
}

type solscanAPI struct {
	client resty.Client
}

var solscanObj ISolscan

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	proAPIKey = config.GetString("SOLSCAN_API_KEY_V2")
	if proAPIKey == "" {
		kglog.Error("Cannot get Solscan pro api key v2!")
	}
	solscanObj = &solscanAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).
			SetHeader("token", proAPIKey).
			SetTimeout(timeout),
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client) {
	proAPIKey = config.GetString("SOLSCAN_API_KEY_V2")
	if proAPIKey == "" {
		kglog.Error("Cannot get Solscan pro api key v2!")
	}
	solscanObj = &solscanAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).
			SetHeader("token", proAPIKey).
			SetTimeout(timeout),
	}
}

// Get get KCC API singleton
func Get() ISolscan {
	return solscanObj
}

func Set(c ISolscan) {
	solscanObj = c
}
