package github

import (
	"context"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/samber/lo"
)

var trustWalletChains = map[domain.Chain]string{
	domain.Ethereum:  "ethereum",
	domain.Polygon:   "polygon",
	domain.Arbitrum:  "arbitrum",
	domain.BNBChain:  "binance",
	domain.Kcc:       "kcc",
	domain.Solana:    "solana",
	domain.Tron:      "tron",
	domain.Ronin:     "ronin",
	domain.Oasys:     "oasys",
	domain.BaseChain: "base",
	domain.Optimism:  "optimism",
}

type fetcherImpl struct{}

// Fetcher of trust wallet github repo. Its coingecko ID will be empty
func Fetcher() domain.TokenMetadataFetcher {
	return &fetcherImpl{}
}

func (f *fetcherImpl) SupportedChains() []domain.Chain {
	return lo.Keys(trustWalletChains)
}

func (f *fetcherImpl) FetchMetadata(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
	ctx, span := tracing.Start(ctx, "github.FetchMetadata")
	defer span.End()

	twChainID, ok := trustWalletChains[chain]
	if !ok {
		return nil, fmt.Errorf("chain %s is not supported", chain)
	}

	resp, httpResp, err := GetTrustWalletTokenInfo(ctx, twChainID, tokenID)
	if err != nil {
		if httpResp != nil && httpResp.StatusCode() == 404 {
			return nil, domain.ErrRecordNotFound
		}
		return nil, err
	}

	return &domain.TokenMetadata{
		Name:       resp.Name,
		Symbol:     resp.Symbol,
		LogoUrl:    GetTrustWalletTokenLogo(twChainID, tokenID),
		Decimals:   uint(resp.Decimals),
		IsVerified: true,
	}, nil
}
