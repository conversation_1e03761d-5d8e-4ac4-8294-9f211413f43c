package github

import (
	"context"
	"errors"
	"fmt"

	"github.com/go-resty/resty/v2"
)

// GetTrustWalletTokenInfoResponse .
type GetTrustWalletTokenInfoResponse struct {
	Name        string `json:"name"`
	Symbol      string `json:"symbol"`
	Type        string `json:"type"`
	Decimals    int32  `json:"decimals"`
	Description string `json:"description"`
	Website     string `json:"website"`
	Explorer    string `json:"explorer"`
	Status      string `json:"status"`
	ID          string `json:"id"`
}

// GetTrustWalletTokenInfo .
func GetTrustWalletTokenInfo(ctx context.Context, twChainID, address string) (*GetTrustWalletTokenInfoResponse, *resty.Response, error) {
	respData := GetTrustWalletTokenInfoResponse{}
	resp, err := httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetPathParam("chain_id", twChainID).
		SetPathParam("address", address).
		ForceContentType("application/json").
		SetResult(&respData).
		Get(trustWalletGithubURL)

	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() >= 400 {
		return &respData, resp, errors.New(resp.String())
	}
	return &respData, resp, nil
}

// GetTrustWalletTokenLogo .
func GetTrustWalletTokenLogo(twChainID, address string) string {
	return fmt.Sprintf(trustWalletGithubLogoURL, twChainID, address)
}
