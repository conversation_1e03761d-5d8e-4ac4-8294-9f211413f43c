package github

import (
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

var (
	timeout    = time.Duration(15 * time.Second)
	httpClient *resty.Client
)

const (
	trustWalletGithubURL     = "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/{chain_id}/assets/{address}/info.json"
	trustWalletGithubLogoURL = "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/%s/assets/%s/logo.png"
)

func init() {
	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)
}
