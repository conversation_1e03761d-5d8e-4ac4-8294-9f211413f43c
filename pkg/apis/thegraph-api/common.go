package thegraphapi

import (
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

const (
	apiHost = "https://api.thegraph.com"
	ensURI  = "/subgraphs/name/ensdomains/ens"
)

var (
	timeout    = time.Duration(3 * time.Second)
	httpClient *resty.Client
)

func init() {
	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetBaseURL(apiHost).SetTimeout(timeout)
}
