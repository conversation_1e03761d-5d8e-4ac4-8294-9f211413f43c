package thegraphapi

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// GetEnsResp api response
type GetEnsResp struct {
	Data struct {
		Domains []struct {
			Name string `json:"name"`
		} `json:"domains"`
	} `json:"data"`
}

// GetENSNames get ens names
func GetENSNames(ctx context.Context, wallet string) ([]string, error) {
	respData := GetEnsResp{}
	// graphql query
	Query := fmt.Sprintf(`
	query {
		domains(where: {owner_in: ["%s"]}) {
		    name
		}
	}
	`, strings.ToLower(wallet))
	data := map[string]interface{}{
		"query": Query,
	}

	resp, err := httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(ensURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "thegraph, GetEns error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "thegraph, GetEns error", resp.String())
		return nil, errors.New(resp.String())
	}

	// parse resp
	ensNames := []string{}
	if respData.Data.Domains != nil {
		for _, v := range respData.Data.Domains {
			if skipReverseRegistrar(v.Name) {
				continue
			}
			ensNames = append(ensNames, v.Name)
		}
	}

	return ensNames, nil
}

// skipReverseRegistrar skips reverse registrar
func skipReverseRegistrar(ensName string) bool {
	return strings.HasSuffix(ensName, ".addr.reverse")
}
