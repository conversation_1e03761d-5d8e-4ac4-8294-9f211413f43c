package coingeckoapi

import (
	"context"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type SimplePriceByAddressResp map[string]map[string]float64

// SimplePriceByAddress queries simple price by contract address from coingecko
//
// Note though coingecko supports multiple contract addresses, we only support one address for now since
// free tier of coingecko API only supports one address
func (c *coingeckoAPI) SimplePriceByAddress(ctx context.Context, platform, address, vsCurrencies string) (SimplePriceByAddressResp, error) {
	ctx, span := tracing.Start(ctx, "[coingeckoapi] SimplePriceByAddress")
	defer span.End()

	respData := make(SimplePriceByAddressResp)
	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := make(map[string]map[string]float64)
		resp, err := request.
			SetQueryParam("contract_addresses", address).
			SetQueryParam("vs_currencies", vsCurrencies).
			SetResult(&localRespData).
			Get(fmt.Sprintf(simplePriceByAddressURI, platform))
		respData = localRespData
		return resp, err
	}

	_, err := c.makeRequestWithAPIKeyRetry(ctx, requestFn)

	if err != nil {
		return nil, err
	}

	return respData, nil
}
