package coingeckoapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
)

// GetPrices get prices of tokens. If context deadline is exceeded, return the prices fetched so far
func (c *coingeckoAPI) GetPrices(ctx context.Context, coingeckoIDs []domain.CoingeckoID) (map[domain.CoingeckoID]domain.Price, error) {
	ctx, span := tracing.Start(ctx, "coingeckoAPI.GetPrices")
	defer span.End()

	rtn := map[domain.CoingeckoID]domain.Price{}

	if len(coingeckoIDs) == 0 {
		return rtn, nil
	}

	batchQuerySize := 500
	chunks := lo.Chunk(coingeckoIDs, batchQuerySize)

	for _, chunk := range chunks {
		if ctx.Err() != nil {
			return rtn, ctx.Err()
		}

		quotes, err := c.tokenQuotes(ctx, chunk)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[GetPrices] tokenQuotes failed", map[string]interface{}{"ids": chunk, "err": err.Error()})
			continue
		}

		for _, id := range chunk {
			if price, ok := quotes[id]; ok {
				rtn[id] = domain.Price(price)
			}
		}
	}
	return rtn, nil
}

// tokenQuotes get a map of token quotes
func (c *coingeckoAPI) tokenQuotes(ctx context.Context, ids []domain.CoingeckoID) (map[domain.CoingeckoID]float64, error) {
	ctx, span := tracing.Start(ctx, "coingecko.tokenQuotes")
	defer span.End()

	var resp map[domain.CoingeckoID]float64
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "tokenQuotes", time.Second*10)
		if err != nil {
			return err
		}
		localResp, err := c.SimplePrice(ctx, ids, "usd")
		if err != nil && err != code.ErrRateLimitExceeded {
			kglog.ErrorWithDataCtx(ctx, "[tokenQuotes] SimplePrice failed", map[string]interface{}{"ids": ids, "err": err.Error()})
		}
		resp = localResp
		return err
	})
	return resp, retryErr
}

func (c *coingeckoAPI) GetPricesByContract(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]domain.Price, error) {
	panic("not implemented")
}

func (c *coingeckoAPI) PricesByContractSupportedChains() []domain.Chain {
	panic("not implemented")
}
