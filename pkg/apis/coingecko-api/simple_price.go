package coingeckoapi

import (
	"context"
	"strings"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/samber/lo"
)

type SimplePriceResp map[string]map[string]float64

// SimplePrice queries simple price from coingecko
func (c *coingeckoAPI) SimplePrice(ctx context.Context, ids []domain.CoingeckoID, vsCurrencies string) (map[domain.CoingeckoID]float64, error) {
	ctx, span := tracing.Start(ctx, "[coingeckoapi] SimplePrice")
	defer span.End()

	if len(ids) == 0 {
		return make(map[domain.CoingeckoID]float64), nil
	}

	respData := SimplePriceResp{}
	joinedIds := strings.Join(lo.Map(ids, func(id domain.CoingeckoID, _ int) string {
		return string(id)
	}), ",")

	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := SimplePriceResp{}
		resp, err := request.
			SetQueryParam("ids", joinedIds).
			SetQueryParam("vs_currencies", vsCurrencies).
			SetResult(&localRespData).
			Get(simplePriceURI)
		respData = localRespData
		return resp, err
	}

	_, err := c.makeRequestWithAPIKeyRetry(ctx, requestFn)
	if err != nil {
		return nil, err
	}

	result := make(map[domain.CoingeckoID]float64)
	for id, value := range respData {
		result[domain.CoingeckoID(id)] = value[vsCurrencies]
	}
	return result, nil
}
