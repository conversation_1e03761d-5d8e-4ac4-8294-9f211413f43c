package coingeckoapi

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiHost                 = "https://api.coingecko.com/api/v3"
	coinListURI             = "/coins/list"
	simplePriceURI          = "/simple/price"
	simplePriceByAddressURI = "/simple/token_price/%s"
)

var (
	timeout = time.Duration(15 * time.Second)
)

// ICoingecko defines the interface for coingeckoAPI
type ICoingecko interface {
	CoinList(ctx context.Context) (*CoinListResp, error)
	SimplePrice(ctx context.Context, ids []domain.CoingeckoID, vsCurrencies string) (map[domain.CoingeckoID]float64, error)
	SimplePriceByAddress(ctx context.Context, platform, address, vsCurrencies string) (SimplePriceByAddressResp, error)
	CoinHistory(ctx context.Context, id string, date string) (CoinHistoryResp, error)
	GetSupportedCurrencies(ctx context.Context) ([]string, error)
	IsCurrencySupported(ctx context.Context, currency string) (bool, error)
	domain.PriceFetcher
}

type coingeckoAPI struct {
	client resty.Client
	rl     domain.RateLimiter
}

var coingeckoObj ICoingecko

// InitDefault inits default API
func InitDefault(ratelimiter domain.RateLimiter) {
	client := resty.NewRestyClient()
	coingeckoObj = &coingeckoAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).SetTimeout(timeout),
		rl: ratelimiter,
	}
}

// Set inits with mock coingecko api
func Set(instance ICoingecko) {
	coingeckoObj = instance
}

// InitResty inits with resty.Client for mocking
func InitResty(client resty.Client, ratelimiter domain.RateLimiter) {
	coingeckoObj = &coingeckoAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).SetTimeout(timeout),
		rl: ratelimiter,
	}
}

// Get get KCC API singleton
func Get() ICoingecko {
	return coingeckoObj
}

// Helper function to make an API request with API key retry logic
func (c *coingeckoAPI) makeRequestWithAPIKeyRetry(ctx context.Context, requestFn func(resty.Request) (*resty.Response, error)) (*resty.Response, error) {
	var resp *resty.Response
	var err error

	request := c.client.R().SetContext(ctx)

	// First attempt without API key
	resp, err = requestFn(request)

	// Check for errors and retry with API key if needed
	if err != nil || resp.StatusCode() >= 400 {
		request = c.client.R().SetContext(ctx)
		request.SetQueryParam("x_cg_demo_api_key", config.GetString("COINGECKO_API_KEY"))
		resp, err = requestFn(request)

		if err != nil {
			return resp, err
		}
		if resp.StatusCode() == 429 {
			return nil, code.ErrRateLimitExceeded
		}
		if strings.Contains(string(resp.Body()), "Your request exceeds the allowed time range.") {
			return nil, errors.New("request exceeds the allowed time range (365 days)")
		}
		if resp.StatusCode() >= 400 {
			kglog.ErrorWithDataCtx(ctx, "[Coingecko] API request failed", map[string]interface{}{
				"status_code": resp.StatusCode(),
				"message":     string(resp.Body()),
			})
			return resp, fmt.Errorf("[Coingecko] API request failed with status code %d and message: %s", resp.StatusCode(), string(resp.Body()))
		}
	}

	return resp, nil
}
