package coingeckoapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type CoinHistoryResp struct {
	ID         string `json:"id"`
	Symbol     string `json:"symbol"`
	Name       string `json:"name"`
	MarketData struct {
		CurrentPrice struct {
			Usd float64 `json:"usd"`
		} `json:"current_price"`
	} `json:"market_data"`
}

// CoinHistory queries the historical data of a coin from coingecko
//
// Note the data must be in the format of "dd-mm-yyyy"
func (c *coingeckoAPI) CoinHistory(ctx context.Context, id string, date string) (CoinHistoryResp, error) {
	ctx, span := tracing.Start(ctx, "[coingeckoapi] CoinHistory")
	defer span.End()

	respData := CoinHistoryResp{}

	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := CoinHistoryResp{}
		resp, err := request.
			SetQueryParam("date", date).
			SetResult(&localRespData).
			Get("/coins/" + id + "/history")
		respData = localRespData
		return resp, err
	}

	_, err := c.makeRequestWithAPIKeyRetry(ctx, requestFn)
	if err != nil {
		return CoinHistoryResp{}, err
	}

	return respData, nil
}
