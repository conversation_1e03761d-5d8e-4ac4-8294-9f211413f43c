package coingeckoapi

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	supportedVsCurrenciesURI = "/simple/supported_vs_currencies"
	cacheDuration            = 24 * time.Hour
)

var (
	currenciesCache      []string
	currenciesCacheTime  time.Time
	currenciesCacheMutex sync.RWMutex
)

// GetSupportedCurrencies returns a list of currencies supported by Coingecko for price conversion
func (c *coingeckoAPI) GetSupportedCurrencies(ctx context.Context) ([]string, error) {
	ctx, span := tracing.Start(ctx, "[coingeckoapi] GetSupportedCurrencies")
	defer span.End()

	// Check if we have a valid cache
	currenciesCacheMutex.RLock()
	if len(currenciesCache) > 0 && time.Since(currenciesCacheTime) < cacheDuration {
		defer currenciesCacheMutex.RUnlock()
		return currenciesCache, nil
	}
	currenciesCacheMutex.RUnlock()

	// Need to refresh the cache
	currenciesCacheMutex.Lock()
	defer currenciesCacheMutex.Unlock()

	// Double-check in case another goroutine refreshed while we were waiting
	if len(currenciesCache) > 0 && time.Since(currenciesCacheTime) < cacheDuration {
		return currenciesCache, nil
	}

	respData := make([]string, 0)
	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := make([]string, 0)
		resp, err := request.
			SetResult(&localRespData).
			Get(supportedVsCurrenciesURI)
		respData = localRespData
		return resp, err
	}

	_, err := c.makeRequestWithAPIKeyRetry(ctx, requestFn)
	if err != nil {
		return nil, err
	}

	// Update cache
	currenciesCache = respData
	currenciesCacheTime = time.Now()

	kglog.InfoWithDataCtx(ctx, "Updated Coingecko supported currencies cache", map[string]interface{}{
		"count":      len(respData),
		"currencies": strings.Join(respData, ", "),
	})

	return respData, nil
}

// IsCurrencySupported checks if a given currency is supported by Coingecko
func (c *coingeckoAPI) IsCurrencySupported(ctx context.Context, currency string) (bool, error) {
	currencies, err := c.GetSupportedCurrencies(ctx)
	if err != nil {
		return false, err
	}

	lowerCurrency := strings.ToLower(currency)
	for _, supported := range currencies {
		if supported == lowerCurrency {
			return true, nil
		}
	}

	return false, nil
}
