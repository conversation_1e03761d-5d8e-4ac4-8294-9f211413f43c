package coingeckoapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// CoinInfo represents the information of a coin from coingecko
type CoinInfo struct {
	ID        string            `json:"id"`
	Symbol    string            `json:"symbol"`
	Name      string            `json:"name"`
	Platforms map[string]string `json:"platforms"`
}

// CoinListResp is the response of CoinList request
type CoinListResp []CoinInfo

// CoinList queries the list of coins from coingecko
func (c *coingeckoAPI) CoinList(ctx context.Context) (*CoinListResp, error) {
	ctx, span := tracing.Start(ctx, "[coingeckoapi] CoinList")
	defer span.End()

	respData := make(CoinListResp, 0)
	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := make(CoinListResp, 0)
		resp, err := request.
			SetQueryParam("include_platform", "true").
			SetResult(&localRespData).
			Get(coinListURI)
		respData = localRespData
		return resp, err
	}

	_, err := c.makeRequestWithAPIKeyRetry(ctx, requestFn)
	if err != nil {
		return nil, err
	}

	return &respData, nil
}
