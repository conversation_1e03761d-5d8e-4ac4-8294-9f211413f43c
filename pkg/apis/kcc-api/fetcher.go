package kccapi

import (
	"context"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

func (k *kccAPI) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Kcc}
}

func (k *kccAPI) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken}
}

func (k *kccAPI) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	if len(chains) != 1 || chains[0] != domain.Kcc {
		return &domain.AggregatedAssets{}, nil
	}
	if len(types) != 1 || types[0] != domain.AssetTypeToken {
		return &domain.AggregatedAssets{}, nil
	}

	tokenAmounts := make([]*domain.TokenAmount, 0)

	// Fetch KCS balance
	lowerAddress := strings.ToLower(address.String())
	var kcsBalance *string
	kcsBalance, _, err := k.KCSBalance(ctx, lowerAddress)
	if err != nil {
		// it will return error when balance is 0
		kcsBalance = util.Ptr("0")
	}

	if kcsBalance != nil {
		kcsAmount, _ := decimal.NewFromString(*kcsBalance)
		kcsToken := domain.Kcc.MainToken()
		tokenAmounts = append(tokenAmounts, &domain.TokenAmount{
			Token:  kcsToken,
			Amount: kcsAmount,
		})
	}

	// Fetch token balances
	tokenListResp, _, err := k.TokenList(ctx, lowerAddress)
	if err != nil {
		return nil, err
	}

	for _, token := range tokenListResp.Result {
		rawAmount, _ := decimal.NewFromString(token.Balance)
		decimals, _ := decimal.NewFromString(token.Decimals)
		tokenObj := domain.NewToken(domain.Kcc, token.ContractAddress, token.Name, token.Symbol, "", uint(decimals.IntPart()), true)
		tokenAmounts = append(tokenAmounts, &domain.TokenAmount{
			Token:  tokenObj,
			Amount: rawAmount.Div(decimal.NewFromInt(10).Pow(decimals)),
		})
	}

	return &domain.AggregatedAssets{
		Tokens: tokenAmounts,
	}, nil
}

// Ensure kccAPI implements AssetFetcher interface
var _ domain.AssetFetcher = (*kccAPI)(nil)
