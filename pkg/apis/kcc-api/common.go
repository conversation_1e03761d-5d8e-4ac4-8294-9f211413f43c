//go:generate mockgen -source=common.go -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/kcc-api -destination=common_mock.go -package=kccapi IKcc
package kccapi

import (
	"context"
	"math/big"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

var (
	timeout = time.Duration(15 * time.Second)
)

// IKcc defines the interface for KccAPI
type IKcc interface {
	KCSBalance(ctx context.Context, address string) (*string, *resty.Response, error)
	NativeBalance(ctx context.Context, address string) (*big.Int, error)
	TransactionList(ctx context.Context, params *RequestParams) (*TransactionListResp, *resty.Response, error)
	InternalTransactionList(ctx context.Context, params *RequestParams) (*InternalTransactionListResp, *resty.Response, error)
	TokenTransactionList(ctx context.Context, params *RequestParams) (*TokenTransactionListResp, *resty.Response, error)
	TokenList(ctx context.Context, address string) (*TokenListResp, *resty.Response, error)
	domain.AssetFetcher
}

type kccAPI struct {
	client resty.Client
}

var kccObj IKcc

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	kccObj = &kccAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout),
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client) {
	kccObj = &kccAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout),
	}
}

// Get get KCC API singleton
func Get() IKcc {
	return kccObj
}
