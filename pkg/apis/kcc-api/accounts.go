package kccapi

import (
	"context"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/shopspring/decimal"
)

// constants
const (
	kccScanURI = "https://scan.kcc.io/api"
	PageOffset = 50
)

// KCSBalance Get account KCS Balance (converted to decimal string)
func (k *kccAPI) KCSBalance(ctx context.Context, address string) (*string, *resty.Response, error) {
	respData := struct {
		Result string `json:"result"`
	}{}
	resp, err := k.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.eth_get_balance")).
		SetQueryParam("module", "account").
		SetQueryParam("action", "eth_get_balance").
		SetQueryParam("address", address).
		SetResult(&respData).
		Get(kccScanURI)

	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() >= 400 {
		return nil, resp, fmt.Errorf("failed to get balance, status code: %d", resp.StatusCode())
	}
	if respData.Result == "" {
		return nil, resp, fmt.Errorf("failed to get balance, empty result")
	}

	// parse hex to decimal and convert to decimal string
	balance, err := hexutil.DecodeBig(respData.Result)
	if err != nil {
		return nil, resp, fmt.Errorf("failed to decode balance: %s", err.Error())
	}
	balanceDecimal := decimal.NewFromBigInt(balance, 0)
	convertedBalance := fmt.Sprintf("%f", balanceDecimal.Div(decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(18)))).InexactFloat64())
	return &convertedBalance, resp, nil
}

// NativeBalance Get account native balance
func (k *kccAPI) NativeBalance(ctx context.Context, address string) (*big.Int, error) {
	respData := struct {
		Result string `json:"result"`
	}{}
	resp, err := k.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.eth_get_balance")).
		SetQueryParam("module", "account").
		SetQueryParam("action", "eth_get_balance").
		SetQueryParam("address", address).
		SetResult(&respData).
		Get(kccScanURI)

	if err != nil {
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("failed to get balance, status code: %d", resp.StatusCode())
	}
	if respData.Result == "" {
		return nil, fmt.Errorf("failed to get balance, empty result")
	}
	balance, err := hexutil.DecodeBig(respData.Result)
	if err != nil {
		return nil, fmt.Errorf("failed to decode balance: %s", err.Error())
	}
	return balance, nil
}

// RequestParams params
type RequestParams struct {
	Address string
	Page    int32
}

// TransactionListResp response for transaction list
type TransactionListResp struct {
	Status  string         `json:"status"`
	Message string         `json:"message"`
	Result  []*Transaction `json:"result"`
}

// Transaction represents an individual transaction returned by the new API
type Transaction struct {
	BlockHash         string `json:"blockHash"`
	BlockNumber       string `json:"blockNumber"`
	Confirmations     string `json:"confirmations"`
	ContractAddress   string `json:"contractAddress"`
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	From              string `json:"from"`
	Gas               string `json:"gas"`
	GasPrice          string `json:"gasPrice"`
	GasUsed           string `json:"gasUsed"`
	Hash              string `json:"hash"`
	Input             string `json:"input"`
	IsError           string `json:"isError"`
	Nonce             string `json:"nonce"`
	Timestamp         string `json:"timeStamp"`
	To                string `json:"to"`
	TransactionIndex  string `json:"transactionIndex"`
	TxReceiptStatus   string `json:"txreceipt_status"`
	Value             string `json:"value"`
}

// TransactionList retrieves transactions for the given address from the new API
func (k *kccAPI) TransactionList(ctx context.Context, params *RequestParams) (*TransactionListResp, *resty.Response, error) {
	respData := TransactionListResp{}
	resp, err := k.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.txlist")).
		SetQueryParams(map[string]string{
			"module":  "account",
			"action":  "txlist",
			"address": params.Address,
			"page":    fmt.Sprintf("%d", params.Page),
			"offset":  fmt.Sprintf("%d", PageOffset),
		}).
		SetResult(&respData).
		Get(kccScanURI)

	if err != nil {
		return nil, resp, err
	}
	return &respData, resp, nil
}

// InternalTransactionListResp response for the internal transaction list API
type InternalTransactionListResp struct {
	Status  string                 `json:"status"`
	Message string                 `json:"message"`
	Result  []*InternalTransaction `json:"result"`
}

// InternalTransaction represents an internal transaction returned by the API
type InternalTransaction struct {
	BlockNumber     string `json:"blockNumber"`
	CallType        string `json:"callType"`
	ContractAddress string `json:"contractAddress"`
	ErrCode         string `json:"errCode"`
	From            string `json:"from"`
	Gas             string `json:"gas"`
	GasUsed         string `json:"gasUsed"`
	Index           string `json:"index"`
	Input           string `json:"input"`
	IsError         string `json:"isError"`
	Timestamp       string `json:"timeStamp"`
	To              string `json:"to"`
	TransactionHash string `json:"transactionHash"`
	Type            string `json:"type"`
	Value           string `json:"value"`
}

// InternalTransactionList retrieves internal transactions from the new API
func (k *kccAPI) InternalTransactionList(ctx context.Context, params *RequestParams) (*InternalTransactionListResp, *resty.Response, error) {
	respData := InternalTransactionListResp{}
	resp, err := k.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.txlistinternal")).
		SetQueryParams(map[string]string{
			"module":  "account",
			"action":  "txlistinternal",
			"address": params.Address,
			"page":    fmt.Sprintf("%d", params.Page),
			"offset":  fmt.Sprintf("%d", PageOffset),
		}).
		SetResult(&respData).
		Get(kccScanURI)

	if err != nil {
		return nil, resp, err
	}
	return &respData, resp, nil
}

// TokenTransactionListResp represents the response for the token transaction list API
type TokenTransactionListResp struct {
	Status  string              `json:"status"`
	Message string              `json:"message"`
	Result  []*TokenTransaction `json:"result"`
}

// TokenTransaction represents an individual token transaction returned by the API
type TokenTransaction struct {
	BlockHash         string  `json:"blockHash"`
	BlockNumber       string  `json:"blockNumber"`
	Confirmations     string  `json:"confirmations"`
	ContractAddress   string  `json:"contractAddress"`
	CumulativeGasUsed string  `json:"cumulativeGasUsed"`
	From              string  `json:"from"`
	Gas               string  `json:"gas"`
	GasPrice          string  `json:"gasPrice"`
	GasUsed           string  `json:"gasUsed"`
	Hash              string  `json:"hash"`
	Input             string  `json:"input"`
	LogIndex          string  `json:"logIndex"`
	Nonce             string  `json:"nonce"`
	Timestamp         string  `json:"timeStamp"`
	To                string  `json:"to"`
	TokenDecimal      string  `json:"tokenDecimal"`
	TokenName         *string `json:"tokenName"`
	TokenSymbol       *string `json:"tokenSymbol"`
	TokenID           *string `json:"tokenID"`
	TransactionIndex  string  `json:"transactionIndex"`
	Value             string  `json:"value"`
}

// TokenTransactionList retrieves token transactions for the given address from the API
func (k *kccAPI) TokenTransactionList(ctx context.Context, params *RequestParams) (*TokenTransactionListResp, *resty.Response, error) {
	respData := TokenTransactionListResp{}
	resp, err := k.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.tokentx")).
		SetQueryParams(map[string]string{
			"module":  "account",
			"action":  "tokentx",
			"address": params.Address,
			"page":    fmt.Sprintf("%d", params.Page),
			"offset":  fmt.Sprintf("%d", PageOffset),
		}).
		SetResult(&respData).
		Get(kccScanURI)

	if err != nil {
		return nil, resp, err
	}
	return &respData, resp, nil
}

// TokenListResp represents the response for the token list API
type TokenListResp struct {
	Status  string       `json:"status"`
	Message string       `json:"message"`
	Result  []*TokenInfo `json:"result"`
}

// TokenInfo represents the details of each token returned by the API
type TokenInfo struct {
	Balance         string `json:"balance"`
	ContractAddress string `json:"contractAddress"`
	Decimals        string `json:"decimals"`
	Name            string `json:"name"`
	Symbol          string `json:"symbol"`
	Type            string `json:"type"`
}

// TokenList retrieves a list of tokens for the given address from the API
func (k *kccAPI) TokenList(ctx context.Context, address string) (*TokenListResp, *resty.Response, error) {
	respData := TokenListResp{}
	resp, err := k.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.tokenlist")).
		SetQueryParams(map[string]string{
			"module":  "account",
			"action":  "tokenlist",
			"address": address,
		}).
		SetResult(&respData).
		Get(kccScanURI)

	if err != nil {
		return nil, resp, err
	}
	return &respData, resp, nil
}
