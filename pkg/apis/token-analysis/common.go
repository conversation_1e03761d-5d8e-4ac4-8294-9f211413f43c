//go:generate go run go.uber.org/mock/mockgen -source=common.go -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/token-analysis -destination=common_mock.go -package=tokenanalysis ITokenAnalysis
package tokenanalysis

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

const (
	apiHost = "http://**************:8088"
	timeout = 600 * time.Second
)

// Analysis represents the token analysis data
type Analysis struct {
	CEX                    float64 `json:"cex"`
	IndividualTraders      float64 `json:"individual_traders"`
	KOLTraders             float64 `json:"kol_traders"`
	LiquidityPools         float64 `json:"liquidity_pools"`
	Others                 float64 `json:"others"`
	SuspectInsiders        float64 `json:"suspect_insiders"`
	SuspectInsidersBalance float64 `json:"suspect_insiders_sol_balance"`
}

// AnalysisResponse represents the response from the analysis API
type AnalysisResponse struct {
	Status string `json:"status"`
	Data   struct {
		Analysis Analysis `json:"analysis"`
		Token    string   `json:"token"`
	} `json:"data"`
}

// ITokenAnalysis defines the interface for token analysis API
type ITokenAnalysis interface {
	// AnalyzeToken analyzes a token and returns the analysis
	AnalyzeToken(ctx context.Context, tokenAddress string) (*Analysis, error)
}

type tokenAnalysisAPI struct {
	client *resty.Client
}

var (
	tokenAnalysisObj ITokenAnalysis
)

// InitDefault initializes the default token analysis API client
func InitDefault() {
	client := resty.New().
		SetBaseURL(apiHost).
		SetTimeout(timeout).
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger())

	tokenAnalysisObj = &tokenAnalysisAPI{
		client: client,
	}
}

// Init initializes the token analysis API client with a custom resty client
func Init(client *resty.Client) {
	tokenAnalysisObj = &tokenAnalysisAPI{
		client: client,
	}
}

// Get returns the token analysis API singleton
func Get() ITokenAnalysis {
	return tokenAnalysisObj
}

// Set sets the token analysis API client (useful for mocking)
func Set(api ITokenAnalysis) {
	tokenAnalysisObj = api
}

// AnalyzeToken analyzes a token and returns the analysis
func (a *tokenAnalysisAPI) AnalyzeToken(ctx context.Context, tokenAddress string) (*Analysis, error) {
	var lastErr error
	for i := 0; i < 3; i++ {
		if i > 0 {
			kglog.InfoWithDataCtx(ctx, "Retrying token analysis", map[string]interface{}{
				"attempt":      i + 1,
				"tokenAddress": tokenAddress,
			})
			time.Sleep(3 * time.Second)
		}

		resp, err := a.client.R().
			SetContext(ctx).
			SetHeader("Content-Type", "application/json").
			SetBody(map[string]string{
				"token": tokenAddress,
			}).
			Post("/analyze")

		if err != nil {
			lastErr = err
			kglog.ErrorWithDataCtx(ctx, "Failed to call token analysis API", map[string]interface{}{
				"error":        err.Error(),
				"tokenAddress": tokenAddress,
				"attempt":      i + 1,
			})
			continue
		}

		if resp.StatusCode() != http.StatusOK {
			lastErr = fmt.Errorf("failed to analyze token: status %d", resp.StatusCode())
			kglog.ErrorWithDataCtx(ctx, "Token analysis API returned non-OK status", map[string]interface{}{
				"statusCode":   resp.StatusCode(),
				"response":     string(resp.Body()),
				"tokenAddress": tokenAddress,
				"attempt":      i + 1,
			})
			continue
		}

		// Parse the response
		var apiResponse AnalysisResponse
		if err := json.Unmarshal(resp.Body(), &apiResponse); err != nil {
			lastErr = err
			kglog.ErrorWithDataCtx(ctx, "Failed to unmarshal token analysis API response", map[string]interface{}{
				"error":        err.Error(),
				"response":     string(resp.Body()),
				"tokenAddress": tokenAddress,
				"attempt":      i + 1,
			})
			continue
		}

		if apiResponse.Status != "success" {
			lastErr = fmt.Errorf("analysis failed: %s", apiResponse.Status)
			kglog.ErrorWithDataCtx(ctx, "Token analysis API returned failure status", map[string]interface{}{
				"status":       apiResponse.Status,
				"response":     string(resp.Body()),
				"tokenAddress": tokenAddress,
				"attempt":      i + 1,
			})
			continue
		}

		return &apiResponse.Data.Analysis, nil
	}

	return nil, lastErr
}
