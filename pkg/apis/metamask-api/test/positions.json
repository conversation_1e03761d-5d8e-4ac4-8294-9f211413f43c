{"createdAt": "2024-10-18T08:46:39.030Z", "updatedAt": "2024-10-18T08:46:39.030Z", "data": [{"protocolId": "aave-v3", "name": "Aave v3 AToken", "description": "Aave v3 defi adapter for yield-generating token", "siteUrl": "https://aave.com/", "iconUrl": "https://aave.com/favicon.ico", "positionType": "supply", "chainId": 137, "productId": "a-token", "chainName": "matic", "success": true, "tokens": [{"address": "******************************************", "name": "Aave Polygon WMATIC", "symbol": "aPolWMATIC", "decimals": 18, "balanceRaw": "1000009021595929756", "type": "protocol", "tokens": [{"address": "******************************************", "name": "Wrapped Polygon Ecosystem Token", "symbol": "WPOL", "decimals": 18, "type": "underlying", "balanceRaw": "1000009021595929756", "balance": 1.0000090215959299, "iconUrl": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png"}], "balance": 1.0000090215959299}]}, {"protocolId": "aave-v3", "name": "Aave v3 VariableDebtToken", "description": "Aave v3 defi adapter for variable interest-accruing token", "siteUrl": "https://aave.com/", "iconUrl": "https://aave.com/favicon.ico", "positionType": "borrow", "chainId": 137, "productId": "variable-debt-token", "chainName": "matic", "success": true, "tokens": [{"address": "******************************************", "name": "Aave Polygon Variable Debt USDT", "symbol": "variableDebtPolUSDT", "decimals": 6, "balanceRaw": "10001", "type": "protocol", "tokens": [{"address": "******************************************", "name": "(PoS) Tether USD", "symbol": "USDT", "decimals": 6, "type": "underlying", "balanceRaw": "10001", "balance": 0.010001, "iconUrl": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png"}], "balance": 0.010001}, {"address": "******************************************", "name": "Aave Polygon Variable Debt USDCn", "symbol": "variableDebtPolUSDCn", "decimals": 6, "balanceRaw": "10000", "type": "protocol", "tokens": [{"address": "******************************************", "name": "USD Coin", "symbol": "USDC", "decimals": 6, "type": "underlying", "balanceRaw": "10000", "balance": 0.01, "iconUrl": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png"}], "balance": 0.01}]}]}