//go:generate go-enum
package metamaskapi

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"go.opentelemetry.io/otel/attribute"
	"golang.org/x/sync/errgroup"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type Token struct {
	Address    string  `json:"address"`
	Name       string  `json:"name"`
	Symbol     string  `json:"symbol"`
	Decimals   int     `json:"decimals"`
	Type       string  `json:"type"`
	Balance    float64 `json:"balance,omitempty"`
	BalanceRaw string  `json:"balanceRaw"`
	IconUrl    string  `json:"iconUrl,omitempty"`
}

type AggregatedToken struct {
	Address    string  `json:"address"`
	Name       string  `json:"name"`
	Symbol     string  `json:"symbol"`
	Decimals   int     `json:"decimals"`
	Type       string  `json:"type"`
	Balance    float64 `json:"balance,omitempty"`
	BalanceRaw string  `json:"balanceRaw"`
	Tokens     []Token `json:"tokens,omitempty"`
}

type Position struct {
	ProtocolID            string            `json:"protocolId"`
	Name                  string            `json:"name"`
	Description           string            `json:"description"`
	SiteUrl               string            `json:"siteUrl"`
	IconUrl               string            `json:"iconUrl"`
	PositionType          PositionType      `json:"positionType"`
	ChainID               int64             `json:"chainId"`
	ProductID             string            `json:"productId"`
	ChainName             string            `json:"chainName"`
	Success               bool              `json:"success"`
	AggregatedTokenTokens []AggregatedToken `json:"tokens"`
}

// PositionType defines the type of position.
// ENUM(supply, borrow)
type PositionType string

type PositionsResponse struct {
	Positions []Position `json:"data"`
}

// Positions fetches positions from metamask
func (m *metaMaskAPI) Positions(ctx context.Context, address domain.Address,
	chains []domain.Chain) (*PositionsResponse, error) {
	ctx, span := tracing.Start(ctx, "positions.Positions")
	span.SetAttributes(
		attribute.String("address", address.String()),
		attribute.String("chains", strings.Join(lo.Map(chains, func(chain domain.Chain, _ int) string {
			return chain.ID()
		}), ",")),
	)
	defer span.End()

	ch := make(chan []Position, len(chains))
	defer close(ch)

	eg, ctx := errgroup.WithContext(ctx)
	for _, chain := range chains {
		chainID := uint64(chain.Number())
		eg.Go(func() error {
			payload := map[string]any{
				"method": "positions",
				"params": map[string]any{
					"userAddress":    address,
					"filterChainIds": []uint64{chainID},
				},
			}

			respData := &PositionsResponse{}
			resp, err := m.client.R().
				SetContext(ctx).
				SetHeader("Content-Type", "application/json").
				SetHeader("Accept", "application/json").
				SetBody(payload).
				SetResult(respData).
				Post(positionsURL)

			if err != nil {
				kglog.WarningWithDataCtx(ctx, "metamask, Positions error", map[string]interface{}{
					"err":      err.Error(),
					"response": resp.String(),
				})
				return err
			}

			if resp.StatusCode() != http.StatusOK {
				kglog.WarningWithDataCtx(ctx, "metamask, Positions error", map[string]interface{}{
					"response": resp.String(),
				})
				return fmt.Errorf("status code: %d", resp.StatusCode())
			}

			ch <- respData.Positions

			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	result := PositionsResponse{}
	for index := 0; index < len(chains); index++ {
		result.Positions = append(result.Positions, <-ch...)
	}

	return &result, nil
}

// Prices fetches token prices to a chain.
func (m *metaMaskAPI) Prices(ctx context.Context, chain domain.Chain,
	tokenAddresses []domain.Address) (map[domain.Address]float64, error) {
	ctx, span := tracing.Start(ctx, "positions.Prices")
	defer span.End()

	currency := "usd"

	tokenAddressesStr := strings.Join(lo.Map(tokenAddresses, func(address domain.Address, _ int) string {
		return address.String()
	}), ",")

	respData := map[string]map[string]float64{}
	resp, err := m.client.R().
		SetContext(ctx).
		SetPathParam("chain_id", strconv.FormatInt(chain.Number(), 10)).
		SetQueryParam("tokenAddresses", tokenAddressesStr).
		SetQueryParam("vsCurrency", currency).
		SetResult(&respData).
		Get(priceAPIURL)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "metamask, Prices error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		kglog.WarningWithDataCtx(ctx, "metamask, Prices error", map[string]interface{}{
			"response": resp.String(),
		})
		return nil, fmt.Errorf("status code: %d", resp.StatusCode())
	}

	mTokenAddressPrice := make(map[domain.Address]float64)
	for tokenAddress, mPrices := range respData {
		mTokenAddressPrice[domain.NewAddressByChain(chain, tokenAddress)] = mPrices[currency]
	}

	return mTokenAddressPrice, nil
}
