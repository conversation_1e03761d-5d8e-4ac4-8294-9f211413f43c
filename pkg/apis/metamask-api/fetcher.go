package metamaskapi

import (
	"context"
	"fmt"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

func (m *metaMaskAPI) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Ethereum, domain.Polygon, domain.Arbitrum, domain.BaseChain, domain.Optimism}
}

func (m *metaMaskAPI) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeDefi}
}

func (m *metaMaskAPI) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	// input validation
	for _, chain := range chains {
		if !lo.Contains(m.Supported<PERSON>hai<PERSON>(), chain) {
			return nil, domain.ErrUnsupportedChain
		}
	}
	for _, assetType := range types {
		if !lo.Contains(m.SupportedTypes(), assetType) {
			return nil, domain.ErrUnsupportedAssetType
		}
	}
	if len(types) != 1 {
		return nil, fmt.Errorf("only support defi for now")
	}

	resp, err := m.Positions(ctx, address, chains)
	if err != nil {
		return nil, err
	}

	defiAssets, err := processDefiAssets(ctx, resp.Positions)
	if err != nil {
		return nil, err
	}

	if err := m.processDefiTokenPrice(ctx, defiAssets); err != nil {
		return nil, err
	}

	return &domain.AggregatedAssets{
		Defi: defiAssets,
	}, nil
}

func processDefiAssets(ctx context.Context, positions []Position) ([]*domain.DefiAsset, error) {
	type key struct {
		chain      domain.Chain
		protocolID string
	}
	defiMap := map[key][]*Position{}
	for index, position := range positions {
		if position.ProductID == "" {
			continue
		}

		chain, ok := chainNumberToChain[position.ChainID]
		if !ok {
			return nil, fmt.Errorf("unknown chain id: %d", position.ChainID)
		}

		key := key{chain: chain, protocolID: position.ProtocolID}
		defiMap[key] = append(defiMap[key], &positions[index])
	}

	defiAssets := make([]*domain.DefiAsset, 0)
	for key, positions := range defiMap {
		chain, assetID := key.chain, key.protocolID

		supplyTokens := make([]*domain.DefiToken, 0)
		rewardTokens := make([]*domain.DefiToken, 0)
		borrowTokens := make([]*domain.DefiToken, 0)
		siteUrl := ""
		for _, position := range positions {
			positionType := position.PositionType
			if positionType == "" {
				continue
			}

			if position.SiteUrl != "" {
				siteUrl = position.SiteUrl
			}

			for _, aggregatedToken := range position.AggregatedTokenTokens {
				for _, token := range aggregatedToken.Tokens {
					if token.Address == "" {
						continue
					}
					defiToken := &domain.DefiToken{
						ID:      token.Address,
						Name:    token.Name,
						Symbol:  token.Symbol,
						LogoUrl: util.Ptr(token.IconUrl),
						Amount:  decimal.NewFromFloat(token.Balance),
					}

					switch positionType {
					case PositionTypeSupply:
						supplyTokens = append(supplyTokens, defiToken)
					case PositionTypeBorrow:
						borrowTokens = append(borrowTokens, defiToken)
					default:
						kglog.WarningfCtx(ctx, "unknown position type: %s", positionType)
						continue
					}
				}
			}
		}
		supplyTokenNames := lo.Map(supplyTokens, func(token *domain.DefiToken, _ int) string {
			return token.Symbol
		})
		assetName := assetID + " " + strings.Join(supplyTokenNames, "/")
		asset := domain.NewDefiAsset(chain, assetID, assetName, siteUrl)
		asset.SupplyTokens = supplyTokens
		asset.RewardTokens = rewardTokens
		asset.BorrowTokens = borrowTokens
		defiAssets = append(defiAssets, asset)
	}
	return defiAssets, nil
}

func (m *metaMaskAPI) processDefiTokenPrice(ctx context.Context, defiAssets []*domain.DefiAsset) error {

	// Aggregate token addresses by chain
	chainTokenAddresses := make(map[domain.Chain][]domain.Address)
	for _, asset := range defiAssets {

		chain := asset.Chain()
		for _, token := range asset.SupplyTokens {
			chainTokenAddresses[chain] = append(chainTokenAddresses[chain],
				domain.NewAddressByChain(asset.Chain(), token.ID))
		}
		for _, token := range asset.RewardTokens {
			chainTokenAddresses[chain] = append(chainTokenAddresses[chain],
				domain.NewAddressByChain(asset.Chain(), token.ID))
		}
		for _, token := range asset.BorrowTokens {
			chainTokenAddresses[chain] = append(chainTokenAddresses[chain],
				domain.NewAddressByChain(asset.Chain(), token.ID))
		}
	}

	// Fetch prices for each chain
	chainPrices := make(map[domain.Chain]map[domain.Address]float64)
	for chainID, tokenAddresses := range chainTokenAddresses {
		prices, err := m.Prices(ctx, chainID, tokenAddresses)
		if err != nil {
			kglog.ErrorfCtx(ctx, "failed to get prices for chain %d: %s", chainID, err)
			return err
		}
		chainPrices[chainID] = prices
	}

	// Fill prices into defiAssets
	for _, asset := range defiAssets {
		chain := asset.Chain()
		for _, token := range asset.SupplyTokens {
			token.Price = chainPrices[chain][domain.NewAddressByChain(asset.Chain(), token.ID)]
		}
		for _, token := range asset.RewardTokens {
			token.Price = chainPrices[chain][domain.NewAddressByChain(asset.Chain(), token.ID)]
		}
		for _, token := range asset.BorrowTokens {
			token.Price = chainPrices[chain][domain.NewAddressByChain(asset.Chain(), token.ID)]
		}
	}

	return nil
}
