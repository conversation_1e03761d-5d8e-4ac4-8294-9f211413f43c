//go:generate mockgen -package=metamaskapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/metamask-api -destination=metamask_api_mock.go . IMetaMask
package metamaskapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	positionsURL = "https://defi-services.metamask-institutional.io/defi-data"
	priceAPIURL  = "https://price.api.cx.metamask.io/v2/chains/{chain_id}/spot-prices"
)

var (
	timeout            = time.Duration(30 * time.Second)
	chainNumberToChain = map[int64]domain.Chain{
		domain.Ethereum.Number():  domain.Ethereum,
		domain.Polygon.Number():   domain.Polygon,
		domain.Arbitrum.Number():  domain.Arbitrum,
		domain.BaseChain.Number(): domain.BaseChain,
		domain.Optimism.Number():  domain.Optimism,
	}
)

// IMetaMask defines the interface for zerionAPI
type IMetaMask interface {
	Positions(ctx context.Context, address domain.Address, chains []domain.Chain) (*PositionsResponse, error)
	Prices(context.Context, domain.Chain, []domain.Address) (map[domain.Address]float64, error)
	domain.AssetFetcher
}

type metaMaskAPI struct {
	client resty.Client
}

var metaMaskImpl IMetaMask

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	metaMaskImpl = &metaMaskAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout),
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client) {
	metaMaskImpl = &metaMaskAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout),
	}
}

// Set metamask instance
func Set(impl IMetaMask) {
	metaMaskImpl = impl
}

// Get get mmetamask API sinMetaMask
func Get() IMetaMask {
	return metaMaskImpl
}
