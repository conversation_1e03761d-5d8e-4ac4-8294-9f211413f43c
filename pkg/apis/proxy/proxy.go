package proxy

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// NewRequestParams is the params for NewRequest
type NewRequestParams struct {
	ClientID string
	Path     string
	Method   string
	Body     interface{}
	Header   map[string]string
}

// NewRequestWithRetry creates a new http request to 3rd party service with retry
func NewRequestWithRetry(ctx context.Context, params *NewRequestParams) (*resty.Response, *code.KGError) {
	var lastResp *resty.Response
	for i := 0; i < 3; i++ {
		resp, kgErr := NewRequest(ctx, params)
		if kgErr != nil {
			return nil, kgErr
		}

		lastResp = resp
		if resp.StatusCode() < 500 {
			return resp, nil
		}
		kglog.WarningfCtx(ctx, "3rd proxy, retry %d, status code %d", i, resp.StatusCode())

		if i < 2 {
			sleepTime := time.Duration(1<<uint(i)) * time.Second
			select {
			case <-ctx.Done():
				kglog.InfofCtx(ctx, "3rd proxy, context done, return last response")
				return lastResp, nil
			case <-time.After(sleepTime):
			}
		}
	}
	return lastResp, nil
}

// NewRequest creates a new http request to 3rd party service
func NewRequest(ctx context.Context, params *NewRequestParams) (*resty.Response, *code.KGError) {
	parsedUrl, err := url.Parse(params.Path)
	if err != nil {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil)
	}

	serviceInfo, kgErr := getService(parsedUrl.Host)
	if kgErr != nil {
		return nil, kgErr
	}
	// get api key by client name
	clientName, kgErr := getClientName(ctx, params.ClientID)
	if kgErr != nil {
		return nil, kgErr
	}

	// request
	var body io.Reader
	if params.Body != nil {
		bodyStr, err := json.Marshal(params.Body)
		if err != nil {
			return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil)
		}
		body = bytes.NewBuffer([]byte(bodyStr))
	}

	req := client.R().
		SetContext(ctx).
		SetBody(body).
		SetHeader("Content-Type", "application/json")

	if len(params.Header) != 0 {
		req.SetHeaders(params.Header)
	}

	targetUrl := params.Path
	
	switch serviceInfo.APIParameter {
	case ParameterTypePath:
		targetUrl = strings.ReplaceAll(params.Path, replacePattern, serviceInfo.APIKeyMap[clientName])

	case ParameterTypeQuery:
		q := parsedUrl.Query()
		q.Add(serviceInfo.APIKeyField, serviceInfo.APIKeyMap[clientName])
		parsedUrl.RawQuery = q.Encode()
		targetUrl = parsedUrl.String()
	case ParameterTypeHeader:
		req.SetHeader(serviceInfo.APIKeyField, serviceInfo.APIKeyMap[clientName])
	case ParameterTypeBasicAuth:
		req.SetBasicAuth(serviceInfo.APIKeyMap[clientName], "")
	}

	resp, err := req.
		Execute(params.Method, targetUrl)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "3rd proxy, newRequest error", map[string]interface{}{
			"error": err.Error(),
		})
		// if err is not nil, replace the error with a generic error to avoid leaking sensitive information
		err = errors.New("failed to proxy request")
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil)
	}
	return resp, nil
}
