package proxy

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
)

const (
	replacePattern     = "{api_key_from_backend}"
	serviceEtherscan   = "etherscan"
	serviceEtherscanV2 = "etherscan-v2"
	serviceBscscan     = "bscscan"
	servicePolygonscan = "polygonscan"
	serviceArbiscan    = "arbiscan"
	serviceKccscan     = "kccscan"
	serviceOpscan      = "opscan"
	serviceBaseScan    = "base-scan"
	serviceBlockchair  = "blockchair"
	serviceInfura      = "infura"
	serviceAlchemy     = "alchemy"
	serviceOpensea     = "opensea"
	servicePoap        = "poap"
	serviceBlowfish    = "blowfish"
	serviceCoingecko   = "coingecko"
	serviceCrystal     = "crystal"
	serviceTrongrid    = "trongrid"
	serviceQuicknode   = "quicknode"
	serviceQuicknodeV2 = "quicknode-v2"
	serviceScamSniffer = "scam-sniffer"
	serviceSolanaNode  = "solana"
	servicePhantom     = "phantom"
	serviceZerion      = "zerion"
)

var (
	// key: the host of the 3rd party service
	serviceMap     = make(map[string]Service)
	hostServiceMap = make(map[string]string)
)

// Service is the struct for 3rd party service
type Service struct {
	APIKeyField  string
	APIKeyMap    map[string]string // key: stickey or kryptogo, value: api key
	Hosts        []string
	APIParameter ParameterType
}

// ParameterType is the type of the api key
type ParameterType string

const (
	// ParameterTypeNone means no api key
	ParameterTypeNone ParameterType = ""
	// ParameterTypePath means the api key is in the path
	ParameterTypePath ParameterType = "path"
	// ParameterTypeQuery means the api key is in the query
	ParameterTypeQuery ParameterType = "query"
	// ParameterTypeHeader means the api key is in the header
	ParameterTypeHeader ParameterType = "header"
	// ParameterTypeBasicAuth means the api key is used for basic authentication
	ParameterTypeBasicAuth ParameterType = "basic_auth"
)

var client *resty.Client

func init() {
	client = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(30 * time.Second)

	// example: Get https://api.etherscan.io/api?module=account&action=txlist&address=******************************************&startblock=********&endblock=********&apikey=YourApiKeyToken
	// APIParameter=Query, APIKeyField=apikey, APIKeyMap=map[kryptogo:xxx stickey:xxx], Hosts=api.etherscan.io
	// example: Get https://mainnet.infura.io/v3/{api_key_from_backend}/eth_getTransactionByHash?params=0x71adb57375b1482cd14f80fcbcfe358d2b2b4f774b39a931f84a2278b378d812
	// APIParameter=Path, APIKeyMap=map[kryptogo:xxx stickey:xxx], Hosts=mainnet.infura.io, ropsten.infura.io, kovan.infura.io, rinkeby.infura.io, goerli.infura.io, polygon-mainnet.infura.io, polygon-mumbai.infura.io
	// example: Get https://api.opensea.io/api/v2/metadata/{chain}/{contract}/{token_id}
	// APIParameter=Header, APIKeyField=X-API-KEY, APIKeyMap=map[kryptogo:xxx stickey:xxx], Hosts=api.opensea.io

	serviceMap[serviceEtherscan] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("ETHERSCAN_API_KEY"),
			"stickey":  config.GetString("STICKEY_ETHERSCAN_API_KEY"),
		},
		Hosts: []string{
			"api.etherscan.io",
		},
	}
	serviceMap[serviceEtherscanV2] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("ETHERSCAN_API_KEY"),
			"stickey":  config.GetString("STICKEY_ETHERSCAN_API_KEY"),
		},
		Hosts: []string{
			"api.etherscan.io",
		},
	}
	serviceMap[serviceBscscan] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("BSCSCAN_API_KEY"),
			"stickey":  config.GetString("STICKEY_BSCSCAN_API_KEY"),
		},
		Hosts: []string{
			"api.bscscan.com",
		},
	}
	serviceMap[servicePolygonscan] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("POLYGONSCAN_API_KEY"),
			"stickey":  config.GetString("STICKEY_POLYGONSCAN_API_KEY"),
		},
		Hosts: []string{
			"api.polygonscan.com",
		},
	}
	serviceMap[serviceArbiscan] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("ARBISCAN_API_KEY"),
			"stickey":  config.GetString("STICKEY_ARBISCAN_API_KEY"),
		},
		Hosts: []string{
			"api.arbiscan.io",
		},
	}
	serviceMap[serviceOpscan] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("OPSCAN_API_KEY"),
		},
		Hosts: []string{
			"api-optimistic.etherscan.io",
		},
	}
	serviceMap[serviceBaseScan] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("BASESCAN_API_KEY"),
		},
		Hosts: []string{
			"api.basescan.org",
		},
	}

	serviceMap[serviceKccscan] = Service{
		APIParameter: ParameterTypeNone,
		Hosts: []string{
			"api.kccscan.com",
		},
	}
	serviceMap[serviceBlockchair] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "key",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("BLOCKCHAIR_API_KEY"),
			"stickey":  config.GetString("BLOCKCHAIR_API_KEY"),
		},
		Hosts: []string{
			"api.blockchair.com",
		},
	}
	serviceMap[serviceInfura] = Service{
		APIParameter: ParameterTypePath,
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("INFURA_PROJECT_ID"),
			"stickey":  config.GetString("STICKEY_INFURA_PROJECT_ID"),
		},
		Hosts: []string{
			"mainnet.infura.io",
			"ropsten.infura.io",
			"kovan.infura.io",
			"rinkeby.infura.io",
			"goerli.infura.io",
			"polygon-mainnet.infura.io",
			"polygon-mumbai.infura.io",
		},
	}
	// legacy alchemy hosts
	alchemyHosts := []string{"eth-mainnet.alchemyapi.io", "polygon-mainnet.g.alchemyapi.io"}
	for _, chain := range domain.Chains {
		if baseURL := chain.AlchemyRpcBase(); baseURL != "" {
			u, err := url.Parse(baseURL)
			if err == nil {
				alchemyHosts = append(alchemyHosts, u.Host)
			}
		}
	}
	serviceMap[serviceAlchemy] = Service{
		APIParameter: ParameterTypePath,
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("ALCHEMY_API_KEY"),
			"stickey":  config.GetString("STICKEY_ALCHEMY_API_KEY"),
		},
		Hosts: alchemyHosts,
	}
	serviceMap[serviceOpensea] = Service{
		APIParameter: ParameterTypeHeader,
		APIKeyField:  "X-API-KEY",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("OPENSEA_API_KEY"),
			"stickey":  config.GetString("OPENSEA_API_KEY"),
		},
		Hosts: []string{
			"api.opensea.io",
		},
	}
	serviceMap[servicePoap] = Service{
		APIParameter: ParameterTypeHeader,
		APIKeyField:  "X-API-KEY",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("POAP_API_KEY"),
			"stickey":  config.GetString("POAP_API_KEY"),
		},
		Hosts: []string{
			"api.poap.tech",
		},
	}
	serviceMap[serviceBlowfish] = Service{
		APIParameter: ParameterTypeHeader,
		APIKeyField:  "X-API-KEY",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("BLOWFISH_API_KEY"),
			"stickey":  config.GetString("BLOWFISH_API_KEY"),
		},
		Hosts: []string{
			"api.blowfish.xyz",
		},
	}
	serviceMap[serviceCoingecko] = Service{
		APIParameter: ParameterTypeQuery,
		APIKeyField:  "x_cg_demo_api_key",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("COINGECKO_API_KEY"),
			"stickey":  config.GetString("COINGECKO_API_KEY"),
		},
		Hosts: []string{
			"api.coingecko.com",
		},
	}
	serviceMap[serviceCrystal] = Service{
		APIParameter: ParameterTypeHeader,
		APIKeyField:  "X-Auth-Apikey",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("CRYSTAL_API_KEY"),
			"stickey":  config.GetString("CRYSTAL_API_KEY"),
		},
		Hosts: []string{
			"apiexpert.crystalblockchain.com",
		},
	}
	serviceMap[serviceTrongrid] = Service{
		APIParameter: ParameterTypeHeader,
		APIKeyField:  "TRON-PRO-API-KEY",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("TRONGRID_API_KEY"),
			"stickey":  config.GetString("TRONGRID_API_KEY"),
		},
		Hosts: []string{
			"api.trongrid.io",
			"api.shasta.trongrid.io",
		},
	}
	serviceMap[serviceQuicknode] = Service{
		APIParameter: ParameterTypePath,
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("QUICKNODE_API_KEY"),
			"stickey":  config.GetString("QUICKNODE_API_KEY"),
		},
		Hosts: []string{
			"autumn-bold-river.tron-mainnet.quiknode.pro",
		},
	}
	quicknodeHosts := []string{}
	for _, chain := range domain.Chains {
		if baseURL := chain.QuicknodeRpcBase(); baseURL != "" {
			u, err := url.Parse(baseURL)
			if err == nil {
				quicknodeHosts = append(quicknodeHosts, u.Host)
			}
		}
	}
	serviceMap[serviceQuicknodeV2] = Service{
		APIParameter: ParameterTypePath,
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("QUICKNODE_API_KEY_V2"),
			"stickey":  config.GetString("QUICKNODE_API_KEY_V2"),
		},
		Hosts: quicknodeHosts,
	}
	serviceMap[serviceScamSniffer] = Service{
		APIParameter: ParameterTypeHeader,
		APIKeyField:  "X-API-KEY",
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("SCAM_SNIFFER_API_KEY"),
			"stickey":  config.GetString("SCAM_SNIFFER_API_KEY"),
		},
		Hosts: []string{
			"check-api.scamsniffer.io",
		},
	}
	serviceMap[serviceSolanaNode] = Service{
		APIParameter: ParameterTypeNone,
		Hosts: []string{
			"api.mainnet-beta.solana.com",
		},
	}
	serviceMap[servicePhantom] = Service{
		APIParameter: ParameterTypeNone,
		Hosts: []string{
			"api.phantom.app",
		},
	}
	serviceMap[serviceZerion] = Service{
		APIParameter: ParameterTypeBasicAuth,
		APIKeyMap: map[string]string{
			"kryptogo": config.GetString("ZERION_API_KEY_V2"),
			"stickey":  config.GetString("ZERION_API_KEY_V2"),
		},
		Hosts: []string{
			"api.zerion.io",
		},
	}

	for serviceName, service := range serviceMap {
		for _, host := range service.Hosts {
			hostServiceMap[host] = serviceName
		}
	}
}

func getService(host string) (*Service, *code.KGError) {
	if serviceName, ok := hostServiceMap[host]; !ok {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("Service not found"), nil)
	} else {
		if service, ok := serviceMap[serviceName]; !ok {
			return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("Service not found"), nil)
		} else {
			return &service, nil
		}
	}
}

func getClientName(ctx context.Context, clientID string) (string, *code.KGError) {
	app, err := application.GetApplication(ctx, clientID)
	if err != nil {
		return "", err
	}

	name := strings.ToLower(app.Name)
	if name == "stickey" {
		return "stickey", nil
	}
	return "kryptogo", nil
}
