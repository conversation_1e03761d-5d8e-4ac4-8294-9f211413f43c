package proxy

import (
	"net/http"
	"sort"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

var (
	allowedEndpoints = make([]endpoint, 0)
)

type endpoint struct {
	Method string
	Path   string
}

func init() {
	allowedEndpoints = []endpoint{
		{Method: http.MethodPost, Path: "https://apiexpert.crystalblockchain.com/monitor/tx/add"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/ethereum/v0/mainnet/scan/transactions"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/polygon/v0/mainnet/scan/transactions"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/arbitrum/v0/one/scan/transactions"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/bnb/v0/mainnet/scan/transactions"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/ethereum/v0/mainnet/scan/message"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/polygon/v0/mainnet/scan/message"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/arbitrum/v0/one/scan/message"},
		{Method: http.MethodPost, Path: "https://api.blowfish.xyz/bnb/v0/mainnet/scan/message"},
		{Method: http.MethodPost, Path: "https://eth-mainnet.alchemyapi.io/v2"},
		{Method: http.MethodPost, Path: "https://polygon-mainnet.g.alchemyapi.io/v2"},
		{Method: http.MethodPost, Path: "https://api.blockchair.com/bitcoin/push/transaction"},
		{Method: http.MethodPost, Path: "https://mainnet.infura.io/v3"},
		{Method: http.MethodPost, Path: "https://polygon-mainnet.infura.io/v3"},
		{Method: http.MethodGet, Path: "https://api.coingecko.com/api/v3/simple/price"},
		{Method: http.MethodGet, Path: "https://api.coingecko.com/api/v3/simple/token_price"},
		{Method: http.MethodGet, Path: "https://api.coingecko.com/api/v3/coins/markets"},
		{Method: http.MethodGet, Path: "https://api.poap.tech/actions/scan"},
		{Method: http.MethodGet, Path: "https://api.etherscan.io/v2/api"},
		{Method: http.MethodGet, Path: "https://api.etherscan.io/api"},
		{Method: http.MethodGet, Path: "https://api.bscscan.com/api"},
		{Method: http.MethodGet, Path: "https://api.polygonscan.com/api"},
		{Method: http.MethodGet, Path: "https://scan.kcc.io/api"},
		{Method: http.MethodGet, Path: "https://api.arbiscan.io/api"},
		{Method: http.MethodGet, Path: "https://api-optimistic.etherscan.io/api"},
		{Method: http.MethodGet, Path: "https://api.basescan.org/api"},
		{Method: http.MethodGet, Path: "https://explorer.oasys.games/api"},
		{Method: http.MethodGet, Path: "https://api.blockchair.com/bitcoin/dashboards/address"},
		{Method: http.MethodGet, Path: "https://api.blockchair.com/ethereum/dashboards/address"},
		{Method: http.MethodGet, Path: "https://api.blockchair.com/bitcoin/stats"},
		{Method: http.MethodGet, Path: "https://api.opensea.io/api/v2/collections"},
		{Method: http.MethodGet, Path: "https://api.opensea.io/api/v2/collection"},
		{Method: http.MethodGet, Path: "https://api.opensea.io/api/api/v2/orders"},
		{Method: http.MethodGet, Path: "https://api.trongrid.io/v1/accounts"},
		{Method: http.MethodGet, Path: "https://api.shasta.trongrid.io/v1/accounts"},
		{Method: http.MethodPost, Path: "https://autumn-bold-river.tron-mainnet.quiknode.pro"},
		{Method: http.MethodPost, Path: "https://check-api.scamsniffer.io/v1/checkRequest"},
		{Method: http.MethodPost, Path: "https://api.mainnet-beta.solana.com"},
		{Method: http.MethodPost, Path: "https://api.phantom.app"},
		{Method: http.MethodGet, Path: "https://api.zerion.io/v1/wallets"},
	}

	for _, chain := range domain.Chains {
		if url := chain.QuicknodeRpcBase(); url != "" {
			allowedEndpoints = append(allowedEndpoints, endpoint{Method: http.MethodPost, Path: url})
		}
		if url := chain.AlchemyRpcBase(); url != "" {
			allowedEndpoints = append(allowedEndpoints, endpoint{Method: http.MethodPost, Path: url})
		}
	}

	// sort the allowed endpoints to make sure shorter paths are checked first
	sort.Slice(allowedEndpoints, func(i, j int) bool {
		// sort method first, then path
		// sort them in ascending order
		if allowedEndpoints[i].Method != allowedEndpoints[j].Method {
			return allowedEndpoints[i].Method < allowedEndpoints[j].Method
		}
		return allowedEndpoints[i].Path < allowedEndpoints[j].Path
	})
}

// IsRequestAllowed checks if the request method and path match any allowed endpoints,
// validates the structure of the path following the prefix, rejects paths starting with "//",
// and allows paths that start with a query string "?".
func IsRequestAllowed(requestMethod, requestPath string) bool {
	for _, endpoint := range allowedEndpoints {
		if endpoint.Method == requestMethod && strings.HasPrefix(requestPath, endpoint.Path) {
			remainingPath := requestPath[len(endpoint.Path):]
			// Check if the remaining path is valid: either empty, starts with "/", not "//", or starts with "?"
			if remainingPath == "" ||
				strings.HasPrefix(remainingPath, "/?") ||
				strings.HasPrefix(remainingPath, "?") ||
				(strings.HasPrefix(remainingPath, "/") && !strings.HasPrefix(remainingPath, "//")) {
				return true
			}
		}
	}
	return false
}
