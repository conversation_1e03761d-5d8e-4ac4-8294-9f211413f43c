package goplus

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

var (
	timeout          = time.Duration(15 * time.Second)
	baseURI          = "https://api.gopluslabs.io/api/v1"
	tokenSecurityURI = "/token_security/{chain}"
)

// IGoPlus defines the interface for GoPlusAPI
type IGoPlus interface {
	IsSpamToken(ctx context.Context, chainNo int64, contractAddress string) (bool, error)
}

type goPlusAPI struct {
	client resty.Client
	rl     domain.RateLimiter
}

var goPlusObj IGoPlus

// InitDefault inits default API
func InitDefault(ratelimiter domain.RateLimiter) {
	client := resty.NewRestyClient()
	goPlusObj = &goPlusAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(baseURI).
			SetTimeout(timeout),
		rl: ratelimiter,
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client, ratelimiter domain.RateLimiter) {
	goPlusObj = &goPlusAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout),
		rl: ratelimiter,
	}
}

func Get() IGoPlus {
	return goPlusObj
}

func Set(instance IGoPlus) {
	goPlusObj = instance
}
