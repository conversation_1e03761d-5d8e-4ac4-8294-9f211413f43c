package goplus

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

func (g *goPlusAPI) IsSpamToken(ctx context.Context, chainNo int64, contractAddress string) (bool, error) {
	ctx, span := tracing.Start(ctx, "goPlusAPI.IsSpamToken")
	defer span.End()

	contractAddress = strings.ToLower(contractAddress)
	respData := struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Result  map[string]struct {
			TrustList    string `json:"trust_list"`
			IsOpenSource string `json:"is_open_source"`
			IsInDex      string `json:"is_in_dex"`
		} `json:"result"`
	}{}

	var result bool
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		// Wait for rate limiter
		err := g.rl.Wait(ctx, "isSpamToken", time.Second*10)
		if err != nil {
			return err
		}

		resp, err := g.client.R().
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "token_security")).
			SetPathParam("chain", fmt.Sprintf("%d", chainNo)).
			SetQueryParam("contract_addresses", contractAddress).
			SetResult(&respData).
			Get(tokenSecurityURI)

		if err != nil {
			return err
		}
		if resp.StatusCode() >= 400 {
			if resp.StatusCode() == 429 || respData.Code == 4029 {
				return code.ErrRateLimitExceeded
			}
			return fmt.Errorf("failed to get balance, status code: %d", resp.StatusCode())
		}

		if val, ok := respData.Result[contractAddress]; ok {
			result = val.TrustList != "1" && (val.IsOpenSource != "1" || val.IsInDex != "1")
			return nil
		}

		// If token isn't explicitly marked as spam by goplus, we consider it as not spam for now to avoid false positives
		result = false
		return nil
	})

	return result, retryErr
}
