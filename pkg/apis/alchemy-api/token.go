package alchemyapi

import (
	"context"
	"errors"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// GetTokenMetadataResp api response
type GetTokenMetadataResp struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		Decimals int    `json:"decimals"`
		Logo     string `json:"logo"`
		Name     string `json:"name"`
		Symbol   string `json:"symbol"`
	} `json:"result"`
	Error struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	} `json:"error"`
}

type getTokenMetadataReqParam struct {
	JSONRpc string   `json:"jsonrpc"`
	ID      int      `json:"id"`
	Method  string   `json:"method"`
	Params  []string `json:"params"`
}

// GetTokenMetadata Gets the metadata associated with a given NFT
func (a *alchemyAPI) GetTokenMetadata(ctx context.Context, chainID, contractAddress string) (*GetTokenMetadataResp, *resty.Response, error) {
	return a.getTokenMetadata(ctx, getAPIURL(chainID, ""), contractAddress)
}

func (a *alchemyAPI) getTokenMetadata(ctx context.Context, url string, contractAddress string) (*GetTokenMetadataResp, *resty.Response, error) {
	bodyParam := getTokenMetadataReqParam{
		JSONRpc: "2.0",
		ID:      0,
		Method:  "alchemy_getTokenMetadata",
		Params:  []string{contractAddress},
	}

	respData := GetTokenMetadataResp{}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "alchemy_getTokenMetadata")).
		SetHeader("Content-Type", "application/json").
		SetBody(bodyParam).
		SetResult(&respData).
		Post(url)

	if err != nil {
		kglog.Warning(err.Error())
		return nil, resp, err
	}
	if respData.Error.Code != 0 {
		kglog.WarningWithDataCtx(ctx, respData.Error.Message, contractAddress)
		return nil, resp, errors.New(respData.Error.Message)
	}

	return &respData, resp, nil
}
