package alchemyapi

import (
	"context"
	"fmt"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	// MaxWebhookAddresses .
	MaxWebhookAddresses = 100
)

func (a *alchemyAPI) updateWebhookAddresses(ctx context.Context, webhookID string, addressesToAdd []string, addressesToRemove []string) error {
	ctx, span := tracing.Start(ctx, "alchemyAPI.updateWebhookAddresses")
	defer span.End()

	req := map[string]interface{}{
		"webhook_id":          webhookID,
		"addresses_to_add":    addressesToAdd,
		"addresses_to_remove": addressesToRemove,
	}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "updateWebhookAddresses")).
		SetHeader("Content-Type", "application/json").
		SetHeader("X-Alchemy-Token", webhookToken).
		SetBody(req).
		Patch("https://dashboard.alchemyapi.io/api/update-webhook-addresses")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "alchemy, updateWebhookAddresses error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return err
	}
	if resp.StatusCode() != 200 {
		kglog.WarningWithDataCtx(ctx, "alchemy, updateWebhookAddresses error", map[string]interface{}{
			"response": resp.String(),
		})
		return fmt.Errorf("alchemy, updateWebhookAddresses error %d", resp.StatusCode())
	}

	return nil
}

// AddSingleWebhookAddresses add single webhook addresses, max 100 addresses
func (a *alchemyAPI) AddSingleWebhookAddresses(ctx context.Context, webhookID string, addressesToAdd []string) error {
	return a.updateWebhookAddresses(ctx, webhookID, addressesToAdd, nil)
}

// RemoveSingleWebhookAddresses remove single webhook addresses
func (a *alchemyAPI) RemoveSingleWebhookAddresses(ctx context.Context, webhookID string, addressesToRemove []string) error {
	return a.updateWebhookAddresses(ctx, webhookID, nil, addressesToRemove)
}

type getAllWebhookAddressesResp struct {
	Data       []string `json:"data"`
	Pagination struct {
		Cursors struct {
			After string `json:"after"`
		} `json:"cursors"`
		TotalCount int `json:"total_count"`
	} `json:"pagination"`
}

// GetAllWebhookAddresses get all webhook addresses
func (a *alchemyAPI) GetAllWebhookAddresses(ctx context.Context, webhookID string, limit, after int) ([]string, error) {
	ctx, span := tracing.Start(ctx, "alchemyAPI.GetAllWebhookAddresses")
	defer span.End()

	query := map[string]string{
		"webhook_id": webhookID,
		"limit":      strconv.Itoa(limit),
		"after":      strconv.Itoa(after),
	}
	respData := &getAllWebhookAddressesResp{}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "getAllWebhookAddresses")).
		SetHeader("Content-Type", "application/json").
		SetHeader("X-Alchemy-Token", webhookToken).
		SetQueryParams(query).
		SetResult(respData).
		Get("https://dashboard.alchemy.com/api/webhook-addresses")
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "alchemy, getAllWebhookAddresses error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}
	if resp.StatusCode() != 200 {
		kglog.WarningWithDataCtx(ctx, "alchemy, getAllWebhookAddresses error", map[string]interface{}{
			"response": resp.String(),
		})
		return nil, fmt.Errorf("alchemy, getAllWebhookAddresses error %d", resp.StatusCode())
	}
	kglog.InfoWithDataCtx(ctx, "alchemy, getAllWebhookAddresses", map[string]interface{}{
		"webhookID": webhookID,
		"limit":     limit,
		"after":     after,
		"total":     respData.Pagination.TotalCount,
	})
	return respData.Data, nil
}

// CreateWebhook creates a new webhook with the specified network, type, and addresses
func (a *alchemyAPI) CreateWebhook(ctx context.Context, network, webhookURL string, addresses []string) (string, error) {
	req := map[string]interface{}{
		"network":      network,
		"webhook_url":  webhookURL,
		"webhook_type": "ADDRESS_ACTIVITY",
		"addresses":    addresses,
	}
	var result struct {
		Data struct {
			ID string `json:"id"`
		} `json:"data"`
	}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "createWebhook")).
		SetHeader("accept", "application/json").
		SetHeader("Content-Type", "application/json").
		SetHeader("X-Alchemy-Token", webhookToken).
		SetBody(req).
		SetResult(&result).
		Post("https://dashboard.alchemy.com/api/create-webhook")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "alchemy, CreateWebhook error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return "", err
	}
	if resp.StatusCode() != 200 {
		kglog.WarningWithDataCtx(ctx, "alchemy, CreateWebhook error", map[string]interface{}{
			"response": resp.String(),
		})
		return "", fmt.Errorf("alchemy, CreateWebhook error %d", resp.StatusCode())
	}

	kglog.InfoWithDataCtx(ctx, "alchemy, CreateWebhook success", map[string]interface{}{
		"network":     network,
		"webhook_url": webhookURL,
		"addresses":   addresses,
		"webhook_id":  result.Data.ID,
	})
	return result.Data.ID, nil
}

// UpdateWebhookIsActive updates the is_active status of a webhook
func (a *alchemyAPI) UpdateWebhookIsActive(ctx context.Context, webhookID string, isActive bool) error {
	req := map[string]interface{}{
		"is_active":  isActive,
		"webhook_id": webhookID,
	}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "updateWebhookIsActive")).
		SetHeader("accept", "application/json").
		SetHeader("Content-Type", "application/json").
		SetHeader("X-Alchemy-Token", webhookToken).
		SetBody(req).
		Put("https://dashboard.alchemy.com/api/update-webhook")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "alchemy, UpdateWebhookIsActive error", map[string]interface{}{
			"err":        err.Error(),
			"response":   resp.String(),
			"webhook_id": webhookID,
			"is_active":  isActive,
		})
		return err
	}
	if resp.StatusCode() != 200 {
		kglog.WarningWithDataCtx(ctx, "alchemy, UpdateWebhookIsActive error", map[string]interface{}{
			"response":   resp.String(),
			"webhook_id": webhookID,
			"is_active":  isActive,
		})
		return fmt.Errorf("alchemy, UpdateWebhookIsActive error %d", resp.StatusCode())
	}

	kglog.InfoWithDataCtx(ctx, "alchemy, UpdateWebhookIsActive success", map[string]interface{}{
		"webhook_id": webhookID,
		"is_active":  isActive,
	})
	return nil
}
