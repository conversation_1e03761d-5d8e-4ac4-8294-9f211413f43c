package alchemyapi

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

const (
	directRetryLimit = 5
)

// JsonRpcResp json rpc response
type JsonRpcResp struct {
	JSONRpc string                 `json:"jsonrpc"`
	ID      int                    `json:"id"`
	Result  string                 `json:"result"`
	Error   map[string]interface{} `json:"error,omitempty"`
}

// SendRawTransaction send raw transaction
func (a *alchemyAPI) SendRawTransaction(ctx context.Context, chainID, signedData string) (string, error) {
	respData := JsonRpcResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_sendRawTransaction",
		"params":  []string{signedData},
		"id":      1,
	}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_sendRawTransaction")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))
	// kglog.DebugfCtx(ctx, "Alchemy, SendRawTransaction resp: %v, err: %v", resp.String(), err)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, SendRawTransaction error", err.Error())
		return "", err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, SendRawTransaction error", resp.String())
		return "", errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, SendRawTransaction error", respData.Error)
		return "", fmt.Errorf("%v", respData.Error)
	}

	return respData.Result, nil
}

// GetGasPrice get gas price
func (a *alchemyAPI) GetGasPrice(ctx context.Context, chainID string) (string, error) {
	respData := JsonRpcResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_gasPrice",
		"params":  nil,
		"id":      1,
	}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_gasPrice")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))
	// kglog.DebugfCtx(ctx, "Alchemy, GetGasPrice resp: %v, err: %v", resp.String(), err)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetGasPrice error", err.Error())
		return "", err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetGasPrice error", resp.String())
		return "", errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetGasPrice error", respData.Error)
		return "", fmt.Errorf("%v", respData.Error)
	}
	return respData.Result, nil
}

// Transaction transaction
type Transaction struct {
	From     string `json:"from"`
	To       string `json:"to"`
	Gas      string `json:"gas,omitempty"`
	GasPrice string `json:"gasPrice,omitempty"`
	Value    string `json:"value"`
	Data     string `json:"data,omitempty"`
	Nonce    string `json:"nonce,omitempty"`
}

// GetEstimateGas get estimate gas
func (a *alchemyAPI) GetEstimateGas(ctx context.Context, chainID string, params *Transaction) (string, error) {
	respData := JsonRpcResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_estimateGas",
		"params":  []Transaction{*params},
		"id":      1,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_estimateGas")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))
	// kglog.DebugfCtx(ctx, "Alchemy, GetEstimateGas resp: %v, err: %v", resp.String(), err)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetEstimateGas error", err.Error())
		return "", err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetEstimateGas error", resp.String())
		return "", errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetEstimateGas error", respData.Error)
		return "", fmt.Errorf("%v", respData.Error)
	}
	return respData.Result, nil
}

// GetNextNonce get next nonce
func (a *alchemyAPI) GetNextNonce(ctx context.Context, chainID, from string) (string, error) {

	respData := JsonRpcResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionCount",
		"params":  []string{from, "latest"},
		"id":      1,
	}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_getTransactionCount")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))

	// kglog.DebugfCtx(ctx, "Alchemy, GetNextNonce resp: %v, err: %v", resp.String(), err)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetNextNonce error", err.Error())
		return "", err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetNextNonce error", resp.String())
		return "", errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetNextNonce error", respData.Error)
		return "", fmt.Errorf("%v", respData.Error)
	}
	return respData.Result, nil
}

// GetTransactionReceiptResp get transaction receipt resp
type GetTransactionReceiptResp struct {
	JsonRpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		BlockHash         *string `json:"blockHash"`
		BlockNumber       string  `json:"blockNumber"`
		TransactionIndex  string  `json:"transactionIndex"`
		TransactionHash   string  `json:"transactionHash"`
		From              string  `json:"from"`
		To                string  `json:"to"`
		CumulativeGasUsed string  `json:"cumulativeGasUsed"`
		GasUsed           string  `json:"gasUsed"`
		ContractAddress   *string `json:"contractAddress"`
		Logs              []*Log  `json:"logs"`
		LogsBloom         string  `json:"logsBloom"`
		Root              string  `json:"root"`
		Status            string  `json:"status"`
		EffectiveGasPrice string  `json:"effectiveGasPrice"`
		Type              string  `json:"type"`
	} `json:"result"`
	Error map[string]interface{} `json:"error,omitempty"`
}

func (r *GetTransactionReceiptResp) toModel() *domain.TxReceipt {
	logs := make([]*domain.Log, 0)
	for _, log := range r.Result.Logs {
		logs = append(logs, &domain.Log{
			Address:         log.Address,
			Data:            log.Data,
			Topics:          log.Topics,
			TransactionHash: log.TransactionHash,
		})
	}
	return &domain.TxReceipt{
		TransactionHash:         r.Result.TransactionHash,
		From:                    r.Result.From,
		To:                      r.Result.To,
		DeployedContractAddress: r.Result.ContractAddress,
		Logs:                    logs,
		Status:                  r.Result.Status,
	}
}

// Log log
type Log struct {
	BlockHash        *string  `json:"blockHash"`
	BlockNumber      string   `json:"blockNumber"`
	TransactionIndex string   `json:"transactionIndex"`
	Address          string   `json:"address"`
	LogIndex         string   `json:"logIndex"`
	Data             string   `json:"data"`
	Removed          bool     `json:"removed"`
	Topics           []string `json:"topics"`
	TransactionHash  string   `json:"transactionHash"`
}

// GetTransactionReceipt get transaction receipt
func (a *alchemyAPI) GetTransactionReceipt(ctx context.Context, chainID, txHash string) (*GetTransactionReceiptResp, error) {
	respData := GetTransactionReceiptResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionReceipt",
		"params":  []string{txHash},
		"id":      1,
	}
	resp, err := a.client.
		SetTimeout(10*time.Second).R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_getTransactionReceipt")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))
	// kglog.DebugfCtx(ctx, "Alchemy, GetTransactionReceipt resp: %v, err: %v", resp.String(), err)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt error", resp.String())
		return nil, errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt error", respData.Error)
		return nil, fmt.Errorf("%v", respData.Error)
	}
	return &respData, nil
}

// GetTransactionReceiptWithRetry Get Transaction receipt with retry
func (a *alchemyAPI) GetTransactionReceiptWithRetry(ctx context.Context, chainID, txHash string) (*domain.TxReceipt, error) {
	cnt := 0
	waitTime := 1
	for {
		resp, err := a.GetTransactionReceipt(ctx, chainID, txHash)
		if err == nil {
			return resp.toModel(), nil
		} else if strings.Contains(err.Error(), "429") {
			kglog.InfoWithDataCtx(ctx, "Alchemy, GetTransactionReceiptWithRetry, 429 error", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
		} else {
			kglog.InfoWithDataCtx(ctx, "Alchemy, GetTransactionReceiptWithRetry, other error", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
			return nil, err
		}

		cnt++
		waitTime = waitTime * 2
		if cnt > directRetryLimit {
			kglog.ErrorWithDataCtx(ctx, "Alchemy, GetTransactionReceiptWithRetry, retry limit", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
			return nil, err
		}
		time.Sleep(time.Second * time.Duration(waitTime))
	}
}

// GetTransactionReceiptWithURL get transaction receipt
func (a *alchemyAPI) GetTransactionReceiptWithURL(ctx context.Context, url, txHash string) (*GetTransactionReceiptResp, error) {
	respData := GetTransactionReceiptResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionReceipt",
		"params":  []string{txHash},
		"id":      1,
	}
	resp, err := a.client.
		SetTimeout(10*time.Second).R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_getTransactionReceipt")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(url)
	// kglog.DebugfCtx(ctx, "Alchemy, GetTransactionReceipt resp: %v, err: %v", resp.String(), err)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt error", resp.String())
		return nil, errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt error", respData.Error)
		return nil, fmt.Errorf("%v", respData.Error)
	}
	return &respData, nil
}

// GetTransactionByHashResp get transaction by hash resp
type GetTransactionByHashResp struct {
	JsonRpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		BlockHash            string `json:"blockHash"`
		BlockNumber          string `json:"blockNumber"`
		Hash                 string `json:"hash"`
		ChainID              string `json:"chainId"`
		From                 string `json:"from"`
		Gas                  string `json:"gas"`
		GasPrice             string `json:"gasPrice"`
		Input                string `json:"input"`
		MaxFeePerGas         string `json:"maxFeePerGas"`
		MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
		Nonce                string `json:"nonce"`
		R                    string `json:"r"`
		S                    string `json:"s"`
		To                   string `json:"to"`
		TransactionIndex     string `json:"transactionIndex"`
		Type                 string `json:"type"`
		V                    string `json:"v"`
		Value                string `json:"value"`
	} `json:"result"`
	Error map[string]interface{} `json:"error,omitempty"`
}

func (r *GetTransactionByHashResp) toModel() *domain.Transaction {
	return &domain.Transaction{
		Hash:  r.Result.Hash,
		From:  r.Result.From,
		To:    r.Result.To,
		Value: r.Result.Value,
		Input: r.Result.Input,
	}
}

// GetTransactionByHash get transaction by hash
func (a *alchemyAPI) GetTransactionByHash(ctx context.Context, chainID, txHash string) (*GetTransactionByHashResp, error) {
	respData := GetTransactionByHashResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionByHash",
		"params":  []string{txHash},
		"id":      1,
	}
	resp, err := a.client.
		SetTimeout(10*time.Second).R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_getTransactionReceipt")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))
	// kglog.DebugfCtx(ctx, "Alchemy, GetTransactionByHash resp: %v, err: %v", resp.String(), err)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionByHash error", err.Error())
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionByHash error", resp.String())
		return nil, errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionByHash error", respData.Error)
		return nil, fmt.Errorf("%v", respData.Error)
	}
	return &respData, nil
}

// GetTransactionByHashWithRetry getTransactionByHash with retry
func (a *alchemyAPI) GetTransactionByHashWithRetry(ctx context.Context, chainID, txHash string) (*domain.Transaction, error) {
	cnt := 0
	waitTime := 1
	for {
		resp, err := a.GetTransactionByHash(ctx, chainID, txHash)
		if err == nil {
			return resp.toModel(), nil
		} else if strings.Contains(err.Error(), "429") {
			kglog.InfoWithDataCtx(ctx, "Alchemy, GetTransactionByHashWithRetry, 429 error", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
		} else {
			kglog.InfoWithDataCtx(ctx, "Alchemy, GetTransactionByHashWithRetry, other error", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
			return nil, err
		}

		cnt++
		waitTime = waitTime * 2
		if cnt > directRetryLimit {
			kglog.ErrorWithDataCtx(ctx, "Alchemy, GetTransactionByHashWithRetry, retry limit", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
			return nil, err
		}
		time.Sleep(time.Second * time.Duration(waitTime))
	}
}

// NftTransaction nft transaction
// type Log
type NftTransaction struct {
	FromAddress     string                   `json:"fromAddress"`
	ToAddress       string                   `json:"toAddress"`
	TokenID         string                   `json:"tokenID"`
	ERC1155Metadata *[]util.ERC1155Metadatum `json:"erc1155Metadata,omitempty"`
}

// ParseTxLog parse tx log
func ParseTxLog(log *domain.Log) *NftTransaction {
	nftTransaction := NftTransaction{}
	if log == nil || len(log.Topics) == 0 {
		return &nftTransaction
	}

	switch log.Topics[0] {
	case dbmodel.EventERC20or721Transfer: // ERC721 Transfer OR ERC20 Transfer
		if len(log.Topics) == 4 {
			// ERC721 Transfer
			nftTransaction.FromAddress = "0x" + log.Topics[1][26:]
			nftTransaction.ToAddress = "0x" + log.Topics[2][26:]
			nftTransaction.TokenID = log.Topics[3]

		} else if len(log.Topics) == 3 {
			// should check if it is ERC20 Transfer
			// ERC20 Transfer
			nftTransaction.FromAddress = "0x" + log.Topics[1][26:]
			nftTransaction.ToAddress = "0x" + log.Topics[2][26:]
		}
	case dbmodel.EventERC1155TransferSingle:
		nftTransaction.FromAddress = "0x" + log.Topics[2][26:]
		nftTransaction.ToAddress = "0x" + log.Topics[3][26:]
		nftTransaction.ERC1155Metadata = &[]util.ERC1155Metadatum{
			{
				TokenID: util.HexToDecString(log.Data[2:66]),
				Amount:  util.HexToDecString(log.Data[66:130]),
			},
		}
	case dbmodel.EventERC1155TransferBatch:
		nftTransaction.FromAddress = "0x" + log.Topics[2][26:]
		nftTransaction.ToAddress = "0x" + log.Topics[3][26:]
		metadata := util.ParseBatch(log.Data[130:])
		nftTransaction.ERC1155Metadata = &metadata
	default:
	}
	return &nftTransaction
}
