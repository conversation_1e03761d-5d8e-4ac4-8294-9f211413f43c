//go:generate mockgen -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api -package=alchemyapi -destination=alchemy_api_mock.go . IAlchemy

package alchemyapi

import (
	context "context"

	resty "github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

// IAlchemy defines the interface for alchemyAPI
type IAlchemy interface {
	BlockNumber(ctx context.Context, chainID string) (string, error)
	GetBalance(ctx context.Context, chainID, address, blockState string) (string, error)
	GetERC20Decimals(ctx context.Context, chainID, token string) (int, error)
	GetLogs(ctx context.Context, chainID, fromBlock, toBlock string) (*GetLogsResp, error)
	GetNFTs(ctx context.Context, chainID string, params *GetNFTsParams) (*GetNFTsResp, error)
	GetNFTMetadata(ctx context.Context, chainID string, params *GetNFTMetadataParams) (*GetNFTMetadataResp, error)
	GetTokenMetadata(ctx context.Context, chainID, contractAddress string) (*GetTokenMetadataResp, *resty.Response, error)
	SendRawTransaction(ctx context.Context, chainID, signedData string) (string, error)
	GetGasPrice(ctx context.Context, chainID string) (string, error)
	GetEstimateGas(ctx context.Context, chainID string, params *Transaction) (string, error)
	GetNextNonce(ctx context.Context, chainID, from string) (string, error)
	GetTransactionReceipt(ctx context.Context, chainID, txHash string) (*GetTransactionReceiptResp, error)
	GetTransactionReceiptWithRetry(ctx context.Context, chainID, txHash string) (*domain.TxReceipt, error)
	GetTransactionReceiptWithURL(ctx context.Context, url, txHash string) (*GetTransactionReceiptResp, error)
	GetTransactionByHash(ctx context.Context, chainID, txHash string) (*GetTransactionByHashResp, error)
	GetTransactionByHashWithRetry(ctx context.Context, chainID, txHash string) (*domain.Transaction, error)
	GetAssetTransfers(ctx context.Context, chainID string, params *GetAssetTransfersParams) (*GetAssetTransfersResp, *resty.Response, error)
	GetAllWebhookAddresses(ctx context.Context, webhookID string, limit, after int) ([]string, error)
	CreateWebhook(ctx context.Context, network, webhookURL string, addresses []string) (string, error)
	UpdateWebhookIsActive(ctx context.Context, webhookID string, isActive bool) error
	AddSingleWebhookAddresses(ctx context.Context, webhookID string, addressesToAdd []string) error
	RemoveSingleWebhookAddresses(ctx context.Context, webhookID string, addressesToRemove []string) error
}
