package alchemyapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// BalanceResp is the response from Alchemy API for eth_getBalance
type BalanceResp struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  string `json:"result"`
}

// GetBalance retrieves the balance of the given address
func (a *alchemyAPI) GetBalance(ctx context.Context, chainID, address, blockState string) (string, error) {
	ctx, span := tracing.Start(ctx, "[alchemy] GetBalance")
	defer span.End()

	respData := BalanceResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getBalance",
		"params":  []interface{}{address, blockState}, // address and block state
		"id":      1,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_getBalance")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))

	if err != nil {
		kglog.ErrorfCtx(ctx, "GetBalance error: %s, %s", err.Error(), resp.String())
		return "", err
	}

	return respData.Result, nil
}
