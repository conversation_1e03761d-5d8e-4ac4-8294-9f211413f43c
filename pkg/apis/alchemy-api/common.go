package alchemyapi

import (
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/samber/lo"
)

var (
	apiKey       string
	webhookToken string
	timeout      = time.Duration(15 * time.Second)
)

var SupportedChains = lo.Filter(domain.Chains, func(chain domain.Chain, _ int) bool {
	return chain.AlchemyNetwork() != ""
})

// RpcURL returns the alchemy rpc url for a given chain
func RpcURL(chainID string) string {
	return getAPIURL(chainID, "")
}

func getAPIURL(chainID string, apiName string) string {
	if chainID == "ethereum" {
		chainID = "eth"
	}
	if chainID == "polygon" {
		chainID = "matic"
	}
	chain := domain.IDToChain(chainID)
	baseURL := chain.AlchemyRpcBase()
	if baseURL == "" {
		return ""
	}
	return fmt.Sprintf("%s/%s/%s", baseURL, apiKey, apiName)
}

type alchemyAPI struct {
	client resty.Client
}

var alchemyObj IAlchemy

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	apiKey = config.GetString("ALCHEMY_API_KEY")
	if apiKey == "" {
		kglog.Error("Cannot get alchemy api key")
	}
	webhookToken = config.GetString("ALCHEMY_TOKEN")
	if webhookToken == "" {
		kglog.Error("Cannot get alchemy webhook token from secret")
	}
	alchemyObj = &alchemyAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout),
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client) {
	alchemyObj = &alchemyAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout),
	}
}

// Get returns the singleton instance of IAlchemy
func Get() IAlchemy {
	return alchemyObj
}

// Set sets the singleton instance of IAlchemy for testing
func Set(_alchemyObj IAlchemy) {
	alchemyObj = _alchemyObj
}
