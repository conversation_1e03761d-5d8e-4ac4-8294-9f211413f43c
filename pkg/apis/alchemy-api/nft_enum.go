// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package alchemyapi

import (
	"errors"
	"fmt"
)

const (
	// IsSpamTrue is a IsSpam of type true.
	IsSpamTrue IsSpam = "true"
	// IsSpamFalse is a IsSpam of type false.
	IsSpamFalse IsSpam = "false"
)

var ErrInvalidIsSpam = errors.New("not a valid IsSpam")

// String implements the Stringer interface.
func (x IsSpam) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x IsSpam) IsValid() bool {
	_, err := ParseIsSpam(string(x))
	return err == nil
}

var _IsSpamValue = map[string]IsSpam{
	"true":  IsSpamTrue,
	"false": IsSpamFalse,
}

// ParseIsSpam attempts to convert a string to a IsSpam.
func ParseIsSpam(name string) (IsSpam, error) {
	if x, ok := _IsSpamValue[name]; ok {
		return x, nil
	}
	return IsSpam(""), fmt.Errorf("%s is %w", name, ErrInvalidIsSpam)
}

const (
	// MediaFormatJpg is a MediaFormat of type jpg.
	MediaFormatJpg MediaFormat = "jpg"
	// MediaFormatGif is a MediaFormat of type gif.
	MediaFormatGif MediaFormat = "gif"
	// MediaFormatPng is a MediaFormat of type png.
	MediaFormatPng MediaFormat = "png"
)

var ErrInvalidMediaFormat = errors.New("not a valid MediaFormat")

// String implements the Stringer interface.
func (x MediaFormat) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x MediaFormat) IsValid() bool {
	_, err := ParseMediaFormat(string(x))
	return err == nil
}

var _MediaFormatValue = map[string]MediaFormat{
	"jpg": MediaFormatJpg,
	"gif": MediaFormatGif,
	"png": MediaFormatPng,
}

// ParseMediaFormat attempts to convert a string to a MediaFormat.
func ParseMediaFormat(name string) (MediaFormat, error) {
	if x, ok := _MediaFormatValue[name]; ok {
		return x, nil
	}
	return MediaFormat(""), fmt.Errorf("%s is %w", name, ErrInvalidMediaFormat)
}
