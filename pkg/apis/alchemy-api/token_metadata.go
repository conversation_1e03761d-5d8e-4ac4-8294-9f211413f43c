package alchemyapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"go.opentelemetry.io/otel/attribute"
)

type fetcherImpl struct {
	instance IAlchemy
}

// Fetcher of trust wallet github repo. Its coingecko ID will be empty
func Fetcher() domain.TokenMetadataFetcher {
	return &fetcherImpl{instance: Get()}
}

func (f *fetcherImpl) SupportedChains() []domain.Chain {
	return SupportedChains
}

func (f *fetcherImpl) FetchMetadata(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
	ctx, span := tracing.Start(ctx, "alchemyapi.FetchMetadata")
	span.SetAttributes(attribute.String("tokenID", tokenID), attribute.String("chain", chain.ID()))
	defer span.End()

	resp, _, err := f.instance.GetTokenMetadata(ctx, chain.ID(), tokenID)
	if err != nil {
		return nil, err
	}
	if resp.Result.Symbol == "" {
		return nil, domain.ErrRecordNotFound
	}
	return &domain.TokenMetadata{
		Name:     resp.Result.Name,
		Symbol:   resp.Result.Symbol,
		LogoUrl:  resp.Result.Logo,
		Decimals: uint(resp.Result.Decimals),
	}, nil
}
