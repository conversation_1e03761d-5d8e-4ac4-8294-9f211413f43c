package alchemyapi

import (
	context "context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// DecimalsResp is the response from Alchemy API for eth_getBalance
type DecimalsResp struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  string `json:"result"`
}

// GetERC20Decimals retrieves the balance of the given address for the given token
func (a *alchemyAPI) GetERC20Decimals(ctx context.Context, chainID, token string) (int, error) {
	ctx, span := tracing.Start(ctx, "[alchemy] GetERC20Decimals")
	defer span.End()

	respData := DecimalsResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_call",
		"params": []interface{}{
			map[string]interface{}{"to": token, "data": "0x313ce567"},
			"latest",
		},
		"id": 1,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_call")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))

	if err != nil {
		kglog.ErrorfCtx(ctx, "GetERC20Decimals error: %s, %s", err.Error(), resp.String())
		return 0, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetERC20Decimals error", resp.String())
		return 0, errors.New("alchemy GetERC20Decimals error")
	}

	decimals := util.HexToBigInt(respData.Result).Int64()
	return int(decimals), nil
}
