//go:generate go-enum
package alchemyapi

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/samber/lo"
	"go.opentelemetry.io/otel/attribute"
)

// GetNFTsParams .
type GetNFTsParams struct {
	Owner             string   `url:"owner"`
	PageKey           string   `url:"pageKey"`
	ContractAddresses []string `url:"contractAddresses[]"`
	WithMetadata      bool     `url:"withMetadata"`
}

// ToURLValues convert GetNFTsParams to url.Values
func (params *GetNFTsParams) ToURLValues() *url.Values {
	values := &url.Values{}
	if params.Owner != "" {
		values.Set("owner", params.Owner)
	}
	if params.PageKey != "" {
		values.Set("pageKey", params.PageKey)
	}
	if len(params.ContractAddresses) > 0 {
		for i, addr := range params.ContractAddresses {
			if i == 0 {
				values.Set("contractAddresses[]", addr)
			} else {
				values.Add("contractAddresses[]", addr)
			}
		}
	}
	if !params.WithMetadata {
		values.Set("withMetadata", "false")
	}
	return values
}

// GetNFTsResp api response
type GetNFTsResp struct {
	OwnedNfts  []Nft  `json:"ownedNfts"`
	TotalCount int    `json:"totalCount"`
	BlockHash  string `json:"blockHash"`
	PageKey    string `json:"pageKey"`
}

// MediaFormat is
// ENUM(jpg, gif, png)
type MediaFormat string

// IsSpam is
// ENUM(true, false)
type IsSpam string

// Nft struct
type Nft struct {
	Balance  string `json:"balance"`
	Contract struct {
		Address string `json:"address"`
	} `json:"contract"`
	ID struct {
		TokenID       string `json:"tokenId"`
		TokenMetadata struct {
			TokenType string `json:"tokenType"`
		} `json:"tokenMetadata"`
	} `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	TokenURI    struct {
		Raw     string `json:"raw"`
		Gateway string `json:"gateway"`
	} `json:"tokenUri"`
	Media []struct {
		Raw       string      `json:"raw"`
		Gateway   string      `json:"gateway"`
		Thumbnail string      `json:"thumbnail"`
		Format    MediaFormat `json:"format"`
		Bytes     int         `json:"bytes"`
	} `json:"media"`
	Metadata         interface{} `json:"metadata"`
	ContractMetadata interface{} `json:"contractMetadata"`
	TimeLastUpdated  string      `json:"timeLastUpdated"`
	Error            string      `json:"error"`
	AcquiredAt       struct {
		BlockTimestamp string `json:"blockTimestamp"`
		BlockNumber    string `json:"blockNumber"`
	} `json:"acquiredAt"`
	SpamInfo struct {
		IsSpam          IsSpam   `json:"isSpam"`
		Classifications []string `json:"classifications"`
	} `json:"spamInfo"`
}

// NftMetadata .
type NftMetadata struct {
	Name         interface{}  `json:"name,omitempty"`
	Description  string       `json:"description,omitempty"`
	Image        string       `json:"image,omitempty"`
	ExternalURL  string       `json:"external_url,omitempty"`
	Attributes   *interface{} `json:"attributes,omitempty"`
	AnimationURL string       `json:"animation_url,omitempty"`
}

// ContractMetadata .
type ContractMetadata struct {
	Description string `json:"description,omitempty"`
	Symbol      string `json:"symbol,omitempty"`
}

// GetNFTs Gets paging NFTs currently owned by a given address
func (a *alchemyAPI) GetNFTs(ctx context.Context, chainID string, params *GetNFTsParams) (*GetNFTsResp, error) {
	return a.getNFTs(ctx, getAPIURL(chainID, "getNFTs"), params)
}

func (a *alchemyAPI) getNFTs(ctx context.Context, url string, params *GetNFTsParams) (*GetNFTsResp, error) {
	ctx, span := tracing.Start(ctx, "nft.getNFTs")
	defer span.End()

	respData := GetNFTsResp{}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "getNFTs")).
		SetHeader("Content-Type", "application/json").
		SetQueryParamsFromValues(*params.ToURLValues()).
		SetResult(&respData).
		Get(url)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "alchemyapi, getNFTs error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}

	return &respData, nil
}

// GetNFTMetadataParams .
type GetNFTMetadataParams struct {
	ContractAddress string `url:"contractAddress"`
	TokenID         string `url:"tokenId"`
	TokenType       string `url:"tokenType"`
}

// ToURLValues convert GetNFTsParams to url.Values
func (params *GetNFTMetadataParams) ToURLValues() *url.Values {
	values := &url.Values{}
	if params.ContractAddress != "" {
		values.Set("contractAddress", params.ContractAddress)
	}
	if params.TokenID != "" {
		values.Set("tokenId", params.TokenID)
	}
	if params.TokenType != "" {
		values.Set("tokenType", params.TokenType)
	}
	return values
}

// GetNFTMetadataResp api response
type GetNFTMetadataResp struct {
	Nft
	Error string `json:"error"`
}

// GetNFTMetadata Gets the metadata associated with a given NFT
func (a *alchemyAPI) GetNFTMetadata(ctx context.Context, chainID string, params *GetNFTMetadataParams) (*GetNFTMetadataResp, error) {
	return a.getNFTMetadata(ctx, getAPIURL(chainID, "getNFTMetadata"), params)
}

var (
	acceptableErrors = []string{
		"Bad token URI", // spam nft
	}
	retryableErrors = []string{
		"Token uri responded with a non 200 response code",
		"Throttled token uri",
		"IPFS gateway timed out",
		"Centralized gateway timed out, try again with a higher tokenUri timeout",
		"ArWeave gateway timed out",
		"Internal service",
	}
	tokenNotExistError = "Token does not exist"
)

func (a *alchemyAPI) getNFTMetadata(ctx context.Context, url string, params *GetNFTMetadataParams) (*GetNFTMetadataResp, error) {
	if params == nil {
		return nil, errors.New("getNFTMetadata params is nil")
	}

	ctx, span := tracing.Start(ctx, "getNFTMetadata")
	span.SetAttributes(attribute.String("contract_address", params.ContractAddress),
		attribute.String("token_id", params.TokenID),
		attribute.String("token_type", params.TokenType))
	defer span.End()

	for cnt := 0; cnt < 3; cnt++ {
		respData := GetNFTMetadataResp{}
		resp, err := a.client.R().
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "getNFTMetadata")).
			SetHeader("Content-Type", "application/json").
			SetQueryParamsFromValues(*params.ToURLValues()).
			SetResult(&respData).
			Get(url)

		if err != nil {
			kglog.WarningWithDataCtx(ctx, "alchemy, getNFTMetadata error", map[string]interface{}{
				"err":      err.Error(),
				"response": resp.String(),
			})
			return nil, err
		}

		if respData.Error == "" || lo.Contains(acceptableErrors, respData.Error) {
			return &respData, nil
		}
		kglog.InfofCtx(ctx, "alchemy, getNFTMetadata resp error: %s", respData.Error)

		// Error handling suggested by alchemy NFT API: https://docs.alchemy.com/reference/nft-api-faq#handling-nft-api-errors
		if lo.Contains(retryableErrors, respData.Error) {
			time.Sleep(time.Second * 3)
			continue
		}
		if respData.Error == tokenNotExistError {
			return nil, code.ErrNftNotFound
		}
		return nil, fmt.Errorf("alchemy non-retryable error: %s", respData.Error)
	}
	return nil, errors.New("alchemy getNFTMetadata failed after 3 retries")
}
