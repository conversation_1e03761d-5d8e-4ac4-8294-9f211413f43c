package alchemyapi

import (
	"context"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// constants
const (
	DefaultPageLimit = 1000
)

// GetAssetTransfersParams params
type GetAssetTransfersParams struct {
	FromBlockHex string   `json:"fromBlock,omitempty"`
	FromAddress  string   `json:"fromAddress,omitempty"`
	ToAddress    string   `json:"toAddress,omitempty"`
	Category     []string `json:"category"`
	MaxCountHex  string   `json:"maxCount,omitempty"`
	PageKey      string   `json:"pageKey,omitempty"`
}

type reqParam struct {
	JSONRpc string                    `json:"jsonrpc"`
	ID      int                       `json:"id"`
	Method  string                    `json:"method"`
	Params  []GetAssetTransfersParams `json:"params"`
}

// GetAssetTransfersResp response
type GetAssetTransfersResp struct {
	ID     int `json:"id"`
	Result struct {
		Transfers []AssetTransfer `json:"transfers"`
		PageKey   string          `json:"pageKey"`
	} `json:"result"`
	Jsonrpc string `json:"jsonrpc"`
}

// AssetTransfer .
type AssetTransfer struct {
	BlockNum        string   `json:"blockNum"`
	Hash            string   `json:"hash"`
	From            string   `json:"from"`
	To              string   `json:"to"`
	Value           *float64 `json:"value"`
	Erc721TokenID   *string  `json:"erc721TokenId"`
	Erc1155Metadata []struct {
		TokenID string `json:"tokenId"`
		Value   string `json:"value"`
	} `json:"erc1155Metadata"`
	TokenID     *string `json:"tokenId"`
	Asset       string  `json:"asset"`
	Category    string  `json:"category"`
	RawContract struct {
		Value   *string `json:"value"`
		Address string  `json:"address"`
		Decimal *string `json:"decimal"`
	} `json:"rawContract"`
}

// GetAssetTransfers fetch historical transactions
func (a *alchemyAPI) GetAssetTransfers(ctx context.Context, chainID string, params *GetAssetTransfersParams) (*GetAssetTransfersResp, *resty.Response, error) {
	return a.getAssetTransfers(ctx, getAPIURL(chainID, ""), params)
}

func (a *alchemyAPI) getAssetTransfers(ctx context.Context, url string, params *GetAssetTransfersParams) (*GetAssetTransfersResp, *resty.Response, error) {
	bodyParam := reqParam{
		JSONRpc: "2.0",
		ID:      0,
		Method:  "alchemy_getAssetTransfers",
		Params:  []GetAssetTransfersParams{*params},
	}

	respData := GetAssetTransfersResp{}
	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "alchemy_getAssetTransfers")).
		SetHeader("Content-Type", "application/json").
		SetBody(bodyParam).
		SetResult(&respData).
		Post(url)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "alchemy, getAssetTransfers error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, resp, err
	}

	return &respData, resp, nil
}
