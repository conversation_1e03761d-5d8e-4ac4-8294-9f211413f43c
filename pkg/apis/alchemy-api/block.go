package alchemyapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// BlockNumberResp is the response from Alchemy API
type BlockNumberResp struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  string `json:"result"`
}

// BlockNumber get the block number
func (a *alchemyAPI) BlockNumber(ctx context.Context, chainID string) (string, error) {
	ctx, span := tracing.Start(ctx, "[alchemy] BlockNumber")
	defer span.End()

	respData := BlockNumberResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_blockNumber",
		"id":      1,
	}

	resp, err := a.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethod<PERSON>ey, "eth_blockNumber")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))

	if err != nil {
		kglog.ErrorfCtx(ctx, "BlockNumber error: %s, %s", err.Error(), resp.String())
		return "", err
	}

	return respData.Result, nil
}
