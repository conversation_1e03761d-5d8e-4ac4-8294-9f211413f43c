package alchemyapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// GetLogsResp get logs response
type GetLogsResp struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  []*Log `json:"result"`
}

// GetLogsParams get logs params
type GetLogsParams struct {
	FromBlock string        `json:"fromBlock"`
	ToBlock   string        `json:"toBlock"`
	Address   []string      `json:"address"`
	Topics    []interface{} `json:"topics"`
	BlockHash string        `json:"blockHash,omitempty"`
}

// GetLogs get logs
func (a *alchemyAPI) GetLogs(ctx context.Context, chainID, fromBlock, toBlock string) (*GetLogsResp, error) {
	ctx, span := tracing.Start(ctx, "[alchemy] GetLogs")
	defer span.End()

	params := GetLogsParams{
		FromBlock: fromBlock,
		ToBlock:   toBlock,
		Topics: []interface{}{
			[]string{
				dbmodel.EventERC20or721Transfer,
				dbmodel.EventERC1155TransferSingle,
				dbmodel.EventERC1155TransferBatch,
			},
			nil,
			nil,
			nil,
		},
	}

	respData := GetLogsResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getLogs",
		"params":  []GetLogsParams{params},
		"id":      1,
	}

	// set timeout to 30s because sometimes there may be lots of logs
	resp, err := a.client.
		SetTimeout(time.Duration(30*time.Second)).R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "eth_getLogs")).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(getAPIURL(chainID, ""))

	if err != nil {
		kglog.ErrorfCtx(ctx, "GetLogs error: %s, %s", err.Error(), resp.String())
		return nil, err
	}
	return &respData, nil
}
