package airdropclient

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type alchemyChainClient struct {
	apiKey  string
	chainID string
}

// NewAlchemyChainClient create a new alchemy chain client
func NewAlchemyChainClient(apiKey, chainID string) ChainServiceClient {
	return &alchemyChainClient{
		apiKey:  apiKey,
		chainID: chainID,
	}
}

// JsonRpcResp is the json rpc response
type JsonRpcResp struct {
	JSONRpc string                 `json:"jsonrpc"`
	ID      int                    `json:"id"`
	Result  string                 `json:"result"`
	Error   map[string]interface{} `json:"error,omitempty"`
}

func (c *alchemyChainClient) GetNextNonce(ctx context.Context, chainID, from string) (int32, error) {
	ctx, span := tracing.Start(ctx, "[alchemyChainClient] GetNextNonce")
	defer span.End()

	respData := JsonRpcResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionCount",
		"params":  []string{from, "latest"},
		"id":      1,
	}

	resp, err := resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(10*time.Second).R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(c.GetAPIURL())

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetNextNonce error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return 0, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetNextNonce error", resp.String())
		return 0, errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetNextNonce error", respData.Error)
		return 0, fmt.Errorf("%v", respData.Error)
	}

	// hex string to int
	nonce, err := hexutil.DecodeUint64(respData.Result)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetNextNonce DecodeUint64 error", err.Error())
		return 0, err
	}
	return int32(nonce), nil
}
func (c *alchemyChainClient) GetAPIURL() string {
	chain := ChainIDToSupportChain[c.chainID]

	var url string
	switch chain {
	case ChainEth, ChainEthereum:
		url = fmt.Sprintf("https://eth-mainnet.alchemyapi.io/v2/%s", c.apiKey)
	case ChainArb:
		url = fmt.Sprintf("https://arb-mainnet.alchemyapi.io/v2/%s", c.apiKey)
	case ChainPolygon:
		url = fmt.Sprintf("https://polygon-mainnet.g.alchemyapi.io/v2/%s", c.apiKey)
	case ChainBase:
		url = fmt.Sprintf("https://base-mainnet.g.alchemy.com/v2/%s", c.apiKey)
	case ChainOptimism:
		url = fmt.Sprintf("https://opt-mainnet.g.alchemy.com/v2/%s", c.apiKey)
	case ChainRin:
		url = fmt.Sprintf("https://eth-rinkeby.alchemyapi.io/v2/%s", c.apiKey)
	case ChainGoerli:
		url = fmt.Sprintf("https://eth-goerli.alchemyapi.io/v2/%s", c.apiKey)
	case ChainMumbai:
		url = fmt.Sprintf("https://polygon-mumbai.g.alchemyapi.io/v2/%s", c.apiKey)
	case ChainSepolia:
		url = fmt.Sprintf("https://eth-sepolia.g.alchemy.com/v2/%s", c.apiKey)
	case ChainBSC:
		url = "https://bsc-dataseed.binance.org"
	case ChainHolesky:
		url = "https://rpc.ankr.com/eth_holesky/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
	}
	return url
}

func (c *alchemyChainClient) GetChainID() string {
	return c.chainID
}

// GetTransactionReceiptResp is the response of get transaction receipt
type GetTransactionReceiptResp struct {
	JsonRpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		BlockHash         *string `json:"blockHash"`
		BlockNumber       string  `json:"blockNumber"`
		TransactionIndex  string  `json:"transactionIndex"`
		TransactionHash   string  `json:"transactionHash"`
		From              string  `json:"from"`
		To                string  `json:"to"`
		CumulativeGasUsed string  `json:"cumulativeGasUsed"`
		GasUsed           string  `json:"gasUsed"`
		ContractAddress   *string `json:"contractAddress"`
		Logs              []*Log  `json:"logs"`
		LogsBloom         string  `json:"logsBloom"`
		Root              string  `json:"root"`
		Status            string  `json:"status"`
		EffectiveGasPrice string  `json:"effectiveGasPrice"`
		Type              string  `json:"type"`
	} `json:"result"`
	Error map[string]interface{} `json:"error,omitempty"`
}

// Log is the log
type Log struct {
	BlockHash        *string  `json:"blockHash"`
	BlockNumber      string   `json:"blockNumber"`
	TransactionIndex string   `json:"transactionIndex"`
	Address          string   `json:"address"`
	LogIndex         string   `json:"logIndex"`
	Data             string   `json:"data"`
	Removed          bool     `json:"removed"`
	Topics           []string `json:"topics"`
	TransactionHash  string   `json:"transactionHash"`
}

func (c *alchemyChainClient) GetTransactionReceipt(ctx context.Context, hash string) (model.TxStatus, error) {
	ctx, span := tracing.Start(ctx, "[alchemyChainClient] GetTransactionReceipt")
	defer span.End()

	respData := GetTransactionReceiptResp{}
	data := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionReceipt",
		"params":  []string{hash},
		"id":      1,
	}

	resp, err := resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(10*time.Second).R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(data).
		SetResult(&respData).
		Post(c.GetAPIURL())

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return model.TxStatusUnknown, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt status code: "+strconv.Itoa(resp.StatusCode()), resp.String())
		return model.TxStatusUnknown, errors.New(resp.String())
	}
	if respData.Error != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt respData error", respData.Error)
		return model.TxStatusUnknown, fmt.Errorf("%v", respData.Error)
	}

	// not found in chain
	if respData.Result.Status == "" {
		return model.TxStatusUnknown, nil
	}

	// hex string to int
	status, err := hexutil.DecodeUint64(respData.Result.Status)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Alchemy, GetTransactionReceipt DecodeUint64 error", map[string]interface{}{
			"err":      err.Error(),
			"status":   respData.Result.Status,
			"response": resp.String(),
		})
		return model.TxStatusUnknown, err
	}
	if status == 1 {
		return model.TxStatusSuccess, nil
	} else if status == 0 {
		return model.TxStatusFail, nil
	}

	kglog.WarningWithDataCtx(ctx, "alchemyChainClient, unexpected receipt status", respData)
	return model.TxStatusUnknown, nil
}
