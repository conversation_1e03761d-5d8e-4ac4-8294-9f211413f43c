package airdropclient

import (
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

// TxStatus tx status
type TxStatus string

// TxStatus list etherscan tx status
const (
	TxStatusSuccess TxStatus = "1"
	TxStatusFail    TxStatus = "0"
	TxStatusUnknown TxStatus = ""
)

var (
	// TxStatusReceiptToStatus map tx receipt status to tx status
	TxStatusReceiptToStatus = map[string]TxStatus{
		"0x0": TxStatusFail,
		"0x1": TxStatusSuccess,
		"":    TxStatusUnknown,
	}
)

// CreateChainClient create chain client by chainID
func CreateChainClient(chainID string) (ChainServiceClient, error) {
	chain := domain.IDToChain(chainID)
	if chain == domain.Holesky {
		return NewHoleskyChainClient(), nil
	} else if chain.IsEVM() {
		apiKey := config.GetString("ALCHEMY_API_KEY")
		return NewAlchemyChainClient(apiKey, chainID), nil
	} else if chain.IsTVM() {
		return NewTrongridChainClient("", chainID), nil
	} else {
		return nil, fmt.Errorf("the chainID not supported")
	}
}
