package airdropclient

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
)

// SupportedChain list alchemy supported chain
type SupportedChain string

// SupportedChain list alchemy supported chain
const (
	ChainEth      SupportedChain = "eth"
	ChainEthereum SupportedChain = "ethereum"
	ChainArb      SupportedChain = "arb"
	ChainArbitrum SupportedChain = "arbitrum"
	ChainBase     SupportedChain = "base"
	ChainOptimism SupportedChain = "optimism"
	ChainPolygon  SupportedChain = "polygon"
	ChainMatic    SupportedChain = "matic"
	ChainRin      SupportedChain = "rin"
	ChainRinkeby  SupportedChain = "rinkeby"
	ChainGoerli   SupportedChain = "goerli"
	ChainMumbai   SupportedChain = "mumbai"
	ChainTron     SupportedChain = "tron"
	ChainShasta   SupportedChain = "shasta"
	ChainBSC      SupportedChain = "bsc"
	ChainSepolia  SupportedChain = "sepolia"
	ChainHolesky  SupportedChain = "holesky"
)

// ChainIDToSupportChain mapping chainID to chain
var ChainIDToSupportChain = map[string]SupportedChain{
	string(ChainEthereum): ChainEth,
	string(ChainEth):      ChainEth,
	string(ChainBase):     ChainBase,
	string(ChainOptimism): ChainOptimism,
	string(ChainArbitrum): ChainArb,
	string(ChainArb):      ChainArb,
	string(ChainPolygon):  ChainPolygon,
	string(ChainMatic):    ChainPolygon,
	string(ChainRinkeby):  ChainRinkeby,
	string(ChainGoerli):   ChainGoerli,
	string(ChainMumbai):   ChainMumbai,
	string(ChainTron):     ChainTron,
	string(ChainShasta):   ChainShasta,
	string(ChainBSC):      ChainBSC,
	string(ChainSepolia):  ChainSepolia,
	string(ChainHolesky):  ChainHolesky,
}

// ChainServiceClient is the interface for chain service client
type ChainServiceClient interface {
	GetNextNonce(ctx context.Context, chainID, from string) (int32, error)
	GetAPIURL() string
	GetChainID() string
	GetTransactionReceipt(context.Context, string) (model.TxStatus, error)
}
