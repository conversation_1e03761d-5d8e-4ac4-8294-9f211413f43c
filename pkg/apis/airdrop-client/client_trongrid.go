package airdropclient

import (
	"context"
	"fmt"

	"github.com/kryptogo/gotron-sdk/pkg/proto/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// ErrTransactionNotFound error transaction not found
var ErrTransactionNotFound = "transaction info not found"

type trongridChainClient struct {
	chainID string
}

// NewTrongridChainClient create a new trongrid chain client
func NewTrongridChainClient(apiKey, chainID string) ChainServiceClient {
	return &trongridChainClient{
		chainID: chainID,
	}
}

func (c *trongridChainClient) GetNextNonce(ctx context.Context, chainID, from string) (int32, error) {
	return 0, fmt.Errorf("not implemented")
}

func (c *trongridChainClient) GetAPIURL() string {
	chain := ChainIDToSupportChain[c.chainID]

	var url string
	switch chain {
	case ChainTron:
		url = "grpc.trongrid.io:50051"
	case ChainShasta:
		url = "grpc.shasta.trongrid.io:50051"
	}
	return url
}

func (c *trongridChainClient) GetChainID() string {
	return c.chainID
}

func (c *trongridChainClient) GetTransactionReceipt(ctx context.Context, txHash string) (model.TxStatus, error) {
	ctx, span := tracing.Start(ctx, "[trongridChainClient] GetTransactionReceipt")
	defer span.End()

	client, err := tron.GetClient(c.chainID)
	if err != nil {
		return model.TxStatusUnknown, err
	}

	ti, err := client.GetTransactionInfoByID(ctx, txHash)
	if err != nil && err.Error() == ErrTransactionNotFound {
		return model.TxStatusUnknown, nil
	} else if err != nil {
		kglog.WarningWithDataCtx(ctx, "trongridChainClient, Failed to get transaction info", err.Error())
		return model.TxStatusUnknown, err
	}

	if ti.Result == core.TransactionInfo_SUCESS {
		return model.TxStatusSuccess, nil
	} else if ti.Result == core.TransactionInfo_FAILED {
		return model.TxStatusFail, nil
	}

	kglog.WarningWithDataCtx(ctx, "trongridChainClient, unexpected receipt status", ti)
	return model.TxStatusUnknown, nil
}
