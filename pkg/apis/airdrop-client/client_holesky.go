package airdropclient

import (
	"context"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type holeskyChainClient struct {
	host    string
	chainID string
}

// NewHoleskyChainClient create a new holesky chain client
func NewHoleskyChainClient() ChainServiceClient {
	return &holeskyChainClient{
		host:    "https://rpc.ankr.com/eth_holesky/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7",
		chainID: "holesky",
	}

}

func (c *holeskyChainClient) GetNextNonce(ctx context.Context, chainID, from string) (int32, error) {
	ctx, span := tracing.Start(ctx, "[holeskyChainClient] GetNextNonce")
	defer span.End()

	address := common.HexToAddress(from)

	client, err := ethclient.Dial(c.GetAPIURL())
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to connect to the Ethereum client", err)
		return 0, err
	}

	nonce, err := client.PendingNonceAt(ctx, address)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to get nonce for address", err)
		return 0, err
	}
	return int32(nonce), nil
}

func (c *holeskyChainClient) GetAPIURL() string {
	chain := ChainIDToSupportChain[c.chainID]

	var url string
	switch chain {
	case ChainHolesky:
		url = c.host
	}
	return url
}

func (c *holeskyChainClient) GetChainID() string {
	return c.chainID
}

func (c *holeskyChainClient) GetTransactionReceipt(ctx context.Context, txHash string) (model.TxStatus, error) {
	ctx, span := tracing.Start(ctx, "[holeskyChainClient] GetTransactionReceipt")
	defer span.End()

	client, err := ethclient.Dial(c.GetAPIURL())
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to connect to the Ethereum client", err)
		return model.TxStatusUnknown, err
	}

	receipt, err := client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		if err.Error() == "not found" {
			return model.TxStatusUnknown, nil
		}
		kglog.WarningWithDataCtx(ctx, "Failed to get transaction receipt", err)
		return model.TxStatusUnknown, err
	}

	if receipt.Status == types.ReceiptStatusSuccessful {
		return model.TxStatusSuccess, nil
	} else if receipt.Status == types.ReceiptStatusFailed {
		return model.TxStatusFail, nil
	}

	kglog.WarningWithDataCtx(ctx, "holeskyChainClient, unexpected receipt status", receipt)
	return model.TxStatusUnknown, nil
}
