package covalenthq

import (
	"context"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type tokenType string

const (
	// TokenTypeCryptocurrency cryptocurrency
	TokenTypeCryptocurrency tokenType = "cryptocurrency"
	// TokenTypeStablecoin stablecoin
	TokenTypeStablecoin tokenType = "stablecoin"
	// TokenTypeNft nft
	TokenTypeNft tokenType = "nft"
	// TokenTypeDust dust
	TokenTypeDust tokenType = "dust"
)

// TokenItem .
type TokenItem struct {
	ContractDecimals     int32     `json:"contract_decimals"`
	ContractName         string    `json:"contract_name"`
	ContractTickerSymbol string    `json:"contract_ticker_symbol"`
	ContractAddress      string    `json:"contract_address"`
	SupportsErc          []string  `json:"supports_erc"`
	LogoURL              string    `json:"logo_url"`
	LastTransferredAt    string    `json:"last_transferred_at"`
	NativeToken          bool      `json:"native_token"`
	Type                 tokenType `json:"type"`
	IsSpam               bool      `json:"is_spam"`
	Balance              string    `json:"balance"`
	Balance24H           string    `json:"balance_24h"`
	QuoteRate            float64   `json:"quote_rate"`
	QuoteRate24H         float64   `json:"quote_rate_24h"`
	Quote                float64   `json:"quote"`
	Quote24H             float64   `json:"quote_24h"`
	NFTData              []struct {
		TokenID           string   `json:"token_id"`
		TokenBalance      string   `json:"token_balance"`
		TokenURL          string   `json:"token_url"`
		SupportsErc       []string `json:"supports_erc"`
		TokenPriceWei     string   `json:"token_price_wei"`
		TokenQuoteRateEth string   `json:"token_quote_rate_eth"`
		OriginalOwner     string   `json:"original_owner"`
		// no-nft-fetch is set to true, so the following fields are always empty.
		// ExternalData      struct {} `json:"external_data"`
		Owner        string      `json:"owner"`
		OwnerAddress string      `json:"owner_address"`
		Burned       interface{} `json:"burned"`
	} `json:"nft_data"`
}

// GetBalancesResp .
type GetBalancesResp struct {
	Data struct {
		Address       string       `json:"address"`
		UpdatedAt     string       `json:"updated_at"`
		NextUpdateAt  string       `json:"next_update_at"`
		QuoteCurrency string       `json:"quote_currency"`
		ChainID       int          `json:"chain_id"`
		Items         []*TokenItem `json:"items"`
	} `json:"data"`
	Error        bool   `json:"error"`
	ErrorMessage string `json:"error_message"`
	ErrorCode    int    `json:"error_code"`
}

// GetTokenBalances .
func (c *covalentAPI) GetTokenBalances(ctx context.Context, address string, chainID int) (*GetBalancesResp, error) {
	ctx, span := tracing.Start(ctx, "[covalenthq] GetTokenBalances")
	defer span.End()

	respData := GetBalancesResp{}

	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetPathParam("chain_id", strconv.Itoa(chainID)).
		SetPathParam("address", address).
		SetQueryParam("quote-currency", "USD").
		SetQueryParam("format", "JSON").
		SetQueryParam("nft", "false").         // don't fetch NFT data
		SetQueryParam("no-nft-fetch", "true"). // avoid fetching NFT data
		SetResult(&respData).
		Get(apiHost + getBalanceURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "covalenthq, GetTokenBalances error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}

	return &respData, nil
}

// GetNftBalances .
func (c *covalentAPI) GetNftBalances(ctx context.Context, address string, chainID int) (*GetBalancesResp, error) {
	ctx, span := tracing.Start(ctx, "[covalenthq] GetNftBalances")
	defer span.End()

	respData := GetBalancesResp{}

	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetPathParam("chain_id", strconv.Itoa(chainID)).
		SetPathParam("address", address).
		SetQueryParam("quote-currency", "USD").
		SetQueryParam("format", "JSON").
		SetQueryParam("nft", "true").          // fetch NFT data
		SetQueryParam("no-nft-fetch", "true"). // avoid fetching NFT data
		SetResult(&respData).
		Get(apiHost + getBalanceURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "covalenthq, GetNftBalances error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}

	return &respData, nil
}
