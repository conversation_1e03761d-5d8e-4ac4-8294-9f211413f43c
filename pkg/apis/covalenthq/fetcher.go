package covalenthq

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

func (c *covalentAPI) AssetFetcher(chain domain.Chain) domain.AssetFetcher {
	if chain != domain.Ronin && chain != domain.Oasys {
		return nil
	}
	return &covalentAssetFetcher{
		api:   c,
		chain: chain,
	}
}

type covalentAssetFetcher struct {
	api   ICovalent
	chain domain.Chain
}

func (f *covalentAssetFetcher) SupportedChains() []domain.Chain {
	return []domain.Chain{f.chain}
}

func (f *covalentAssetFetcher) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken}
}

func (f *covalentAssetFetcher) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	if len(chains) != 1 || chains[0] != f.chain {
		return nil, domain.ErrUnsupportedChain
	}
	if len(types) != 1 || types[0] != domain.AssetTypeToken {
		return nil, domain.ErrUnsupportedAssetType
	}

	tokenResp, tokenErr := f.api.GetTokenBalances(ctx, address.String(), int(f.chain.Number()))
	if tokenErr != nil {
		return nil, tokenErr
	}

	tokens := make([]*domain.TokenAmount, 0)
	for _, item := range tokenResp.Data.Items {
		if item.Type == TokenTypeNft {
			continue
		}
		var token domain.Token
		if item.NativeToken {
			token = f.chain.MainToken()
		} else {
			token = domain.NewToken(
				f.chain,
				item.ContractAddress,
				item.ContractName,
				item.ContractTickerSymbol,
				item.LogoURL,
				uint(item.ContractDecimals),
				!item.IsSpam,
			)
		}
		amount := util.StringBalanceToDecimal(item.Balance, int(item.ContractDecimals))
		tokens = append(tokens, &domain.TokenAmount{
			Token:  token,
			Amount: amount,
			Price:  util.Ptr(item.QuoteRate),
		})
	}
	return &domain.AggregatedAssets{
		Tokens: tokens,
	}, nil
}
