package covalenthq

import (
	"context"
	"strconv"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// TxItem .
type TxItem struct {
	BlockSignedAt    string      `json:"block_signed_at"`
	BlockHeight      uint32      `json:"block_height"`
	TxHash           string      `json:"tx_hash"`
	TxOffset         int         `json:"tx_offset"`
	Successful       bool        `json:"successful"`
	FromAddress      string      `json:"from_address"`
	FromAddressLabel string      `json:"from_address_label"`
	ToAddress        string      `json:"to_address"`
	ToAddressLabel   string      `json:"to_address_label"`
	Value            string      `json:"value"`
	ValueQuote       float64     `json:"value_quote"`
	GasOffered       int         `json:"gas_offered"`
	GasSpent         int         `json:"gas_spent"`
	GasPrice         int         `json:"gas_price"`
	FeesPaid         string      `json:"fees_paid"`
	GasQuote         float64     `json:"gas_quote"`
	GasQuoteRate     float64     `json:"gas_quote_rate"`
	LogEvents        []*LogEvent `json:"log_events"`
}

// LogEvent .
type LogEvent struct {
	BlockSignedAt              string   `json:"block_signed_at"`
	BlockHeight                int      `json:"block_height"`
	TxOffset                   int      `json:"tx_offset"`
	LogOffset                  int      `json:"log_offset"`
	TxHash                     string   `json:"tx_hash"`
	RawLogTopics               []string `json:"raw_log_topics"`
	SenderContractDecimals     int      `json:"sender_contract_decimals"`
	SenderName                 string   `json:"sender_name"`
	SenderContractTickerSymbol string   `json:"sender_contract_ticker_symbol"`
	SenderAddress              string   `json:"sender_address"`
	SenderAddressLabel         string   `json:"sender_address_label"`
	SenderLogoURL              string   `json:"sender_logo_url"`
	RawLogData                 string   `json:"raw_log_data"`
	Decoded                    struct {
		Name      string `json:"name"`
		Signature string `json:"signature"`
		Params    []struct {
			Name    string      `json:"name"`
			Type    string      `json:"type"`
			Indexed bool        `json:"indexed"`
			Decoded bool        `json:"decoded"`
			Value   interface{} `json:"value"`
		} `json:"params"`
	} `json:"decoded"`
}

// GetTransactionsResp .
type GetTransactionsResp struct {
	Data struct {
		Address       string `json:"address"`
		UpdatedAt     string `json:"updated_at"`
		NextUpdateAt  string `json:"next_update_at"`
		QuoteCurrency string `json:"quote_currency"`
		ChainID       int    `json:"chain_id"`
		ChainName     string `json:"chain_name"`
		CurrentPage   int    `json:"current_page"`
		Links         struct {
			Prev *string `json:"prev"`
			Next *string `json:"next"`
		} `json:"links"`
		Items []*TxItem `json:"items"`
	} `json:"data"`
	Error        bool   `json:"error"`
	ErrorMessage string `json:"error_message"`
	ErrorCode    int    `json:"error_code"`
}

// GetTransactions .
func (c *covalentAPI) GetTransactions(ctx context.Context, address string, chainID, pageNumber int) (*GetTransactionsResp, *resty.Response, error) {
	respData := GetTransactionsResp{}
	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetPathParam("chain_id", strconv.Itoa(chainID)).
		SetPathParam("address", address).
		SetPathParam("page_number", strconv.Itoa(pageNumber)).
		SetQueryParam("block-signed-at-asc", "true"). // sort by block_signed_at asc
		SetQueryParam("no-logs", "false").
		SetResult(&respData).
		Get(apiHost + getTransactionsURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "covalenthq, GetTransactions error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, resp, err
	}

	return &respData, resp, nil
}

// GetRoninTransactions .
func (c *covalentAPI) GetRoninTransactions(ctx context.Context, address string, pageNumber int) (*GetTransactionsResp, *resty.Response, error) {
	return c.GetTransactions(ctx, address, model.ChainNoRonin, pageNumber)
}
