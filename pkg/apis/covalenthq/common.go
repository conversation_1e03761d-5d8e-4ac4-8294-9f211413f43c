//go:generate mockgen -package=covalenthq -destination=covalent_api_mock.go . ICovalent
package covalenthq

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiHost = "https://api.covalenthq.com/v1"

	getBalanceURI      = "/{chain_id}/address/{address}/balances_v2/"
	getTransactionsURI = "/{chain_id}/address/{address}/transactions_v3/page/{page_number}/"
)

var (
	apiKey string

	timeout = time.Duration(30 * time.Second)
)

// ICovalent defines the interface for covalentAPI
type ICovalent interface {
	GetTokenBalances(ctx context.Context, address string, chainID int) (*GetBalancesResp, error)
	GetNftBalances(ctx context.Context, address string, chainID int) (*GetBalancesResp, error)
	GetTransactions(ctx context.Context, address string, chainID, pageNumber int) (*GetTransactionsResp, *resty.Response, error)
	GetRoninTransactions(ctx context.Context, address string, pageNumber int) (*GetTransactionsResp, *resty.Response, error)
	AssetFetcher(chain domain.Chain) domain.AssetFetcher
}

type covalentAPI struct {
	client resty.Client
}

var covalentObj ICovalent

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	apiKey = config.GetString("COVALENT_API_KEY")
	if apiKey == "" {
		kglog.Error("Cannot get Covalent api key")
	}
	covalentObj = &covalentAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout).
			SetBasicAuth(apiKey, ""),
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client) {
	covalentObj = &covalentAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout).
			SetBasicAuth(apiKey, ""),
	}
}

// Get get KCC API singleton
func Get() ICovalent {
	return covalentObj
}
