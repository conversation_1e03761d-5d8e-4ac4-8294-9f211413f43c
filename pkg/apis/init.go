package apis

import (
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/ankr"
	axieinfinity "github.com/kryptogo/kg-wallet-backend/pkg/apis/axieinfinity-api"
	binancepay "github.com/kryptogo/kg-wallet-backend/pkg/apis/binance-pay"
	blockchainapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchain-api"
	blockchairapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchair-api"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	covalentapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/covalenthq"
	etherscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/etherscan-api"
	feeeapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/feee-api"
	googlestorage "github.com/kryptogo/kg-wallet-backend/pkg/apis/google/storage"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/goplus"
	hyperliquidapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/hyperliquid-api"
	kccapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/kcc-api"
	lifiapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/lifi-api"
	metamaskapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/metamask-api"
	okxapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/okx-api"
	openseaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/opensea-api"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	solscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solscan-api"
	tokenanalysis "github.com/kryptogo/kg-wallet-backend/pkg/apis/token-analysis"
	tronscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api"
	zerionapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
)

// InitDefault init all default third party APIs
func InitDefault() {
	alchemyapi.InitDefault()
	axieinfinity.InitDefault()
	blockchairapi.InitDefault(cache.NewCommonRateLimiter("blockchairapi", domain.RateLimitParams{
		Rate:   30,
		Burst:  5,
		Period: time.Minute,
	}))
	blockchainapi.InitDefault(cache.NewCommonRateLimiter("blockchainapi", domain.RateLimitParams{
		Rate:   1,
		Burst:  1,
		Period: time.Second,
	}))
	coingeckoapi.InitDefault(cache.NewCommonRateLimiter("coingecko", domain.RateLimitParams{
		Rate:   1,
		Burst:  2,
		Period: time.Second,
	}))
	covalentapi.InitDefault()
	kccapi.InitDefault()
	openseaapi.InitDefault()
	solscanapi.InitDefault()
	metamaskapi.InitDefault()
	zerionapi.InitDefault()
	tronscanapi.InitDefault()
	goplus.InitDefault(cache.NewCommonRateLimiter("goplus", domain.RateLimitParams{
		Rate:   2,
		Burst:  5,
		Period: time.Second,
	}))
	feeeapi.InitDefault()
	ankr.InitDefault()
	solanaapi.InitDefault()
	etherscanapi.InitDefault()
	binancepay.InitDefault()
	hyperliquidapi.InitDefault()
	googlestorage.InitDefault(cache.NewCommonRateLimiter("googlestorage", domain.RateLimitParams{
		Rate:   5,
		Burst:  10,
		Period: time.Second,
	}))
	okxapi.InitDefault(cache.NewCommonRateLimiter("okxapi", domain.RateLimitParams{
		Rate:   1,
		Burst:  1,
		Period: time.Second,
	}))
	lifiapi.InitDefault(cache.NewCommonRateLimiter("lifiapi", domain.RateLimitParams{
		Rate:   1,
		Burst:  1,
		Period: time.Second,
	}))
	tokenanalysis.InitDefault()
}
