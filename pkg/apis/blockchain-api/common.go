//go:generate mockgen -package=blockchainapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchain-api -destination=blockchain_api_mock.go . IBlockchain
package blockchainapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiHost        = "https://blockchain.info"
	addressURI     = "/rawaddr/{address}"
	transactionURI = "/rawtx/{txHash}"
)

var (
	timeout = time.Duration(15 * time.Second)
)

type IBlockchain interface {
	GetAddressInfo(ctx context.Context, address string) (*AddressResponse, error)
	GetTransaction(ctx context.Context, txHash string) (*domain.BitcoinTransaction, error)
	domain.AssetFetcher
}

type blockchainAPI struct {
	client resty.Client
	rl     domain.RateLimiter
}

var blockchainObj IBlockchain

func InitDefault(_rl domain.RateLimiter) {
	client := resty.NewRestyClient()
	blockchainObj = &blockchainAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).
			SetTimeout(timeout),
		rl: _rl,
	}
}

func Init(client resty.Client, _rl domain.RateLimiter) {
	blockchainObj = &blockchainAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).
			SetTimeout(timeout),
		rl: _rl,
	}
}

func Get() IBlockchain {
	return blockchainObj
}

func Set(obj IBlockchain) {
	blockchainObj = obj
}

func isRateLimitExceeded(resp *resty.Response) bool {
	return resp.StatusCode() == 429
}
