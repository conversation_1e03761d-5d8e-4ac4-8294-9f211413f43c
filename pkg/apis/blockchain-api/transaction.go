package blockchainapi

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
)

type TransactionInput struct {
	PrevOut struct {
		Value uint64 `json:"value"`
		Addr  string `json:"addr"`
	} `json:"prev_out"`
}

type TransactionOutput struct {
	Value uint64 `json:"value"`
	Addr  string `json:"addr"`
}

type TransactionResponse struct {
	Hash       string              `json:"hash"`
	BlockIndex int64               `json:"block_index"`
	Fee        uint64              `json:"fee"`
	Time       uint64              `json:"time"`
	Inputs     []TransactionInput  `json:"inputs"`
	Outputs    []TransactionOutput `json:"out"`
}

func (b *blockchainAPI) GetTransaction(ctx context.Context, txHash string) (*domain.BitcoinTransaction, error) {
	respData := &TransactionResponse{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		proxyURL := util.RandomProxyURL()
		waitKey := "GetTransaction"
		client := b.client.Clone()
		if proxyURL != nil {
			waitKey = fmt.Sprintf("%s-%s", waitKey, proxyURL.String())
			client.SetTransport(&http.Transport{
				Proxy: http.ProxyURL(proxyURL),
			})
		}
		err := b.rl.Wait(ctx, waitKey, time.Second*10)
		if err != nil {
			return err
		}

		resp, err := client.R().
			SetContext(ctx).
			SetResult(respData).
			SetPathParam("txHash", txHash).
			Get(transactionURI)

		if isRateLimitExceeded(resp) {
			kglog.WarningWithDataCtx(ctx, "[blockchainapi] GetTransaction rate limit exceeded", map[string]interface{}{
				"resp":   resp.String(),
				"txHash": txHash,
			})
			return code.ErrRateLimitExceeded
		}

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[blockchainapi] GetTransaction failed", map[string]interface{}{"txHash": txHash, "err": err.Error()})
			return err
		}

		if resp.StatusCode() >= 400 {
			if resp.StatusCode() == 404 {
				return domain.ErrRecordNotFound
			}
			return fmt.Errorf("blockchainapi get transaction error: %v", resp.String())
		}
		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return respData.toDomain(), nil
}

func (t *TransactionResponse) toDomain() *domain.BitcoinTransaction {
	return &domain.BitcoinTransaction{
		BlockID: t.BlockIndex,
		Hash:    t.Hash,
		Fee:     t.Fee,
		Time:    time.Unix(int64(t.Time), 0).UTC(),
		Inputs: lo.Map(t.Inputs, func(input TransactionInput, _ int) domain.BitcoinTransactionInput {
			return domain.BitcoinTransactionInput{
				Address: input.PrevOut.Addr,
				Value:   input.PrevOut.Value,
			}
		}),
		Outputs: lo.Map(t.Outputs, func(output TransactionOutput, _ int) domain.BitcoinTransactionOutput {
			return domain.BitcoinTransactionOutput{
				Address: output.Addr,
				Value:   output.Value,
			}
		}),
	}
}
