package blockchainapi

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

type AddressResponse struct {
	Hash160       string `json:"hash160"`
	Address       string `json:"address"`
	FinalBalance  int64  `json:"final_balance"` // in satoshi
	TxCount       int64  `json:"n_tx"`
	TotalSent     int64  `json:"total_sent"`
	TotalReceived int64  `json:"total_received"`
}

func (b *blockchainAPI) GetAddressInfo(ctx context.Context, address string) (*AddressResponse, error) {
	respData := &AddressResponse{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		proxyURL := util.RandomProxyURL()
		waitKey := "GetAddressInfo"
		client := b.client.Clone()
		if proxyURL != nil {
			waitKey = fmt.Sprintf("%s-%s", waitKey, proxyURL.String())
			client.SetTransport(&http.Transport{
				Proxy: http.ProxyURL(proxyURL),
			})
		}
		err := b.rl.Wait(ctx, waitKey, time.Second*10)
		if err != nil {
			return err
		}

		resp, err := client.R().
			SetContext(ctx).
			SetResult(respData).
			SetPathParam("address", address).
			Get(addressURI)

		if isRateLimitExceeded(resp) {
			kglog.WarningWithDataCtx(ctx, "[blockchainapi] GetAddressInfo rate limit exceeded", map[string]interface{}{
				"resp":    resp.String(),
				"address": address,
			})
			return code.ErrRateLimitExceeded
		}

		if err != nil {
			if !errors.Is(err, context.DeadlineExceeded) {
				kglog.ErrorWithDataCtx(ctx, "[blockchainapi] GetAddressInfo failed", map[string]interface{}{"address": address, "err": err.Error()})
			}
			return err
		}

		if resp.StatusCode() >= 400 {
			if resp.StatusCode() == 404 {
				return domain.ErrRecordNotFound
			}
			return fmt.Errorf("blockchain.info address info error: %v", resp.String())
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return respData, nil
}

func (b *blockchainAPI) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Bitcoin}
}

func (b *blockchainAPI) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken}
}

func (b *blockchainAPI) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	resp, err := b.GetAddressInfo(ctx, address.String())
	if err != nil {
		return nil, err
	}

	assets := &domain.AggregatedAssets{
		Tokens: []*domain.TokenAmount{
			{
				Token:  domain.Bitcoin.MainToken(),
				Amount: decimal.NewFromInt(resp.FinalBalance).Div(decimal.NewFromInt(1e8)),
			},
		},
	}

	return assets, nil
}
