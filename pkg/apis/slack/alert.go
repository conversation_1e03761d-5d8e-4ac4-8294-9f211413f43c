package slackapi

import (
	"strconv"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/slack-go/slack"
)

// SendFailedOrderNotification sends a notification to the Slack channel when an order fails to be processed.
func SendFailedOrderNotification(order *model.Order, reason, symbol string) error {
	blocks := []slack.Block{
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: reason,
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Order ID", order.ID),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("User UID", order.UID),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Chain ID", order.ChainID),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Symbol", symbol),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Amount", order.Amount),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Price", order.Price),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Wallet Address", order.WalletAddress),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Created At", FormatTime(order.CreatedAt)),
			},
		},
	}
	if order.BinanceOrderID != nil {
		blocks = append(blocks, &slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Binance Order ID", *order.BinanceOrderID),
			},
		})
	}
	if order.BinanceWithdrawalID != nil {
		blocks = append(blocks, &slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Binance Withdrawal ID", *order.BinanceWithdrawalID),
			},
		})
	}
	message := &slack.WebhookMessage{
		Blocks: &slack.Blocks{
			BlockSet: blocks,
		},
	}
	return slack.PostWebhook(webhook, message)
}

// SendCashLevelNotification sends a notification to the Slack channel when the cash level is low.
func SendCashLevelNotification(currentLevel, thresholdLevel string) error {
	blocks := []slack.Block{
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: "Cash level is low\nCurrent level " + currentLevel + " USDT lower than " + thresholdLevel + " USDT\n",
			},
		},
	}
	message := &slack.WebhookMessage{
		Blocks: &slack.Blocks{
			BlockSet: blocks,
		},
	}
	return slack.PostWebhook(webhook, message)
}

// SendLargeTransactionNotification sends a notification to the Slack channel when a large transaction is detected.
func SendLargeTransactionNotification(order *model.Order, symbol string) error {
	blocks := []slack.Block{
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: "Large transaction detected",
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Order ID", order.ID),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("User UID", order.UID),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Chain ID", order.ChainID),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Symbol", symbol),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Amount", order.Amount),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Price", order.Price),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Wallet Address", order.WalletAddress),
			},
		},
		&slack.SectionBlock{
			Type: slack.MBTSection,
			Text: &slack.TextBlockObject{
				Type: slack.MarkdownType,
				Text: AssembleText("Created At", FormatTime(order.CreatedAt)),
			},
		},
	}
	message := &slack.WebhookMessage{
		Blocks: &slack.Blocks{
			BlockSet: blocks,
		},
	}
	return PostMessage(message)
}

// AssembleText assembles a text block for Slack.
func AssembleText(field, value string) string {
	return "\n>*" + field + "*\n>`" + value + "`\n"
}

// FormatTime formats a time.Time object for Slack.
func FormatTime(t time.Time) string {
	return "<!date^" + strconv.FormatInt(t.Unix(), 10) + "^{date_num} {time_secs}|" + t.Format(time.RFC3339) + ">"
}
