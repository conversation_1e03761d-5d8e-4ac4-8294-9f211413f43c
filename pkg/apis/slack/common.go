package slackapi

import (
	"errors"
	"log"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/slack-go/slack"
)

var (
	webhook string
)

func init() {
	webhook = config.GetString("SLACK_WEBHOOK_URL")
	if webhook == "" {
		log.Println("Slack webhook is empty")
	}
}

// PostMessage posts a message to webhook Slack channel
func PostMessage(message *slack.WebhookMessage) error {
	if webhook == "" {
		return errors.New("Slack webhook is empty")
	}
	return slack.PostWebhook(webhook, message)
}
