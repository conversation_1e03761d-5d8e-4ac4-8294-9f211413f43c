package okxapi

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// normalizeFeeRate ensures the fee rate is within valid bounds for OKX API
// Valid range is between 0 and 3% with up to 2 decimal places
// Reference: https://web3.okx.com/zh-hant/build/dev-docs/dex-api/dex-api-addfee
func normalizeFeeRate(rate float64) float64 {
	// OKX allows 0.01% fee rate at minimum, but require it to be explicitly set
	// For negative values, we'll treat as 0.01%
	if rate <= 0 {
		return 0.0001
	}
	// Cap at maximum 3%
	if rate > 0.03 {
		return 0.03
	}
	return rate
}

func (c *Client) getHeaders(pathWithParams, method string, body ...interface{}) map[string]string {
	timestamp := time.Now().UTC().Format("2006-01-02T15:04:05.000Z07:00")
	pathWithParamsNoBase := pathWithParams

	if len(pathWithParams) >= 19 {
		pathWithParamsNoBase = pathWithParams[19:]
	}

	var message string

	if len(body) > 0 && body[0] != nil {
		bodyBytes, err := json.Marshal(body[0])
		if err != nil {
			kglog.ErrorWithData("[okxAPI] Failed to marshal request body", map[string]interface{}{
				"body": body[0],
				"err":  err.Error(),
			})
			message = timestamp + method + pathWithParamsNoBase
		} else {
			message = timestamp + method + pathWithParamsNoBase + string(bodyBytes)
		}
	} else {
		message = timestamp + method + pathWithParamsNoBase
	}

	h := hmac.New(sha256.New, []byte(c.secretKey))
	h.Write([]byte(message))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return map[string]string{
		"Content-Type":         "application/json",
		"OK-ACCESS-KEY":        c.accessKey,
		"OK-ACCESS-SIGN":       signature,
		"OK-ACCESS-TIMESTAMP":  timestamp,
		"OK-ACCESS-PASSPHRASE": c.passphrase,
	}
}

// GetApproveTransaction gets approval transaction data
func (c *Client) GetApproveTransaction(ctx context.Context, params *ApproveTransactionParams) (*APIResponse[ApproveTransactionResponse], error) {
	urlParams := url.Values{
		"chainId":              []string{fmt.Sprintf("%d", GetOKXChainIndex(params.Chain))},
		"tokenContractAddress": []string{GetOKXTokenAddress(params.Chain, params.TokenID)},
		"approveAmount":        []string{params.ApproveAmount},
	}
	pathWithParams := fmt.Sprintf("%s/aggregator/approve-transaction?%s", apiBaseURL, urlParams.Encode())

	resp := &APIResponse[ApproveTransactionResponse]{}
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(c.getHeaders(pathWithParams, "GET")).
			SetResult(resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetApproveTransaction failed", map[string]interface{}{
				"params": params,
				"err":    err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetApproveTransaction rate limit exceeded", map[string]interface{}{
				"resp":   r.String(),
				"params": params,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			return fmt.Errorf("okx approve transaction error: %v", r.String())
		}
		if resp.Code != "0" {
			return fmt.Errorf("okx quote bridge code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}

// GetQuoteSwap gets swap quote
func (c *Client) GetQuoteSwap(ctx context.Context, params *SwapParams) (*APIResponse[QuoteResponse], error) {
	fromTokenAddr := GetOKXTokenAddress(params.Chain, params.FromTokenAddress)
	toTokenAddr := GetOKXTokenAddress(params.Chain, params.ToTokenAddress)

	params.FeeRate = normalizeFeeRate(params.FeeRate)

	urlParams := url.Values{
		"chainId":             []string{fmt.Sprintf("%d", GetOKXChainIndex(params.Chain))},
		"fromTokenAddress":    []string{fromTokenAddr},
		"toTokenAddress":      []string{toTokenAddr},
		"amount":              []string{params.Amount},
		"userWalletAddress":   []string{params.UserWalletAddress},
		"swapReceiverAddress": []string{params.SwapReceiverAddress},
		"feePercent":          []string{fmt.Sprintf("%.2f", params.FeeRate*100)},
		"referrerAddress":     []string{params.ReferrerAddress},
	}

	pathWithParams := fmt.Sprintf("%s/aggregator/quote?%s", apiBaseURL, urlParams.Encode())
	resp := &APIResponse[QuoteResponse]{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(c.getHeaders(pathWithParams, "GET")).
			SetResult(resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetQuoteSwap failed", map[string]interface{}{
				"params": params,
				"err":    err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetQuoteSwap rate limit exceeded", map[string]interface{}{
				"resp":   r.String(),
				"params": params,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			return fmt.Errorf("okx quote swap error: %v", r.String())
		}
		if resp.Code != "0" {
			return fmt.Errorf("okx quote bridge code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}

// GetBuildSwapTx gets swap transaction data
func (c *Client) GetBuildSwapTx(ctx context.Context, params *SwapParams) (*APIResponse[SwapDataResponse], error) {
	fromTokenAddr := GetOKXTokenAddress(params.Chain, params.FromTokenAddress)
	toTokenAddr := GetOKXTokenAddress(params.Chain, params.ToTokenAddress)

	params.FeeRate = normalizeFeeRate(params.FeeRate)

	urlParams := url.Values{
		"chainId":             []string{fmt.Sprintf("%d", GetOKXChainIndex(params.Chain))},
		"fromTokenAddress":    []string{fromTokenAddr},
		"toTokenAddress":      []string{toTokenAddr},
		"amount":              []string{params.Amount},
		"userWalletAddress":   []string{params.UserWalletAddress},
		"swapReceiverAddress": []string{params.SwapReceiverAddress},
		"slippage":            []string{"0.03"}, // Default slippage
		"feePercent":          []string{fmt.Sprintf("%.2f", params.FeeRate*100)},
		"referrerAddress":     []string{params.ReferrerAddress},
	}
	pathWithParams := fmt.Sprintf("%s/aggregator/swap?%s", apiBaseURL, urlParams.Encode())
	resp := &APIResponse[SwapDataResponse]{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(c.getHeaders(pathWithParams, "GET")).
			SetResult(resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetBuildSwapTx failed", map[string]interface{}{
				"params": params,
				"err":    err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetBuildSwapTx rate limit exceeded", map[string]interface{}{
				"resp":   r.String(),
				"params": params,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			return fmt.Errorf("okx build swap tx error: %v", r.String())
		}
		if resp.Code == "82000" {
			return code.ErrDexAmountTooSmall
		}
		if resp.Code != "0" {
			return fmt.Errorf("okx build swap tx code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}

// GetQuoteBridge gets cross-chain bridge quote
func (c *Client) GetQuoteBridge(ctx context.Context, params *BridgeParams) (*APIResponse[QuoteBridgeResponse], error) {
	fromTokenAddr := GetOKXTokenAddress(params.FromChain, params.FromTokenAddress)
	toTokenAddr := GetOKXTokenAddress(params.ToChain, params.ToTokenAddress)

	params.FeeRate = normalizeFeeRate(params.FeeRate)

	urlParams := url.Values{
		"fromChainId":       []string{fmt.Sprintf("%d", GetOKXChainIndex(params.FromChain))},
		"toChainId":         []string{fmt.Sprintf("%d", GetOKXChainIndex(params.ToChain))},
		"fromTokenAddress":  []string{fromTokenAddr},
		"toTokenAddress":    []string{toTokenAddr},
		"amount":            []string{params.Amount},
		"slippage":          []string{"0.03"}, // Default slippage
		"sort":              []string{"2"},    // Default sort value
		"userWalletAddress": []string{params.UserWalletAddress},
		"receiveAddress":    []string{params.ReceiveAddress},
		"feePercent":        []string{fmt.Sprintf("%.2f", params.FeeRate*100)},
	}

	pathWithParams := fmt.Sprintf("%s/cross-chain/quote?%s", apiBaseURL, urlParams.Encode())
	resp := &APIResponse[QuoteBridgeResponse]{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(c.getHeaders(pathWithParams, "GET")).
			SetResult(resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetQuoteBridge failed", map[string]interface{}{
				"params": params,
				"err":    err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetQuoteBridge rate limit exceeded", map[string]interface{}{
				"resp":   r.String(),
				"params": params,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			return fmt.Errorf("okx quote bridge error: %v", r.String())
		}
		if resp.Code != "0" {
			return fmt.Errorf("okx quote bridge code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}

// GetBuildBridgeTx gets cross-chain bridge transaction data
func (c *Client) GetBuildBridgeTx(ctx context.Context, params *BridgeParams) (*APIResponse[BuildTxResponse], error) {
	fromTokenAddr := GetOKXTokenAddress(params.FromChain, params.FromTokenAddress)
	toTokenAddr := GetOKXTokenAddress(params.ToChain, params.ToTokenAddress)

	params.FeeRate = normalizeFeeRate(params.FeeRate)

	urlParams := url.Values{
		"amount":            []string{params.Amount},
		"fromTokenAddress":  []string{fromTokenAddr},
		"fromChainId":       []string{fmt.Sprintf("%d", GetOKXChainIndex(params.FromChain))},
		"toTokenAddress":    []string{toTokenAddr},
		"toChainId":         []string{fmt.Sprintf("%d", GetOKXChainIndex(params.ToChain))},
		"userWalletAddress": []string{params.UserWalletAddress},
		"receiveAddress":    []string{params.ReceiveAddress},
		"slippage":          []string{"0.03"}, // Default slippage
		"sort":              []string{"2"},    // Default sort value
		"feePercent":        []string{fmt.Sprintf("%.2f", params.FeeRate*100)},
		"referrerAddress":   []string{params.ReferrerAddress},
	}
	pathWithParams := fmt.Sprintf("%s/cross-chain/build-tx?%s", apiBaseURL, urlParams.Encode())
	resp := &APIResponse[BuildTxResponse]{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(c.getHeaders(pathWithParams, "GET")).
			SetResult(resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetBuildBridgeTx failed", map[string]interface{}{
				"params": params,
				"err":    err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetBuildBridgeTx rate limit exceeded", map[string]interface{}{
				"resp":   r.String(),
				"params": params,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			_ = json.Unmarshal(r.Body(), resp)
			if resp.Code == "51000" || resp.Code == "82102" {
				// Parameter amount error
				return code.ErrDexAmountTooSmall
			}
			return fmt.Errorf("okx build bridge tx error: %v", r.String())
		}
		if resp.Code != "0" {
			if resp.Code == "51000" || resp.Code == "82102" {
				// Parameter amount error
				return code.ErrDexAmountTooSmall
			}
			return fmt.Errorf("okx build bridge tx code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}

// GetSupportedChains gets information about supported chains
func (c *Client) GetSupportedChains(ctx context.Context, chainId string) ([]ChainInfo, error) {
	pathWithParams := fmt.Sprintf("%s/cross-chain/supported/chain", apiBaseURL)
	if chainId != "" {
		pathWithParams = fmt.Sprintf("%s?chainId=%s", pathWithParams, chainId)
	}

	var resp struct {
		Code string      `json:"code"`
		Data []ChainInfo `json:"data"`
		Msg  string      `json:"msg"`
	}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(c.getHeaders(pathWithParams, "GET")).
			SetResult(&resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetSupportedChains failed", map[string]interface{}{
				"chainId": chainId,
				"err":     err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetSupportedChains rate limit exceeded", map[string]interface{}{
				"resp":    r.String(),
				"chainId": chainId,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			return fmt.Errorf("okx supported chains error: %v", r.String())
		}
		if resp.Code != "0" {
			return fmt.Errorf("okx supported chains code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp.Data, nil
}

// GetTransactionStatus gets the status of a cross-chain transaction
func (c *Client) GetTransactionStatus(ctx context.Context, hash string, chainId string) (*APIResponse[TransactionStatus], error) {
	urlParams := url.Values{"hash": []string{hash}}
	if chainId != "" {
		urlParams.Add("chainId", chainId)
	}

	pathWithParams := fmt.Sprintf("%s/cross-chain/status?%s", apiBaseURL, urlParams.Encode())
	resp := &APIResponse[TransactionStatus]{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		headers := c.getHeaders(pathWithParams, "GET")
		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(headers).
			SetResult(resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetTransactionStatus failed", map[string]interface{}{
				"hash":    hash,
				"chainId": chainId,
				"err":     err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetTransactionStatus rate limit exceeded", map[string]interface{}{
				"resp":    r.String(),
				"hash":    hash,
				"chainId": chainId,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			return fmt.Errorf("okx transaction status error: %v", r.String())
		}
		if resp.Code != "0" {
			return fmt.Errorf("okx transaction status code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}

// GetTokenPrice gets the latest price for tokens
func (c *Client) GetTokenPrice(ctx context.Context, requests []TokenPriceRequest) (*APIResponse[TokenPriceResponse], error) {
	endpoint := "/market/price"
	pathWithParams := fmt.Sprintf("%s%s", apiBaseURL, endpoint)

	resp := &APIResponse[TokenPriceResponse]{}
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetHeaders(c.getHeaders(pathWithParams, "POST", requests)).
			SetBody(requests).
			SetResult(resp).
			Post(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[okxAPI] GetTokenPrice failed", map[string]interface{}{
				"requests": requests,
				"err":      err.Error(),
			})
			return err
		}

		if isRateLimitExceeded(r) {
			kglog.WarningWithDataCtx(ctx, "[okxAPI] GetTokenPrice rate limit exceeded", map[string]interface{}{
				"resp":     r.String(),
				"requests": requests,
			})
			return code.ErrRateLimitExceeded
		}

		if r.StatusCode() >= 400 {
			return fmt.Errorf("okx token price error: %v", r.String())
		}
		if resp.Code != "0" {
			return fmt.Errorf("okx token price code non-zero: %s %v", resp.Code, resp.Msg)
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}
