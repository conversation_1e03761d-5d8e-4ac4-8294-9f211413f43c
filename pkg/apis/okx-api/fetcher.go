package okxapi

import (
	"context"
	"fmt"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/samber/lo"
)

// OKXPriceFetcher implements the domain.PriceFetcher interface using OKX API
type OKXPriceFetcher struct{}

// NewOKXPriceFetcher creates a new OKX price fetcher
func NewOKXPriceFetcher() *OKXPriceFetcher {
	return &OKXPriceFetcher{}
}

// PricesByContractSupportedChains returns the chains that OKX API supports for price fetching by contract addresses
func (f *OKXPriceFetcher) PricesByContractSupportedChains() []domain.Chain {
	return SupportedChains
}

// GetPrices is not implemented for OKX as it doesn't support coingecko IDs
func (f *OKXPriceFetcher) GetPrices(ctx context.Context, tokens []domain.CoingeckoID) (map[domain.CoingeckoID]domain.Price, error) {
	// This method is not implemented as OKX API doesn't support Coingecko IDs
	// The compoundPriceFetcher will use Coingecko API for this method
	return nil, fmt.Errorf("GetPrices not implemented in OKX price fetcher")
}

// GetPricesByContract fetches token prices by their contract addresses using OKX API
func (f *OKXPriceFetcher) GetPricesByContract(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]domain.Price, error) {
	if len(tokens) == 0 {
		return make(map[domain.ChainToken]domain.Price), nil
	}

	// Skip chains not supported by OKX API
	supportedTokens := make([]domain.ChainToken, 0, len(tokens))
	for _, token := range tokens {
		if lo.Contains(SupportedChains, token.Chain) {
			supportedTokens = append(supportedTokens, token)
		}
	}

	// Prepare requests for OKX API
	requests := make([]TokenPriceRequest, 0, len(supportedTokens))
	for _, token := range supportedTokens {
		tokenID := token.TokenID
		if token.IsMainToken() {
			if token.Chain.ID() == domain.Solana.ID() {
				tokenID = okxSolanaNativeToken
			} else {
				tokenID = okxEthNativeTokenAddress
			}
		}

		requests = append(requests, TokenPriceRequest{
			ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(token.Chain)),
			TokenContractAddress: tokenID,
		})
	}

	// No supported tokens
	if len(requests) == 0 {
		return make(map[domain.ChainToken]domain.Price), nil
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to fetch prices from OKX API", map[string]interface{}{
			"error": err.Error(),
		})
		return nil, err
	}

	// Process response
	result := make(map[domain.ChainToken]domain.Price)
	for _, priceData := range resp.Data {
		// Convert chain index to domain.Chain
		chainIndex, err := strconv.ParseInt(priceData.ChainIndex, 10, 64)
		if err != nil {
			kglog.WarningWithDataCtx(ctx, "Failed to parse chain index from OKX API response", map[string]interface{}{
				"chainIndex": priceData.ChainIndex,
				"error":      err.Error(),
			})
			continue
		}

		var chain domain.Chain
		for _, c := range domain.Chains {
			// Match by OKX chain index
			if GetOKXChainIndex(c) == chainIndex {
				chain = c
				break
			}
		}

		if chain == nil {
			kglog.WarningWithDataCtx(ctx, "Unsupported chain index from OKX API response", map[string]interface{}{
				"chainIndex": priceData.ChainIndex,
			})
			continue
		}

		// Handle native token address
		tokenID := priceData.TokenContractAddress
		if tokenID == okxEthNativeTokenAddress || (chain.ID() == domain.Solana.ID() && tokenID == okxSolanaNativeToken) {
			tokenID = chain.MainToken().ID()
		}

		// Parse price
		price, err := strconv.ParseFloat(priceData.Price, 64)
		if err != nil {
			kglog.WarningWithDataCtx(ctx, "Failed to parse price from OKX API response", map[string]interface{}{
				"price": priceData.Price,
				"error": err.Error(),
			})
			continue
		}

		// Add to result
		chainToken := domain.ChainToken{
			Chain:   chain,
			TokenID: tokenID,
		}
		result[chainToken] = domain.Price(price)
	}

	return result, nil
}
