package okxapi

import "github.com/kryptogo/kg-wallet-backend/domain"

type Token struct {
	Decimals             int    `json:"decimals"`
	TokenContractAddress string `json:"tokenContractAddress"`
	TokenSymbol          string `json:"tokenSymbol"`
}

type DexRouterList struct {
	// Add fields as needed
}

type QuoteCompareList struct {
	// Add fields as needed
}

type RouterResult struct {
	ChainID          string             `json:"chainId"`
	DexRouterList    []DexRouterList    `json:"dexRouterList"`
	EstimateGasFee   string             `json:"estimateGasFee"`
	FromToken        Token              `json:"fromToken"`
	FromTokenAmount  string             `json:"fromTokenAmount"`
	QuoteCompareList []QuoteCompareList `json:"quoteCompareList"`
	ToToken          Token              `json:"toToken"`
	ToTokenAmount    string             `json:"toTokenAmount"`
}

type Router struct {
	BridgeID                  int    `json:"bridgeId"`
	BridgeName                string `json:"bridgeName"`
	CrossChainFee             string `json:"crossChainFee"`
	CrossChainFeeTokenAddress string `json:"crossChainFeeTokenAddress"`
	OtherNativeFee            string `json:"otherNativeFee"`
}

type Tx struct {
	Data                 string `json:"data"`
	From                 string `json:"from"`
	Gas                  string `json:"gas"`
	GasLimit             string `json:"gasLimit"`
	GasPrice             string `json:"gasPrice"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	RandomKeyAccount     []any  `json:"randomKeyAccount"`
	To                   string `json:"to"`
	Value                string `json:"value"`
}

// Request parameter types
type ApproveTransactionParams struct {
	Chain         domain.Chain
	TokenID       string
	ApproveAmount string
}

type SwapParams struct {
	Chain               domain.Chain
	FromTokenAddress    string
	ToTokenAddress      string
	Amount              string
	UserWalletAddress   string
	SwapReceiverAddress string
	FeeRate             float64
	ReferrerAddress     string
}

type BridgeParams struct {
	Amount            string
	FromTokenAddress  string
	FromChain         domain.Chain
	ToTokenAddress    string
	ToChain           domain.Chain
	UserWalletAddress string
	ReceiveAddress    string
	FeeRate           float64
	ReferrerAddress   string
}

// ApproveTransactionResponse represents the approval transaction data
type ApproveTransactionResponse struct {
	Data               string `json:"data"`
	DexContractAddress string `json:"dexContractAddress"`
	GasLimit           string `json:"gasLimit"`
	GasPrice           string `json:"gasPrice"`
}

// SwapDataResponse represents the swap transaction data
type SwapDataResponse struct {
	RouterResult RouterResult `json:"routerResult"`
	Tx           Tx           `json:"tx"`
}

// BuildTxResponse represents the bridge transaction data
type BuildTxResponse struct {
	FromTokenAmount string `json:"fromTokenAmount"`
	MinimumReceived string `json:"minimumReceived"`
	Router          Router `json:"router"`
	ToTokenAmount   string `json:"toTokenAmount"`
	Tx              Tx     `json:"tx"`
}

// QuoteBridgeResponse represents the bridge quote data
type QuoteBridgeResponse struct {
	FromChainId     string       `json:"fromChainId"`
	FromToken       Token        `json:"fromToken"`
	FromTokenAmount string       `json:"fromTokenAmount"`
	RouterList      []RouterList `json:"routerList"`
	ToChainId       string       `json:"toChainId"`
	ToToken         Token        `json:"toToken"`
}

type RouterList struct {
	EstimateGasFee      string `json:"estimateGasFee"`
	EstimateTime        string `json:"estimateTime"`
	FromChainNetworkFee string `json:"fromChainNetworkFee"`
	FromDexRouterList   []any  `json:"fromDexRouterList"`
	MinimumReceived     string `json:"minimumReceived"`
	NeedApprove         int    `json:"needApprove"`
	Router              Router `json:"router"`
	ToChainNetworkFee   string `json:"toChainNetworkFee"`
	ToDexRouterList     []any  `json:"toDexRouterList"`
	ToTokenAmount       string `json:"toTokenAmount"`
}

// QuoteResponse represents the swap quote data
type QuoteResponse struct {
	ChainID          string             `json:"chainId"`
	DexRouterList    []DexRouterList    `json:"dexRouterList"`
	EstimateGasFee   string             `json:"estimateGasFee"`
	FromToken        Token              `json:"fromToken"`
	FromTokenAmount  string             `json:"fromTokenAmount"`
	QuoteCompareList []QuoteCompareList `json:"quoteCompareList"`
	ToToken          Token              `json:"toToken"`
	ToTokenAmount    string             `json:"toTokenAmount"`
}

// ChainInfo represents supported chain information
type ChainInfo struct {
	ChainID                string `json:"chainId"`
	ChainName              string `json:"chainName"`
	DexTokenApproveAddress string `json:"dexTokenApproveAddress"`
}

// Add these new types

type CrossChainFee struct {
	Symbol  string `json:"symbol"`
	Address string `json:"address"`
	Amount  string `json:"amount"`
}

type CrossChainInfo struct {
	// Add fields as needed based on API response
}

type TransactionStatus struct {
	FromChainId            string         `json:"fromChainId"`
	ToChainId              string         `json:"toChainId"`
	FromTxHash             string         `json:"fromTxHash"`
	ToTxHash               string         `json:"toTxHash"`
	FromAmount             string         `json:"fromAmount"`
	FromTokenAddress       string         `json:"fromTokenAddress"`
	ToAmount               string         `json:"toAmount"`
	ToTokenAddress         string         `json:"toTokenAddress"`
	ErrorMsg               string         `json:"errorMsg"`
	BridgeHash             string         `json:"bridgeHash"`
	RefundChainId          string         `json:"refundChainId"`
	RefundTokenAddress     string         `json:"refundTokenAddress"`
	RefundTxHash           string         `json:"refundTxHash"`
	SourceChainGasfee      string         `json:"sourceChainGasfee"`
	CrossChainFee          CrossChainFee  `json:"crossChainFee"`
	CrossChainInfo         CrossChainInfo `json:"crossChainInfo"`
	Memo                   string         `json:"memo"`
	DestinationChainGasfee string         `json:"destinationChainGasfee"`
	DetailStatus           string         `json:"detailStatus"`
	Status                 string         `json:"status"`
}

// TokenPriceRequest represents the request for token price data
type TokenPriceRequest struct {
	ChainIndex           string `json:"chainIndex"`
	TokenContractAddress string `json:"tokenContractAddress"`
}

// TokenPriceResponse represents the token price data
type TokenPriceResponse struct {
	ChainIndex           string `json:"chainIndex"`
	TokenContractAddress string `json:"tokenContractAddress"`
	Time                 string `json:"time"`
	Price                string `json:"price"`
}
