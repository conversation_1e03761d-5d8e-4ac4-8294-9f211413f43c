package okxapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiBaseURL               = "https://www.okx.com/api/v5/dex"
	okxEthNativeTokenAddress = "******************************************"
	okxSolanaNativeToken     = "********************************"
	defaultTimeout           = 15 * time.Second
	OkxTokenPriceBatchSize   = 100
)

// OKXChainIndices maps chain IDs to OKX API chain indices
// For EVM chains, the chain ID is usually the same as the OKX chain index
// For non-EVM chains, we need to define specific mappings
// Reference: https://web3.okx.com/zh-hans/build/dev-docs/dex-api/dex-supported-chain
var ChainToIndex = map[domain.Chain]int64{
	domain.Solana: 501, // Solana
	domain.Sui:    784, // SUI
	domain.Tron:   195, // Tron/Trx
	domain.Ton:    607, // TON
}

var SupportedChains = []domain.Chain{
	// domain.Ethereum,
	// domain.Polygon,
	// domain.BNBChain,
	// domain.Arbitrum,
	// domain.BaseChain,
	// domain.Optimism,
	domain.Solana,
	// domain.Sui,
	// domain.Tron,
	// domain.Ton,
}

// GetOKXChainIndex returns the OKX API chain index for a given chain
func GetOKXChainIndex(chain domain.Chain) int64 {
	if index, ok := ChainToIndex[chain]; ok {
		return index
	}
	// For EVM chains, return the chain's number (which is the chain ID)
	return chain.Number()
}

var (
	okxObj IOkx
)

// IOkx defines the interface for OKX API operations
type IOkx interface {
	GetApproveTransaction(ctx context.Context, params *ApproveTransactionParams) (*APIResponse[ApproveTransactionResponse], error)
	GetBuildSwapTx(ctx context.Context, params *SwapParams) (*APIResponse[SwapDataResponse], error)
	GetBuildBridgeTx(ctx context.Context, params *BridgeParams) (*APIResponse[BuildTxResponse], error)
	GetQuoteBridge(ctx context.Context, params *BridgeParams) (*APIResponse[QuoteBridgeResponse], error)
	GetQuoteSwap(ctx context.Context, params *SwapParams) (*APIResponse[QuoteResponse], error)
	GetSupportedChains(ctx context.Context, chainId string) ([]ChainInfo, error)
	GetTransactionStatus(ctx context.Context, hash string, chainId string) (*APIResponse[TransactionStatus], error)
	GetTokenPrice(ctx context.Context, requests []TokenPriceRequest) (*APIResponse[TokenPriceResponse], error)
}

type Client struct {
	client     resty.Client
	rl         domain.RateLimiter
	accessKey  string
	secretKey  string
	passphrase string
}

// InitDefault initializes the default OKX API client
func InitDefault(rl domain.RateLimiter) {
	client := resty.NewRestyClient()
	okxObj = &Client{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiBaseURL).
			SetTimeout(defaultTimeout),
		rl:         rl,
		accessKey:  config.GetString("OKX_ACCESS_KEY"),
		secretKey:  config.GetString("OKX_SECRET_KEY"),
		passphrase: config.GetString("OKX_PASSPHRASE"),
	}
}

// Init initializes with custom client for testing
func Init(client resty.Client, rl domain.RateLimiter) {
	okxObj = &Client{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiBaseURL).
			SetTimeout(defaultTimeout),
		rl:         rl,
		accessKey:  config.GetString("OKX_ACCESS_KEY"),
		secretKey:  config.GetString("OKX_SECRET_KEY"),
		passphrase: config.GetString("OKX_PASSPHRASE"),
	}
}

// Set sets the OKX API singleton
func Set(api IOkx) {
	okxObj = api
}

// Get gets the OKX API singleton
func Get() IOkx {
	return okxObj
}

// APIResponse is a generic response wrapper for OKX API
type APIResponse[T any] struct {
	Code string `json:"code"`
	Data []T    `json:"data"`
	Msg  string `json:"msg"`
}

// Helper function to check if rate limit is exceeded
func isRateLimitExceeded(resp *resty.Response) bool {
	return resp.StatusCode() == 429
}

// GetOKXTokenAddress converts internal token ID to OKX API format
// This handles native tokens for different chains (like ETH, SOL, etc.)
func GetOKXTokenAddress(chain domain.Chain, tokenID string) string {
	// If it's not a main/native token, return as is
	if tokenID != chain.MainToken().ID() {
		return tokenID
	}

	if chain.ID() == domain.Solana.ID() {
		return okxSolanaNativeToken
	}
	return okxEthNativeTokenAddress
}
