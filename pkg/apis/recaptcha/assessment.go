package recaptcha

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"

	recaptchapb "cloud.google.com/go/recaptchaenterprise/v2/apiv1/recaptchaenterprisepb"
)

const (
	// the recommended threshold is 0.5
	// however, we set it to 0.1 to reduce the risk of false positive
	// and there is still issue about the score calculation:
	// https://github.com/google/recaptcha/issues/235
	// we should find better anti-robot solution in the future because the false positive rate is too high
	riskThreshold = 0.1
)

// Check checks if the token is valid.
func Check(ctx context.Context, platform, token string) (bool, *code.KGError) {
	if platform != "ios" && platform != "android" && platform != "web" {
		return false, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("platform incorrect"), nil)
	}
	if token == "" {
		return false, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid recaptcha token"), nil)
	}

	siteKey := getRecaptchaSiteKey(platform)
	request := &recaptchapb.CreateAssessmentRequest{
		Assessment: &recaptchapb.Assessment{
			Event: &recaptchapb.Event{
				Token:   token,
				SiteKey: siteKey,
			},
		},
		Parent: fmt.Sprintf("projects/%s", projectID),
	}

	response, err := recaptchaClient.CreateAssessment(ctx, request)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "recaptchaClient.CreateAssessment failed", err.Error())
		return false, code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, err, nil)
	}

	// Check if the token is valid.
	if !response.TokenProperties.Valid {
		errStr := response.TokenProperties.InvalidReason.String()
		switch errStr {
		case "EXPIRED":
			return false, code.NewKGError(code.CaptchaExpired, http.StatusBadRequest, fmt.Errorf("captcha expired"), nil)
		default:
			kglog.WarningWithDataCtx(ctx, "[reCAPTCHA] login token invalid:", errStr)
			return false, code.NewKGError(code.CaptchaInvalid, http.StatusBadRequest, fmt.Errorf("captcha invalid"), nil)
		}
	}

	kglog.InfofCtx(ctx, "[reCAPTCHA] login risk score: %f", response.RiskAnalysis.Score)
	if response.RiskAnalysis.Score < riskThreshold {
		for _, reason := range response.RiskAnalysis.Reasons {
			kglog.WarningWithDataCtx(ctx, "[reCAPTCHA] login risk reason:", reason.String())
		}
		return false, code.NewKGError(code.CaptchaRisk, http.StatusBadRequest, fmt.Errorf("captcha risk"), nil)
	}
	return true, nil
}

func getRecaptchaSiteKey(platform string) string {
	if strings.ToLower(platform) == "web" {
		return config.GetString("WEB_RECAPTCHA_SITE_KEY")
	} else if strings.ToLower(platform) == "ios" {
		return config.GetString("IOS_RECAPTCHA_SITE_KEY")
	} else if strings.ToLower(platform) == "android" {
		return config.GetString("ANDROID_RECAPTCHA_SITE_KEY")
	}

	return ""
}
