package recaptcha

import (
	"context"

	recaptcha "cloud.google.com/go/recaptchaenterprise/v2/apiv1"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"google.golang.org/api/option"
)

var (
	projectID       string
	recaptchaClient *recaptcha.Client
)

func init() {
	projectID = config.GetString("PROJECT_ID")

	credentialFile := ""
	clientOption := option.WithCredentialsFile(credentialFile)
	ctx := context.Background()
	var err error
	recaptchaClient, err = recaptcha.NewClient(ctx, clientOption)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "recaptcha.NewClient failed", err.Error())
	}
}
