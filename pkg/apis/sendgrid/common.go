//go:generate mockgen -package=sendgrid -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid -destination=mail_mock.go . EmailClient
package sendgrid

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/sendgrid/rest"
	"github.com/sendgrid/sendgrid-go"
)

// EmailClient email client interface
type EmailClient interface {
	// Send sends email with sendgrid api
	Send(ctx context.Context, to, subject, content string) (*rest.Response, error)
	// SendEmail handle email content with template
	SendEmail(ctx context.Context, to, clientName string, emailType EmailType, params interface{}) (*rest.Response, error)
	// SendEmailWithSubject handle email content with template
	SendEmailWithSubject(ctx context.Context, to, subject string, emailType EmailType, params interface{}) (*rest.Response, error)
}

// Client sendgrid client
type Client struct {
	*sendgrid.Client
}

// NewClient create sendgrid client
func NewClient() EmailClient {
	return &Client{sendgrid.NewSendClient(config.GetString("SENDGRID_API_KEY"))}
}
