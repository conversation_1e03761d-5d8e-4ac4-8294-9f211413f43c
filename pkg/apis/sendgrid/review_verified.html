<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<html lang="en">
  <head data-id="__react-email-head"></head>
  <table
    align="center"
    width="100%"
    data-id="__react-email-container"
    role="presentation"
    cellspacing="0"
    cellpadding="0"
    border="0"
    style="
      max-width: 37.5em;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      background-color: rgb(255, 255, 255);
    "
  >
    <tbody>
      <tr style="width: 100%">
        <td>
          <table
            align="center"
            width="100%"
            data-id="__react-email-container"
            role="presentation"
            cellspacing="0"
            cellpadding="0"
            border="0"
            style="
              max-width: 37.5em;
              display: flex;
              flex-direction: column;
              align-items: center;
              width: 100%;
              border-radius: 0.75rem;
              box-sizing: border-box;
              border-width: 1px;
              border-style: solid;
              border-color: rgb(225, 233, 248);
            "
          >
            <tbody>
              <tr style="width: 100%">
                <td>
                  <table
                    align="center"
                    width="100%"
                    data-id="__react-email-container"
                    role="presentation"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                    style="
                      max-width: 37.5em;
                      display: flex;
                      flex-direction: column;
                      align-items: flex-start;
                      width: 100%;
                      border-radius: 0.75rem;
                    "
                  >
                    <tbody>
                      <tr style="width: 100%">
                        <td>
                          <img
                            data-id="react-email-img"
                            alt="logo"
                            src="https://wallet-static.kryptogo.com/public/logo/MailHeader.png"
                            style="
                              display: flex;
                              outline: none;
                              border: none;
                              text-decoration: none;
                              flex-direction: column;
                              align-items: center;
                              width: 100%;
                              background-color: rgb(27, 37, 89);
                              border-top-left-radius: 0.75rem;
                              border-top-right-radius: 0.75rem;
                            "
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table
                    align="center"
                    width="100%"
                    data-id="__react-email-container"
                    role="presentation"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                    style="
                      max-width: 37.5em;
                      display: flex;
                      flex-direction: column;
                      align-items: flex-start;
                      width: 100%;
                      background-color: rgb(255, 255, 255);
                      flex-grow: 1;
                      padding: 2.5rem;
                      padding-bottom: 0px;
                      box-sizing: border-box;
                    "
                  >
                    <tbody>
                      <tr style="width: 100%">
                        <td>
                          <a
                            data-id="react-email-link"
                            target="_blank"
                            style="
                              color: #1b2559;
                              text-decoration: none;
                              font-weight: bold;
                              font-size: 18px;
                              margin-top: 0;
                              line-height: 120%;
                              letter-spacing: 0.18px;
                              font-family: DM Sans, sans-serif;
                            "
                            >Your identity has been verified!</a
                          >
                          <p
                            data-id="react-email-text"
                            style="
                              font-size: 16px;
                              line-height: 150%;
                              margin: 16px 0;
                              font-weight: 400;
                              font-family: DM Sans, sans-serif;
                              font-style: normal;
                              margin-bottom: 0;
                              color: #1b2559;
                              overflow-wrap: break-word;
                            "
                          >
                            Hi {{.legal_name}}, <br />
                            Congratulations! We're excited to welcome you to the
                            {{.org_name}} community. Sign in to your account to
                            begin your crypto journey today.
                          </p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table
                    align="center"
                    width="100%"
                    data-id="__react-email-container"
                    role="presentation"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                    style="
                      max-width: 37.5em;
                      width: 200px;
                      margin-top: 2rem;
                      cursor: pointer;
                    "
                  ></table>
                  <table
                    align="center"
                    width="100%"
                    data-id="__react-email-container"
                    role="presentation"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                    style="
                      max-width: 37.5em;
                      color: rgb(27, 37, 89);
                      margin-top: 1rem;
                      padding-left: 2.5rem;
                      padding-right: 2.5rem;
                    "
                  >
                    <tbody>
                      <tr style="width: 100%">
                        <td>
                          <p
                            data-id="react-email-text"
                            style="
                              font-size: 1rem;
                              line-height: 150%;
                              margin: 16px 0;
                              font-weight: 400;
                              font-family: DM Sans, sans-serif;
                              font-style: normal;
                              margin-bottom: 2.5rem;
                              color: #1b2559;
                              overflow-wrap: break-word;
                              margin-top: 3rem;
                            "
                          >
                            Team {{.org_name}}
                          </p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table
                    align="center"
                    width="100%"
                    data-id="__react-email-container"
                    role="presentation"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                    style="
                      max-width: 37.5em;
                      width: 100%;
                      height: 0.5px;
                      background-color: rgb(225, 233, 248);
                    "
                  >
                    <tbody>
                      <tr style="width: 100%">
                        <td></td>
                      </tr>
                    </tbody>
                  </table>
                  <table
                    align="center"
                    width="100%"
                    data-id="__react-email-container"
                    role="presentation"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                    style="max-width: 37.5em; padding: 2.5rem"
                  >
                    <tbody>
                      <tr style="width: 100%">
                        <td>
                          <table
                            align="center"
                            width="100%"
                            data-id="__react-email-container"
                            role="presentation"
                            cellspacing="0"
                            cellpadding="0"
                            border="0"
                            style="
                              max-width: 37.5em;
                              color: rgb(104, 118, 159);
                              display: flex;
                            "
                          >
                            <tbody>
                              <tr style="width: 100%">
                                <td>
                                  <p
                                    data-id="react-email-text"
                                    style="
                                      font-size: 16px;
                                      line-height: 150%;
                                      margin: 0px;
                                      font-weight: 400;
                                      font-family: DM Sans, sans-serif;
                                      font-style: normal;
                                      margin-bottom: 0;
                                      color: #1b2559;
                                      overflow-wrap: break-word;
                                    "
                                  >
                                    <span style="font-weight: 700">Note: </span
                                    ><span
                                      >This is an automatically generated email.
                                      Please do not reply to this email. If you
                                      have any questions, please</span
                                    ><a
                                      href="mailto:{{.support_address}}"
                                      data-id="react-email-link"
                                      target="_blank"
                                      style="
                                        color: rgb(255, 194, 17);
                                        text-decoration: none;
                                        margin-left: 0.25rem;
                                        text-decoration-line: underline;
                                      "
                                      >contact support</a
                                    >.
                                  </p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <table
            align="center"
            width="100%"
            data-id="__react-email-container"
            role="presentation"
            cellspacing="0"
            cellpadding="0"
            border="0"
            style="max-width: 37.5em; margin-top: 1.5rem"
          >
            <tbody>
              <tr style="width: 100%">
                <td>
                  <table
                    align="center"
                    width="100%"
                    data-id="react-email-row"
                    role="presentation"
                    cellspacing="0"
                    cellpadding="0"
                    border="0"
                  >
                    <tbody style="width: 100%">
                      <tr style="width: 100%">
                        <td align="center" data-id="__react-email-column">
                          <div style="display: inline-flex">
                            <a
                              href="https://www.facebook.com/kryptogo"
                              data-id="react-email-link"
                              target="_blank"
                              style="
                                color: #067df7;
                                text-decoration: none;
                                margin-left: 0.5rem;
                                margin-right: 0.5rem;
                              "
                              ><img
                                data-id="react-email-img"
                                src="https://wallet-static.kryptogo.com/public/icon/KgFacebook.png"
                                style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                " /></a
                            ><a
                              href="https://twitter.com/kryptogo_"
                              data-id="react-email-link"
                              target="_blank"
                              style="
                                color: #067df7;
                                text-decoration: none;
                                margin-left: 0.5rem;
                                margin-right: 0.5rem;
                              "
                              ><img
                                data-id="react-email-img"
                                src="https://wallet-static.kryptogo.com/public/icon/KgX.png"
                                style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                " /></a
                            ><a
                              href="https://www.linkedin.com/company/kryptogo/"
                              data-id="react-email-link"
                              target="_blank"
                              style="
                                color: #067df7;
                                text-decoration: none;
                                margin-left: 0.5rem;
                                margin-right: 0.5rem;
                              "
                              ><img
                                data-id="react-email-img"
                                src="https://wallet-static.kryptogo.com/public/icon/KgLinkedin.png"
                                style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                " /></a
                            ><a
                              href="https://www.youtube.com/channel/UCrj-kZUkRde6tvin7DKhfQQ"
                              data-id="react-email-link"
                              target="_blank"
                              style="
                                color: #067df7;
                                text-decoration: none;
                                margin-left: 0.5rem;
                                margin-right: 0.5rem;
                              "
                              ><img
                                data-id="react-email-img"
                                src="https://wallet-static.kryptogo.com/public/icon/KgYoutube.png"
                                style="
                                  display: block;
                                  outline: none;
                                  border: none;
                                  text-decoration: none;
                                "
                            /></a>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <p
                    data-id="react-email-text"
                    style="
                      font-size: 12px;
                      line-height: 150%;
                      margin: 16px 0;
                      font-weight: 400;
                      font-family: DM Sans, sans-serif;
                      font-style: normal;
                      overflow-wrap: break-word;
                      margin-top: 12px;
                      color: #b0bbd5;
                      text-align: center;
                      text-decoration: none;
                    "
                  >
                    <a
                      data-id="react-email-link"
                      target="_blank"
                      style="
                        color: #b0bbd5;
                        text-decoration: none;
                        font-weight: 400;
                        font-family: DM Sans, sans-serif;
                        font-size: 12px;
                        font-style: normal;
                        line-height: 150%;
                        overflow-wrap: break-word;
                        margin-top: 12px;
                        text-align: center;
                      "
                      >© 2023 KryptoGO. All Rights Reserved.</a
                    ><br /><a
                      data-id="react-email-link"
                      target="_blank"
                      style="
                        color: rgb(176, 187, 213);
                        text-decoration: none;
                        font-weight: 400;
                        font-family: DM Sans, sans-serif;
                        font-size: 12px;
                        font-style: normal;
                        line-height: 150%;
                        overflow-wrap: break-word;
                        margin-top: 12px;
                        text-align: center;
                      "
                      >www.kryptogo.com</a
                    >
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
</html>
