<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{.BodyTitle}}</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f4f4f4;
        color: #333333;
      }
      .container {
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        padding-bottom: 20px;
      }
      .header img {
        max-width: 150px;
      }
      .content img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 20px 0;
      }

      .content h1 {
        font-size: 22px;
        color: #333333;
      }
      .content p {
        font-size: 16px;
        line-height: 1.6;
      }
      .footer {
        text-align: center;
        padding-top: 20px;
        font-size: 12px;
        color: #777777;
      }
      .info-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
      }
      .info-table th,
      .info-table td {
        border: none;
        padding: 8px;
      }
      .info-table th {
        text-align: left;
        background-color: transparent;
      }
      .info-table td {
        text-align: right;
      }
      .status-pending {
        color: #ffa500;
      } /* Orange */
      .status-processing {
        color: #007bff;
      } /* Blue */
      .status-success {
        color: #28a745;
      } /* Green */
      .status-failed {
        color: #dc3545;
      } /* Red */
      .status-expired {
        color: #6c757d;
      } /* Gray */
      .status-refunded {
        color: #17a2b8;
      } /* Info Blue */
      .status-partially_refunded {
        color: #fd7e14;
      } /* Orange */
      .status-insufficient_refunded {
        color: #fd7e14;
      } /* Orange */
      .status-insufficient_not_refunded {
        color: #dc3545;
      } /* Red */
      .tx-link {
        color: #007bff;
        text-decoration: underline;
        word-break: break-all;
      }
      .tx-link:hover {
        color: #0056b3;
        text-decoration: none;
      }
      .divider {
        border-top: 1px solid #e0e0e0;
        margin: 20px 0;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        {{if .MainLogo}}
        <img src="{{.MainLogo}}" alt="{{.AppName}} Logo" />
        {{else if not .IsGenericAppName}}
        <h2>{{.AppName}}</h2>
        {{end}}
      </div>
      <div class="content">
        <h1>{{.BodyTitle}}</h1>
        {{if .IsFirstSale}}
        <!-- TODO: Add first sale header message -->
        <div
          style="
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
          "
        >
          {{if .IsOwner}}
          <p style="margin: 0; color: #856404">
            <strong>🌟 First Sale Milestone!</strong>
          </p>
          {{end}}
        </div>
        {{end}}
        <p>Hello {{.RecipientName}},</p>

        {{if .IsOwner}}
        <!-- Content for Owners -->
        <img
          src="https://firebasestorage.googleapis.com/v0/b/kryptogo-wallet-app.appspot.com/o/public%2Flogo%2FImage%20from%20Ashley%20Lin%20via%20Slack.png?alt=media&token=1a7cf30b-0386-4e03-8632-b9f69038754f"
          alt="Payment Status"
        />
        {{if .IsOrder}}
        <p>
          A customer order (Payment Intent ID:
          <strong>{{.PaymentIntentID}}</strong>) has been updated to
          <strong
            ><span class="status-{{.StatusClass}}">{{.Status}}</span></strong
          >.
        </p>
        {{else}}
        <p>
          A payment (ID: <strong>{{.PaymentIntentID}}</strong>) has been updated
          to
          <strong
            ><span class="status-{{.StatusClass}}">{{.Status}}</span></strong
          >.
        </p>
        {{end}} {{else}}
        <!-- Content for Customers -->
        {{if .IsOrder}}
        <p>
          The status of your order (Payment Intent ID:
          <strong>{{.PaymentIntentID}}</strong>) has been updated to
          <strong
            ><span class="status-{{.StatusClass}}">{{.Status}}</span></strong
          >.
        </p>
        {{else}}
        <p>
          The status of your payment (ID: <strong>{{.PaymentIntentID}}</strong>)
          has been updated to
          <strong
            ><span class="status-{{.StatusClass}}">{{.Status}}</span></strong
          >.
        </p>
        {{end}} {{end}} {{if .IsOwner}} {{if .IsOrder}}
        <h2>Customer Order Details:</h2>
        {{else}}
        <h2>Customer Payment Details:</h2>
        {{end}} {{else}} {{if .IsOrder}}
        <h2>Order Summary:</h2>
        {{else}}
        <h2>Payment Details:</h2>
        {{end}} {{end}}

        <table class="info-table">
          <tr>
            <th>Payment Intent ID</th>
            <td>{{.PaymentIntentID}}</td>
          </tr>
          <tr>
            <th>Status</th>
            <td>
              <strong class="status-{{.StatusClass}}">{{.Status}}</strong>
            </td>
          </tr>
          {{if .FiatAmount}}
          <tr>
            <th>Fiat Amount</th>
            <td>{{.FiatAmount}} {{.FiatCurrency}}</td>
          </tr>
          {{end}}
          <tr>
            <th>Crypto Amount</th>
            <td>{{.CryptoAmount}} {{.Symbol}}</td>
          </tr>
          {{if .ReceivedAmount}}
          <tr>
            <th>Received Amount</th>
            <td>{{.ReceivedAmount}} {{.Symbol}}</td>
          </tr>
          {{end}} {{if .PaymentTxHash}}
          <tr>
            <th>Payment Transaction Hash</th>
            <td>
              {{if .PaymentTxURL}}
              <a href="{{.PaymentTxURL}}" target="_blank" class="tx-link"
                >{{.PaymentTxHash}}</a
              >
              {{else}} {{.PaymentTxHash}} {{end}}
            </td>
          </tr>
          {{end}} {{if .PaymentTxTimestamp}}
          <tr>
            <th>Payment Timestamp</th>
            <td>{{.PaymentTxTimestamp}}</td>
          </tr>
          {{end}} {{if .KGDeepLink}}
          <tr>
            <th>Payment Link</th>
            <td><a href="{{.KGDeepLink}}">View Payment</a></td>
          </tr>
          {{end}}
        </table>

        <div class="divider"></div>

        {{if and .IsOrder .HasOrderData}}
        <h2>{{if .IsOwner}}Customer {{end}}Order Information:</h2>
        <table class="info-table">
          {{range .OrderDataItems}}
          <tr>
            <th>{{.Key}}</th>
            <td>{{.Value}}</td>
          </tr>
          {{end}}
        </table>
        {{end}} {{if and .SuccessMessage (eq .Status "success")}}
        <div
          style="
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
          "
        >
          <h3 style="margin-top: 0; font-size: 18px">
            {{if .IsOwner}}Customer Success Message{{else}}Thank You!{{end}}
          </h3>
          <p style="margin-bottom: 0">{{.SuccessMessage}}</p>
        </div>

        <div class="divider"></div>
        {{end}} {{if .IsOwner}}
        <p>
          This is a notification about the status of a payment in your system.
        </p>
        {{if .IsFirstSale}}
        <!-- TODO: Add first sale congratulations message for owners -->
        <p
          style="
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #28a745;
            margin: 20px 0;
          "
        >
          🎉 <strong>Congratulations!</strong> This is your first successful
          sale!
          <!-- Add custom message here -->
        </p>
        {{end}} {{else}}
        <p>If you have any questions, please contact our support team.</p>
        {{end}}

        <p>Thank you,<br /></p>
      </div>
      <div class="footer">
        <p>
          &copy; {{.CurrentYear}} {{if not
          .IsGenericAppName}}{{.AppName}}{{else}}Our Service{{end}}. All rights
          reserved.
        </p>
        {{if .SupportAddress}}
        <p>
          Support: <a href="mailto:{{.SupportAddress}}">{{.SupportAddress}}</a>
        </p>
        {{end}}
      </div>
    </div>
  </body>
</html>
