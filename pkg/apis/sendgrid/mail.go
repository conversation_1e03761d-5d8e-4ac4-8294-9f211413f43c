package sendgrid

import (
	"bytes"
	"context"
	"embed"
	"fmt"
	"net/http"
	"strings"
	"text/template"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/sendgrid/rest"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

// EmailType email type
type EmailType string

const (
	// EmailTypeVerificationCode verification code
	EmailTypeVerificationCode EmailType = "verification_code"
	// EmailTypeBind bind
	EmailTypeBind EmailType = "bind"
	// EmailTypeUnbind unbind
	EmailTypeUnbind EmailType = "unbind"
	// EmailTypeStudioInvitation Studio invitation
	EmailTypeStudioInvitation EmailType = "studio_invitation"
	// EmailTypeReviewVerified review verified
	EmailTypeReviewVerified EmailType = "review_verified"
	// EmailTypeReviewRejected review rejected
	EmailTypeReviewRejected EmailType = "review_rejected"
	// EmailTypeManualVerified manual verified
	EmailTypeManualVerified EmailType = "manual_verified"
	// EmailTypeManualRejected manual rejected
	EmailTypeManualRejected EmailType = "manual_rejected"
	// EmailTypeAssetProLiquiditiesAlert asset pro liquidities alert
	EmailTypeAssetProLiquiditiesAlert EmailType = "asset_pro_liquidities_alert"
	// EmailTypePaymentIntentStatusChanged payment intent status changed
	EmailTypePaymentIntentStatusChanged EmailType = "payment_intent_status_changed"
	// EmailTypePaymentItemReminder payment item reminder
	EmailTypePaymentItemReminder EmailType = "payment_item_reminder"
)

//go:embed *.html
var mailTemplates embed.FS

var templates map[string]string

func init() {
	templates = make(map[string]string)

	files, err := mailTemplates.ReadDir(".")
	if err != nil {
		kglog.Fatalf("Init Mail Templates: %s", err.Error())
		return
	}
	for _, file := range files {
		fileName := file.Name()
		if file.IsDir() || !strings.Contains(fileName, ".html") {
			continue
		}
		htmlName := strings.Replace(fileName, ".html", "", 1)
		fileData, err := mailTemplates.ReadFile(fileName)
		if err != nil {
			kglog.Fatalf("Init Mail Templates: %s", err.Error())
			return
		}
		templates[htmlName] = string(fileData)
	}
}

// Send send email
func (s *Client) Send(ctx context.Context, to, subject, content string) (*rest.Response, error) {
	_, span := tracing.Start(ctx, "[sendgrid] Send")
	defer span.End()

	fromEmail := mail.NewEmail("KryptoGO", "<EMAIL>")
	toEmail := mail.NewEmail("", to)
	message := mail.NewSingleEmail(fromEmail, subject, toEmail, "", content)
	return s.Client.Send(message)
}

// SendEmail send email
func (s *Client) SendEmail(ctx context.Context, to, clientName string, emailType EmailType, params interface{}) (*rest.Response, error) {
	htmlContent, ok := templates[string(emailType)]
	if !ok {
		return nil, fmt.Errorf("template not found")
	}
	var subject string
	switch emailType {
	case EmailTypeVerificationCode:
		subject = getVerificationCodeSubject(clientName)
	case EmailTypeBind:
		subject = getBindSubject()
	case EmailTypeUnbind:
		subject = getUnbindSubject()
	case EmailTypeReviewVerified:
		subject = getReviewVerifiedSubject()
	case EmailTypeReviewRejected:
		subject = getReviewRejectedSubject()
	case EmailTypeManualVerified:
		subject = getManualVerifiedSubject()
	case EmailTypeManualRejected:
		subject = getManualRejectedSubject()
	case EmailTypePaymentIntentStatusChanged:
		subject = getPaymentIntentStatusChangedSubject()
	case EmailTypePaymentItemReminder:
		subject = getPaymentItemReminderSubject()
	}
	tmpl, err := template.New(string(emailType)).Parse(htmlContent)
	if err != nil {
		return nil, err
	}
	buf := new(bytes.Buffer)
	if err = tmpl.Execute(buf, params); err != nil {
		return nil, err
	}
	resp, err := s.Send(ctx, to, subject, buf.String())

	// Check err and response here to log error and trigger alert
	// alert will be triggered by keyword: "[sendgrid.mail] SendEmail" and "error" severity
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "[sendgrid.mail] SendEmail error: "+err.Error(), map[string]interface{}{
			"email": to,
			"error": err.Error(),
		})
		return resp, err
	} else if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusAccepted {
		kglog.ErrorWithDataCtx(ctx, "[sendgrid.mail] SendEmail result status not 200 or 202", map[string]interface{}{
			"email":  to,
			"status": resp.StatusCode,
			"resp":   resp,
		})
		return resp, fmt.Errorf("email sent result status not 200 or 202")
	}

	return resp, nil
}

// SendEmailWithSubject send email with subject
func (s *Client) SendEmailWithSubject(ctx context.Context, to, subject string, emailType EmailType, params interface{}) (*rest.Response, error) {
	htmlContent, ok := templates[string(emailType)]
	if !ok {
		return nil, fmt.Errorf("template not found")
	}

	tmpl, err := template.New(string(emailType)).Parse(htmlContent)
	if err != nil {
		return nil, err
	}
	buf := new(bytes.Buffer)
	if err = tmpl.Execute(buf, params); err != nil {
		return nil, err
	}
	return s.Send(ctx, to, subject, buf.String())
}

func getVerificationCodeSubject(clientName string) string {
	return fmt.Sprintf("%s - Verification Code", clientName)
}

func getBindSubject() string {
	return "Your email has been bound to KryptoGO Auth"
}

func getUnbindSubject() string {
	return "Your email has been unbound from KryptoGO Auth"
}

func getReviewVerifiedSubject() string {
	return "Your identity has been verified!"
}

func getReviewRejectedSubject() string {
	return "Identity Verification Update: Action Required"
}

func getManualVerifiedSubject() string {
	return "Account Verification Update: Successful Reassessment!"
}

func getManualRejectedSubject() string {
	return "Important Notice: Update on Your Account Status"
}

func getPaymentIntentStatusChangedSubject() string {
	return "Your Payment Status Has Been Updated"
}

func getPaymentItemReminderSubject() string {
	return "Your ideas are worth $500 — here's how to sell them"
}
