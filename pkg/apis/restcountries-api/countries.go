package restcountriesapi

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/res/restcountries"
	"github.com/siongui/gojianfan"
)

const (
	// HOST .
	HOST = "https://restcountries.com/v3.1"

	// ALL_SUBPATH .
	ALL_SUBPATH = "/all"
)

var (
	httpClient             *resty.Client
	timeout                = time.Duration(10 * time.Second)
	initCountryNameMapOnce sync.Once

	// CountryNameMap .
	CountryNameMap map[string]map[string]string
)

func initCountryNameMap() {
	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)

	countries, resp, err := GetAllTranslations()
	if err != nil {
		kglog.WarningWithData("Cannot get country translations", map[string]interface{}{
			"error": err.Error(),
			"resp":  resp.String(),
		})

		// Use embedded data
		embeddedData, readErr := restcountries.Res.ReadFile("all.json")
		if readErr != nil {
			kglog.Error("Failed to read embedded countries JSON: ", readErr)
		}
		err = json.Unmarshal(embeddedData, &countries)
		if err != nil {
			kglog.Error("Failed to unmarshal embedded countries JSON: ", err)
		}
	}

	CountryNameMap = make(map[string]map[string]string)
	CountryNameMap[util.LocaleEnUS] = make(map[string]string)
	CountryNameMap[util.LocaleZhCN] = make(map[string]string)
	CountryNameMap[util.LocaleZhTW] = make(map[string]string)
	for _, country := range countries {
		enName := country.Name.Official
		zhName := country.Name.Official
		if native, ok := country.Name.NativeName["zho"]; ok {
			zhName = native.Official
		} else if translation, ok := country.Translations["zho"]; ok {
			zhName = translation.Official
		}
		if country.Cca3 == "TWN" {
			enName = "Taiwan"
			zhName = "中華民國（台灣）"
		}
		CountryNameMap[util.LocaleEnUS][country.Cca3] = enName
		CountryNameMap[util.LocaleZhCN][country.Cca3] = zhName
		CountryNameMap[util.LocaleZhTW][country.Cca3] = gojianfan.S2T(zhName)
	}
}

// Country .
// mark out unused fields
type Country struct {
	Name struct {
		Common     string                 `json:"common"`
		Official   string                 `json:"official"`
		NativeName map[string]Translation `json:"nativeName"`
	} `json:"name"`
	Cca3         string                 `json:"cca3"`
	Translations map[string]Translation `json:"translations"`
}

// Translation .
type Translation struct {
	Common   string `json:"common"`
	Official string `json:"official"`
}

// Currency .
type Currency struct {
	Name   string `json:"name"`
	Symbol string `json:"symbol"`
}

// Demonym .
type Demonym struct {
	F string `json:"f"`
	M string `json:"m"`
}

// GetAllTranslations .
func GetAllTranslations() ([]Country, *resty.Response, error) {
	respData := make([]Country, 0)
	resp, err := httpClient.R().
		SetHeader("Content-Type", "application/json").
		SetResult(&respData).
		Get(HOST + ALL_SUBPATH)

	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() != 200 {
		return nil, resp, fmt.Errorf("failed to get country translations, status code: %d", resp.StatusCode())
	}

	return respData, resp, nil
}

// GetSingleCountryNameMap .
func GetSingleCountryNameMap(locale string) map[string]string {
	initCountryNameMapOnce.Do(initCountryNameMap)
	if m, ok := CountryNameMap[locale]; ok {
		return m
	}
	return CountryNameMap["en_US"]
}

// GetCountryNameByCode get country name by code and locale
func GetCountryNameByCode(ctx context.Context, locale, code string) string {
	initCountryNameMapOnce.Do(initCountryNameMap)
	if m, ok := CountryNameMap[locale]; ok {
		if name, ok := m[code]; ok {
			return name
		}
	}
	kglog.WarningWithDataCtx(ctx, "Country name not found", map[string]interface{}{
		"locale": locale,
		"code":   code,
	})
	return ""
}
