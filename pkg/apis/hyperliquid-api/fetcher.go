package hyperliquidapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

func (c *clientImpl) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Hyperliquid}
}

func (c *clientImpl) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeDefi}
}

func (c *clientImpl) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	ctx, span := tracing.Start(ctx, "hyperliquid.GetAssets")
	defer span.End()

	if len(chains) != 1 || chains[0] != domain.Hyperliquid {
		return nil, domain.ErrUnsupportedChain
	}
	if len(types) != 1 || types[0] != domain.AssetTypeDefi {
		return nil, domain.ErrUnsupportedAssetType
	}

	state, err := c.GetClearingHouseState(ctx, address.String())
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "failed to get clearing house state", map[string]interface{}{
			"error": err.Error(),
		})
		return nil, err
	}

	accountValue, err := decimal.NewFromString(state.MarginSummary.AccountValue)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "failed to parse account value", map[string]interface{}{
			"error": err.Error(),
		})
		return nil, err
	}

	defiAsset := domain.NewDefiAsset(domain.Hyperliquid, "hyperliquid-total-position", "Hyperliquid Total Position", "https://app.hyperliquid.xyz/")
	defiAsset.SupplyTokens = []*domain.DefiToken{
		{
			ID:      "usdc",
			Name:    "USD Coin",
			Symbol:  "USDC",
			LogoUrl: util.Ptr("https://token-icons.s3.amazonaws.com/0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.png"),
			Amount:  accountValue,
			Price:   1.0,
		},
	}
	defiAssets := []*domain.DefiAsset{defiAsset}
	return &domain.AggregatedAssets{
		Defi: defiAssets,
	}, nil
}
