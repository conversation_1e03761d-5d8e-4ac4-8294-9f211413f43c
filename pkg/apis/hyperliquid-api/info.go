package hyperliquidapi

import (
	"context"
	"encoding/json"
)

type ClearingHouseStateRequest struct {
	Type string `json:"type"`
	User string `json:"user"`
}

type ClearingHouseStateResponse struct {
	MarginSummary struct {
		AccountValue string `json:"accountValue"`
		TotalNtlPos  string `json:"totalNtlPos"`
		TotalRawUsd  string `json:"totalRawUsd"`
	} `json:"marginSummary"`
	AssetPositions []struct {
		Position struct {
			Coin          string `json:"coin"`
			Szi           string `json:"szi"`
			EntryPx       string `json:"entryPx"`
			PositionValue string `json:"positionValue"`
			UnrealizedPnl string `json:"unrealizedPnl"`
		} `json:"position"`
	} `json:"assetPositions"`
}

func (c *clientImpl) GetClearingHouseState(ctx context.Context, address string) (*ClearingHouseStateResponse, error) {
	req := ClearingHouseStateRequest{
		Type: "clearinghouseState",
		User: address,
	}

	resp, err := c.client.R().
		SetContext(ctx).
		SetBody(req).
		Post("https://api.hyperliquid.xyz/info")

	if err != nil {
		return nil, err
	}

	var result ClearingHouseStateResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, err
	}

	return &result, nil
}
