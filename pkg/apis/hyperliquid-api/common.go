package hyperliquidapi

import (
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

const (
	APIHost = "https://api.hyperliquid.xyz"
	timeout = 15 * time.Second
)

type IClient interface {
	domain.AssetFetcher
}

type clientImpl struct {
	client *resty.Client
}

var (
	instance IClient
)

func InitDefault() {
	httpClient := resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetBaseURL(APIHost).
		SetTimeout(timeout)
	instance = &clientImpl{client: httpClient}
}

func Get() IClient {
	return instance
}

func Set(c IClient) {
	instance = c
}
