package compliance

import (
	"context"
	"fmt"
	"strconv"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// Progress is the progress of the task
type Progress int // [0, 100]
// PotentialRisk is the potential risk of the task
type PotentialRisk int // [0, 100]

const (
	// ProgressCompleted is the progress of the task when it is completed
	ProgressCompleted Progress = 100

	// PotentialRiskMedium is the potential risk of the task when it is medium
	PotentialRiskMedium PotentialRisk = 40
	// PotentialRiskMax is the potential risk of the task when it is max
	PotentialRiskMax PotentialRisk = 100
)

// SearchTaskCallbackRequest is the request body of the callback from compliance
type SearchTaskCallbackRequest struct {
	TaskID        int `json:"task_id"`
	SearchSetting struct {
		Mode          int      `json:"mode"`
		TimeRangeYear int      `json:"time_range_year"`
		Language      string   `json:"language"`
		NegativeWords []string `json:"negative_words"`
		Sites         []string `json:"sites"`
		NumberOfPages int      `json:"number_of_pages"`
	} `json:"search_setting"`
	Target struct {
		Type        int    `json:"type"`
		Name        string `json:"name"`
		Birthday    string `json:"birthday"`
		Citizenship string `json:"citizenship"`
	} `json:"target"`
	CustomerReference string    `json:"customer_reference"`
	Progress          *Progress `json:"progress"`
	TaskOrigin        int       `json:"task_origin"`
	CreationTime      int64     `json:"creation_time"`
	UpdateTime        int64     `json:"update_time"`
	AuditTime         int64     `json:"audit_time"`
	Report            struct {
		SanctionMatched bool          `json:"sanction_matched"`
		PotentialRisk   PotentialRisk `json:"potential_risk"`
		URL             string        `json:"url"`
		Accepted        *bool         `json:"accepted"`
		Comment         string        `json:"comment"`
	} `json:"report"`
	Metadata []struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	} `json:"metadata"`
}

// UpdateSearchTaskAcceptedStatusRequest is the request body of the callback from compliance
type UpdateSearchTaskAcceptedStatusRequest struct {
	Comment  string `json:"comment"`
	Accepted bool   `json:"accepted"`
}

// UpdateSearchTaskAcceptedStatusResponse is the response body of the callback from compliance
type UpdateSearchTaskAcceptedStatusResponse struct {
	TaskID   int    `json:"task_id"`
	Comment  string `json:"comment"`
	Accepted bool   `json:"accepted"`
	Code     int    `json:"code"`
	Message  string `json:"message"`
}

// UpdateSearchTaskAcceptedStatus updates the accepted status of the search task
func (c *apiClient) UpdateSearchTaskAcceptedStatus(ctx context.Context, taskID int, params *UpdateSearchTaskAcceptedStatusRequest) (*UpdateSearchTaskAcceptedStatusResponse, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[compliance] UpdateSearchTaskAcceptedStatus")
	defer span.End()

	respData := &UpdateSearchTaskAcceptedStatusResponse{}
	resp, err := c.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader(apiKeyHeader, c.apiKey).
		SetPathParam("task_id", strconv.Itoa(taskID)).
		SetBody(params).
		SetResult(respData).
		Post(ddHost + updateSearchTaskAcceptedUri)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "UpdateSearchTaskAcceptedStatus error", map[string]interface{}{
			"taskID": taskID,
			"params": fmt.Sprintf("%+v", params),
			"err":    err.Error(),
		})
		return nil, resp, err
	}

	return respData, resp, nil
}
