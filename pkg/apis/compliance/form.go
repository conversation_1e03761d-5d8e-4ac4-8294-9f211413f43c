package compliance

import (
	"context"
	"fmt"
	"strconv"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// GetTasksParam is the param for getting tasks
type GetTasksParam struct {
	ComplianceOrgID int
	KgUID           string
}

type getFormSubmissionsAndTasksResponse struct {
	Code int                       `json:"code"`
	Data []*FormSubmissionAndTasks `json:"data"`
}

// FormSubmissionAndTasks is the result of the form submissions and tasks
type FormSubmissionAndTasks struct {
	KgUID           string      `json:"kg_uid"`
	FormID          int         `json:"form_id"`
	IdvID           int         `json:"idv_id"`
	CddID           int         `json:"cdd_id"`
	FormSubmittedAt int64       `json:"submitted_at"`
	FormUpdatedAt   int64       `json:"updated_at"`
	FormAuditStatus AuditStatus `json:"form_audit_status"`
	// idv & cdd
	IdvAuditStatus   *AuditStatus      `json:"idv_audit_status"`
	IdvStatus        *domain.IdvStatus `json:"idv_status"`
	CddRiskScore     *int32            `json:"cdd_risk_score"`
	SanctionMatched  *bool             `json:"sanction_matched"`
	CddAuditStatus   *AuditStatus      `json:"cdd_audit_status"`
	SearchAutoCreate *bool             `json:"search_auto_create"`
	IdvAutoCreate    *bool             `json:"idv_auto_create"`
	// pi
	Email             *string `json:"email"`
	PhoneNumber       *string `json:"phone_number"`
	RealName          *string `json:"real_name"`
	Country           *string `json:"country"`
	IDNumber          *string `json:"id_number"`
	Birthday          *string `json:"birthday"`
	BankName          *string `json:"bank_name"`
	BranchName        *string `json:"branch_name"`
	AccountNumber     *string `json:"account_number"`
	AccountHolderName *string `json:"account_holder_name"`
}

// GetFormSubmissionsAndTasks returns the form submissions and tasks
func (c *adminAPIClient) GetFormSubmissionsAndTasks(ctx context.Context, params *GetTasksParam) ([]*FormSubmissionAndTasks, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[compliance] GetFormSubmissionsAndTasks")
	defer span.End()

	queryParams := map[string]string{
		"organization_id": strconv.Itoa(params.ComplianceOrgID),
	}

	respData := &getFormSubmissionsAndTasksResponse{}
	resp, err := c.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader(apiKeyHeader, c.apiKey).
		SetResult(respData).
		SetQueryParams(queryParams).
		Get(ddHost + internalFormSubmissionsWithTasksUri)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "GetFormSubmissionsAndTasks error", map[string]any{
			"params": fmt.Sprintf("%+v", params),
			"error":  err.Error(),
		})
		return nil, resp, err
	}

	if resp.StatusCode() != 200 {
		kglog.ErrorWithDataCtx(ctx, "GetFormSubmissionsAndTasks error", map[string]any{
			"params":   fmt.Sprintf("%+v", params),
			"resp":     resp.String(),
			"respData": respData,
		})
		return nil, resp, fmt.Errorf("GetFormSubmissionsAndTasks error: %s", resp.String())
	}

	return respData.Data, resp, nil
}
