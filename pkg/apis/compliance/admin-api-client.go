package compliance

import (
	"context"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

var (
	superAdminAPIKey string
	jumioApiKey      string
)

func init() {
	superAdminAPIKey = config.GetString("COMPLIANCE_API_KEY")
	if superAdminAPIKey == "" {
		kglog.Warning("Cannot get compliance site admin api key")
	}

	jumioApiKey = config.GetString("JUMIO_API_KEY")
	if jumioApiKey == "" {
		kglog.Warning("Cannot get jumio api key")
	}
}

type adminAPIClient struct {
	*resty.Client
	apiKey      string
	jumioAPIKey string
}

// AdminAPIClient is the interface for interacting with compliance admin api.
type AdminAPIClient interface {
	GetAcceptedIDTypes(ctx context.Context) (*AcceptedIDTypesResp, *resty.Response, error)
	GetFormSubmissionsAndTasks(ctx context.Context, params *GetTasksParam) ([]*FormSubmissionAndTasks, *resty.Response, error)
	GetOrgAdmin(ctx context.Context, orgID int) (*OrgAdmin, *resty.Response, error)
}

// NewAPIClient returns a new compliance client
func NewAdminAPIClient() AdminAPIClient {
	return &adminAPIClient{
		httpClient,
		superAdminAPIKey,
		jumioApiKey,
	}
}
