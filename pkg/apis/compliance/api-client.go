package compliance

import (
	"context"

	"github.com/go-resty/resty/v2"
)

// apiClient is the apiClient for interacting with compliance
type apiClient struct {
	*resty.Client
	apiKey string
}

// APIClient is the interface for interacting with compliance
type APIClient interface {
	InitIdv(ctx context.Context, params *IdvTaskRequest) (*InitIdvResp, *resty.Response, error)
	UpdateIDVTaskAcceptedStatus(ctx context.Context, taskID int, params *UpdateIDVTaskAcceptedStatusRequest) (*UpdateIDVTaskAcceptedStatusResponse, *resty.Response, error)
	UpdateSearchTaskAcceptedStatus(ctx context.Context, taskID int, params *UpdateSearchTaskAcceptedStatusRequest) (*UpdateSearchTaskAcceptedStatusResponse, *resty.Response, error)
}

// NewAPIClient returns a new compliance client
func NewAPIClient(apiKey string) APIClient {
	return &apiClient{
		httpClient,
		apiKey,
	}
}
