// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package compliance

import (
	"errors"
	"fmt"
)

const (
	// AuditStatusUndecided is a AuditStatus of type Undecided.
	AuditStatusUndecided AuditStatus = "Undecided"
	// AuditStatusAccepted is a AuditStatus of type Accepted.
	AuditStatusAccepted AuditStatus = "Accepted"
	// AuditStatusRejected is a AuditStatus of type Rejected.
	AuditStatusRejected AuditStatus = "Rejected"
)

var ErrInvalidAuditStatus = errors.New("not a valid AuditStatus")

// String implements the Stringer interface.
func (x AuditStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AuditStatus) IsValid() bool {
	_, err := ParseAuditStatus(string(x))
	return err == nil
}

var _AuditStatusValue = map[string]AuditStatus{
	"Undecided": AuditStatusUndecided,
	"Accepted":  AuditStatusAccepted,
	"Rejected":  AuditStatusRejected,
}

// ParseAuditStatus attempts to convert a string to a AuditStatus.
func ParseAuditStatus(name string) (AuditStatus, error) {
	if x, ok := _AuditStatusValue[name]; ok {
		return x, nil
	}
	return AuditStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidAuditStatus)
}
