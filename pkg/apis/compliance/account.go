package compliance

import (
	"context"
	"strconv"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type OrgAdmin struct {
	EmployeeID int `json:"employee_id"`
	Role       int `json:"role"`
}

// GetOrgAdmin get organization admin
func (c *adminAPIClient) GetOrgAdmin(ctx context.Context, orgID int) (*OrgAdmin, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[compliance] GetOrgAdmin")
	defer span.End()

	respData := &OrgAdmin{}
	resp, err := c.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader(apiKeyHeader, c.apiKey).
		SetPathParam("organization_id", strconv.Itoa(orgID)).
		SetResult(respData).
		Get(authHost + orgAdminUri)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "GetOrgAdmin error", map[string]interface{}{
			"orgID": orgID,
			"err":   err.Error(),
		})
		return nil, resp, err
	}

	return respData, resp, nil
}
