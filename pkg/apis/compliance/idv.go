package compliance

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	_ "embed"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// IDType .
type IDType string

// ApplicantLevel .
type ApplicantLevel string

const (
	// IDTypePassport .
	IDTypePassport IDType = "PASSPORT"
	// IDTypeDrivingLicense .
	IDTypeDrivingLicense IDType = "DRIVING_LICENSE"
	// IDTypeIDCard .
	IDTypeIDCard IDType = "ID_CARD"

	// Sumsub
	ApplicantLevelBuyCrypto ApplicantLevel = "buy-crypto-idv-liveness-level"
)

// IdvTaskRequest .
type IdvTaskRequest struct {
	IDType                        IDType         `json:"id_type" binding:"required"`
	Locale                        string         `json:"locale" binding:"required"`
	WorkflowID                    int            `json:"workflow_id"`
	SuccessURL                    *string        `json:"success_url"`
	ErrorURL                      *string        `json:"error_url"`
	Country                       string         `json:"country" binding:"required"`
	ExpectedName                  string         `json:"expected_name" binding:"required"`
	ExpectedBirthday              string         `json:"expected_birthday" binding:"required"`
	ExpectedIDNumber              string         `json:"expected_id_number" binding:"required"`
	CallbackURL                   string         `json:"callback_url"`
	CustomerReference             string         `json:"customer_reference"`
	AutoCreateDDTask              bool           `json:"auto_create_dd_task"`
	DDTaskCallbackURL             string         `json:"dd_task_callback_url"`
	DDTaskSearchSettingID         int            `json:"dd_task_search_setting_id"`
	DDLanguage                    *string        `json:"dd_language"`
	UseLocaleDefaultSearchSetting bool           `json:"use_locale_default_search_setting"`
	KgUID                         string         `json:"kg_uid"`
	SumsubApplicantLevel          ApplicantLevel `json:"sumsub_applicant_level"`
}

// InitIdvResp .
type InitIdvResp struct {
	IdvTaskID int       `json:"idv_task_id"`
	URL       string    `json:"url"`
	Timestamp time.Time `json:"timestamp"`
	Message   string    `json:"message"`
}

// InitIdv .
func (c *apiClient) InitIdv(ctx context.Context, params *IdvTaskRequest) (*InitIdvResp, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[compliance] InitIdv")
	defer span.End()

	if params == nil {
		return nil, nil, fmt.Errorf("params is nil")
	}

	respData := &InitIdvResp{}
	resp, err := c.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader(apiKeyHeader, c.apiKey).
		SetBody(params).
		SetResult(respData).
		Post(ddHost + idvInitUri)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "InitIdv error", map[string]interface{}{
			"err":    err.Error(),
			"params": *params,
		})
		return nil, resp, err
	}

	if resp.StatusCode() != 200 {
		kglog.ErrorWithDataCtx(ctx, "InitIdv resp error", map[string]any{
			"params":   fmt.Sprintf("%+v", *params),
			"resp":     resp.String(),
			"status":   resp.StatusCode(),
			"respData": respData,
		})
		return nil, resp, fmt.Errorf("InitIdv error: %s", resp.String())
	}

	return respData, resp, nil
}

// State .
type State int

// IdvTaskAuditStatus .
type IdvTaskAuditStatus string

// DDTaskAuditStatus .
type DDTaskAuditStatus int

const (
	// StateAccept .
	StateAccept State = iota
	// StateReview .
	StateReview State = iota
	// StateReject .
	StateReject State = iota
	// StatePending .
	StatePending State = iota
	// StateInitial .
	StateInitial State = iota
)

var (
	// IdvStatusFromIotaToDomain .
	IdvStatusFromIotaToDomain = map[State]domain.IdvStatus{
		StateAccept:  domain.IdvStatusAccept,
		StateReview:  domain.IdvStatusReview,
		StateReject:  domain.IdvStatusReject,
		StatePending: domain.IdvStatusPending,
		StateInitial: domain.IdvStatusInitial,
	}
)

const (
	// IdvTaskAuditStatusPending .
	IdvTaskAuditStatusPending IdvTaskAuditStatus = "Pending"
	// IdvTaskAuditStatusAccepted .
	IdvTaskAuditStatusAccepted IdvTaskAuditStatus = "Accepted"
	// IdvTaskAuditStatusRejected .
	IdvTaskAuditStatusRejected IdvTaskAuditStatus = "Rejected"
)

const (
	// DDTaskAuditStatusUndecided .
	DDTaskAuditStatusUndecided DDTaskAuditStatus = iota
	// DDTaskAuditStatusAccepted .
	DDTaskAuditStatusAccepted DDTaskAuditStatus = iota
	// DDTaskAuditStatusRejected .
	DDTaskAuditStatusRejected DDTaskAuditStatus = iota
)

// IdvTaskDetailResp .
type IdvTaskDetailResp struct {
	IDVTaskID              int                `json:"idv_task_id"`
	Country                string             `json:"country"`
	IDType                 IDType             `json:"id_type"`
	IDImageURL             string             `json:"id_image_url"`
	IDBackImageURL         string             `json:"id_back_image_url"`
	FaceImageURL           string             `json:"face_image_url"`
	IDAndFaceImageURL      string             `json:"id_and_face_image_url"`
	ExpectedName           string             `json:"expected_name"`
	ExpectedBirthday       string             `json:"expected_birthday"`
	ExpectedIDNumber       string             `json:"expected_id_number"`
	CustomerReference      string             `json:"customer_reference"`
	FirstName              *string            `json:"first_name"`
	LastName               *string            `json:"last_name"`
	FullName               *string            `json:"full_name"`
	Birthday               *string            `json:"birthday"`
	IDNumber               *string            `json:"id_number"`
	ExpiryDate             *string            `json:"expiry_date"`
	State                  State              `json:"state"`
	Gender                 *string            `json:"gender"`
	ReviewReasons          []ReviewReason     `json:"review_reasons"`
	RejectReasons          []RejectReason     `json:"reject_reasons"`
	CreatedSearchTaskID    *int               `json:"created_search_task_id"`
	AuditStatus            IdvTaskAuditStatus `json:"audit_status"`
	AuditTimestamp         int64              `json:"audit_timestamp"`
	AuditorEmployeeAccount string             `json:"auditor_employee_account"`
	DDTasks                []DDTask           `json:"dd_tasks"`
}

// ReviewReason .
type ReviewReason struct {
	Code        int    `json:"code"`
	Description string `json:"description"`
}

// RejectReason .
type RejectReason struct {
	Code        int    `json:"code"`
	Description string `json:"description"`
}

// DDTask .
type DDTask struct {
	ID                int               `json:"id"`
	CreationTimestamp int64             `json:"creation_timestamp"`
	AuditStatus       DDTaskAuditStatus `json:"audit_status"`
}

// AcceptedIDTypesResp .
type AcceptedIDTypesResp struct {
	Timestamp       time.Time        `json:"timestamp"`
	AcceptedIDTypes []AcceptedIDType `json:"acceptedIdTypes"`
}

// AcceptedIDType .
type AcceptedIDType struct {
	CountryCode string         `json:"countryCode"`
	CountryName string         `json:"countryName"`
	IDTypes     []IDTypeConfig `json:"idTypes"`
}

// IDTypeConfig .
type IDTypeConfig struct {
	AcquisitionConfig AcquisitionConfig `json:"acquisitionConfig"`
	IDType            IDType            `json:"idType"`
}

// AcquisitionConfig .
type AcquisitionConfig struct {
	BackSide bool `json:"backSide"` // if true, backSide is required for idv
}

// GetAcceptedIDTypes .
func (c *adminAPIClient) GetAcceptedIDTypes(ctx context.Context) (*AcceptedIDTypesResp, *resty.Response, error) {
	// Decode the embedded JSON data
	data := AcceptedIDTypesResp{}
	if err := json.Unmarshal(acceptedIDTypesJSON, &data); err != nil {
		return nil, nil, err
	}

	// Deprecated: Delete this after 2024-12-01
	// respData := &AcceptedIDTypesResp{}
	// resp, err := c.R().
	// 	SetContext(ctx).
	// 	SetHeader("Accept", "application/json").
	// 	SetHeader("Authorization", "Basic "+c.jumioAPIKey).
	// 	SetResult(respData).
	// 	Get(jumioAcceptedIDTypesURL)

	// if err != nil {
	// 	kglog.ErrorCtx(ctx, "GetAcceptedIDTypes error: "+err.Error())
	// 	return nil, resp, err
	// }

	return &data, nil, nil
}

// ReviewReasonTemp .
func ReviewReasonTemp(code int) string {
	switch code {
	case 400, 406:
		return "kyc-review-reason-400"
	case 500:
		return "kyc-review-reason-500"
	case 501:
		return "kyc-review-reason-501"
	case 502:
		return "kyc-review-reason-502"
	case 503:
		return "kyc-review-reason-503"
	case 600:
		return "kyc-review-reason-600"
	default:
		return "kyc-review-reason-unknown"
	}
}

// RejectReasonTemp returns the reject reason temp
func RejectReasonTemp(code int) string {
	switch code {
	case 100, 105, 106, 107, 108, 109, 111:
		return "kyc-reject-reason-100"
	case 102, 103, 104:
		return "kyc-reject-reason-102"
	case 200, 201, 202, 207, 209, 213:
		return "kyc-reject-reason-200"
	case 401, 402, 404, 405:
		return "kyc-reject-reason-401"
	case 403, 407, 408, 409, 410, 411:
		return "kyc-reject-reason-403"
	default:
		return "kyc-reject-reason-unknown"
	}
}

// ParseGenderFromCompliance convert gender from compliance
func ParseGenderFromCompliance(gender string) *string {
	switch gender {
	case "M":
		return util.Ptr("male")
	case "F":
		return util.Ptr("female")
	}
	return nil
}

// UpdateIDVTaskAcceptedStatusRequest is the request body of the callback from compliance
type UpdateIDVTaskAcceptedStatusRequest struct {
	Comment string `json:"comment"`
	Result  string `json:"result"`
}

// UpdateIDVTaskAcceptedStatusResponse is the response body of the callback from compliance
type UpdateIDVTaskAcceptedStatusResponse struct {
	TaskID   int    `json:"task_id"`
	Comment  string `json:"comment"`
	Accepted bool   `json:"accepted"`
	Code     int    `json:"code"`
	Message  string `json:"message"`
}

// UpdateIDVTaskAcceptedStatus updates the accepted status of the idv task
func (c *apiClient) UpdateIDVTaskAcceptedStatus(ctx context.Context, taskID int, params *UpdateIDVTaskAcceptedStatusRequest) (*UpdateIDVTaskAcceptedStatusResponse, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[compliance] UpdateIdvTaskAcceptedStatus")
	defer span.End()

	respData := &UpdateIDVTaskAcceptedStatusResponse{}
	resp, err := c.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader(apiKeyHeader, c.apiKey).
		SetPathParam("idv_id", strconv.Itoa(taskID)).
		SetBody(params).
		SetResult(respData).
		Post(ddHost + updateIdvTaskAcceptedUri)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "UpdateIdvTaskAcceptedStatus error", map[string]interface{}{
			"taskID": taskID,
			"params": fmt.Sprintf("%+v", params),
			"err":    err.Error(),
		})
		return nil, resp, err
	}

	return respData, resp, nil
}

//go:embed accepted_id_types.json
var acceptedIDTypesJSON []byte
