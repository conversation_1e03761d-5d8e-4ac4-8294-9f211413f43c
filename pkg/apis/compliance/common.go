//go:generate go-enum
package compliance

import (
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"golang.org/x/text/language"
)

var (
	ddHost   string
	authHost string

	timeout    = time.Duration(10 * time.Second)
	httpClient *resty.Client

	// LangMatcher is the matcher for language tags
	LangMatcher language.Matcher

	// LangTags first tag will be the default
	LangTags = []language.Tag{
		language.English,
		language.Arabic,
		language.Bulgarian,
		language.Czech,
		language.Danish,
		language.German,
		language.Greek,
		language.BritishEnglish,
		language.Spanish,
		language.LatinAmericanSpanish,
		language.Estonian,
		language.Finnish,
		language.French,
		language.Hebrew,
		language.Croatian,
		language.Hungarian,
		language.Armenian,
		language.Indonesian,
		language.Italian,
		language.Japanese,
		language.Georgian,
		language.Khmer,
		language.Korean,
		language.Lithuanian,
		language.Malay,
		language.Dutch,
		language.Norwegian,
		language.Polish,
		language.Portuguese,
		language.BrazilianPortuguese,
		language.Romanian,
		language.Russian,
		language.Slovak,
		language.Swedish,
		language.Swahili,
		language.Thai,
		language.Turkish,
		language.Vietnamese,
		language.SimplifiedChinese,
		language.TraditionalChinese,
	}

	// LangMapForJumio is the map for language tags to Jumio language tags
	LangMapForJumio = map[language.Tag]string{
		language.LatinAmericanSpanish: "es-MX",
		language.SimplifiedChinese:    "zh-CN",
		language.TraditionalChinese:   "zh-HK",
	}
)

const (
	apiKeyHeader = "GOFACT-API-TOKEN"

	authSubpath              = "/auth"
	ddSubpath                = "/dd"
	idvInitUri               = "/idv/init"
	idvDetailUri             = "/idv/{idv_id}"
	updateIdvTaskAcceptedUri = "/idv/{idv_id}/audit"

	orgAdminUri = "/organization/{organization_id}/admin"

	taskSummaryUri              = "/task/{task_id}/summary"
	updateSearchTaskAcceptedUri = "/task/{task_id}/accepted"

	internalFormSubmissionsUri          = "/internal/form_submissions"
	internalFormSubmissionsWithTasksUri = "/internal/form_submissions_and_tasks"

	internalSearchTasksUri = "/internal/search_tasks"
	internalIdvTasksUri    = "/internal/idv_tasks"
	internalFormConfigUri  = "/internal/form_config"

	jumioAcceptedIDTypesURL = "https://core-sgp.jumio.com/api/netverify/v2/acceptedIdTypes"
)

func init() {
	if config.GetString("COMPLIANCE_HOST") == "" {
		kglog.Warning("Cannot get compliance host from config")
	}
	ddHost = config.GetString("COMPLIANCE_HOST") + ddSubpath
	authHost = config.GetString("COMPLIANCE_HOST") + authSubpath

	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)

	initMatcher()
}

func initMatcher() {
	LangMatcher = language.NewMatcher(LangTags)
}

// AuditStatus is the audit status
// ENUM(Undecided, Accepted, Rejected)
type AuditStatus string
