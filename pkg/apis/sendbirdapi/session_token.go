package sendbirdapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	userTokenURI = "/v3/users/{user_id}/token"
)

// IssueASessionTokenRequest issue a session token for chatroom request
type IssueASessionTokenRequest struct {
	ExpiresAt int64 `json:"expires_at,omitempty"` // Unix milliseconds, default 7 days from token issue time
}

// IssueASessionTokenResponse issue a session token for chatroom response
type IssueASessionTokenResponse struct {
	commonResponse
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expires_at"`
}

// IssueASessionToken issue a session token for chatroom
func (c *SendbirdClient) IssueASessionToken(ctx context.Context, userID string, req *IssueASessionTokenRequest) (*IssueASessionTokenResponse, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[sendbirdapi] IssueASessionToken")
	defer span.End()

	resp := &IssueASessionTokenResponse{}
	restyResp, err := c.R().
		SetContext(ctx).
		SetBody(req).
		SetResult(resp).
		SetPathParam("user_id", userID).
		Post(c.appHost + userTokenURI)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "sendbird issue a session token error", map[string]interface{}{
			"err": err.Error(),
			"req": req,
		})
		return nil, restyResp, err
	}

	if restyResp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "sendbird issue a session token error", map[string]interface{}{
			"resp": restyResp.String(),
			"req":  req,
		})
		return nil, restyResp, fmt.Errorf("sendbird issue a session token error: %v", restyResp.String())
	}

	return resp, restyResp, nil
}
