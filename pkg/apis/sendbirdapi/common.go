//go:generate mockgen -package=sendbirdapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi -destination=sendbird_api_mock.go . SendbirdClientI
package sendbirdapi

import (
	"context"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

const (
	apiHost = "https://api-%s.sendbird.com"
)

var (
	timeout = time.Duration(15 * time.Second)
)

// SendbirdClientI sendbird client interface
type SendbirdClientI interface {
	// IssueASessionToken issues a session token for a user
	IssueASessionToken(ctx context.Context, userID string, req *IssueASessionTokenRequest) (*IssueASessionTokenResponse, *resty.Response, error)
	// CreateAUser creates a user
	CreateAUser(ctx context.Context, req *CreateAUserRequest) (*User, *resty.Response, error)
	// UpdateAUser updates a user
	UpdateAUser(ctx context.Context, userID string, req *UpdateAUserRequest) (*User, *resty.Response, error)
	// ListOpenChannels lists open channels
	ListOpenChannels(ctx context.Context, req *ListOpenChannelsRequest) (*ListOpenChannelsResponse, *resty.Response, error)
	// ListGroupChannels lists group channels
	ListGroupChannels(ctx context.Context, req *ListGroupChannelsRequest) (*ListGroupChannelsResponse, *resty.Response, error)
}

// SendbirdClient sendbird client
type SendbirdClient struct {
	*resty.Client
	appHost string
}

// NewClient returns a new sendbird client
func NewClient(applicationID, apiTokenName string) SendbirdClientI {
	apiToken := config.GetString(apiTokenName)
	appHost := fmt.Sprintf(apiHost, applicationID)
	return &SendbirdClient{
		resty.New().
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout).
			SetHeader("Api-Token", apiToken),
		appHost,
	}
}

type commonResponse struct {
	Message string `json:"message"`
	Code    int    `json:"code"`
	Error   bool   `json:"error"`
}
