package sendbirdapi

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	listOpenChannelsURI  = "/v3/open_channels"
	listGroupChannelsURI = "/v3/group_channels"
)

// ListOpenChannelsRequest .
type ListOpenChannelsRequest struct {
	Token        string `json:"token,omitempty"`
	Limit        int    `json:"limit,omitempty"`
	CustomTypes  string `json:"custom_types,omitempty"`
	NameContains string `json:"name_contains,omitempty"`
	URLContains  string `json:"url_contains,omitempty"`
	ShowFrozen   bool   `json:"show_frozen,omitempty"`
	ShowMetadata bool   `json:"show_metadata,omitempty"`
	ChannelURLs  string `json:"channel_urls,omitempty"`
}

// ToURLValues convert ListOpenChannelsRequest to url.Values
func (params *ListOpenChannelsRequest) ToURLValues() *url.Values {
	values := &url.Values{}
	if params.Token != "" {
		values.Set("token", params.Token)
	}
	if params.Limit != 0 {
		values.Set("limit", strconv.Itoa(params.Limit))
	}
	if params.CustomTypes != "" {
		values.Set("custom_types", params.CustomTypes)
	}
	if params.NameContains != "" {
		values.Set("name_contains", params.NameContains)
	}
	if params.URLContains != "" {
		values.Set("url_contains", params.URLContains)
	}
	if params.ShowFrozen {
		values.Set("show_frozen", "true")
	}
	if params.ShowMetadata {
		values.Set("show_metadata", "true")
	}
	if params.ChannelURLs != "" {
		values.Set("channel_urls", params.ChannelURLs)
	}
	return values
}

// ListOpenChannelsResponse .
type ListOpenChannelsResponse struct {
	Channels []*OpenChannel `json:"channels"`
	Next     string         `json:"next"`
}

// Channel .
type Channel struct {
	Name             string  `json:"name"`
	ChannelURL       string  `json:"channel_url"`
	CoverURL         string  `json:"cover_url"`
	CustomType       string  `json:"custom_type"`
	Data             string  `json:"data"`
	IsEpemeral       bool    `json:"is_ephemeral"`
	MaxLengthMessage int     `json:"max_length_message"`
	CreatedAt        int64   `json:"created_at"`
	Freeze           bool    `json:"freeze"`
	Operators        []*User `json:"operators"`
}

// OpenChannel .
type OpenChannel struct {
	*Channel
	ParticipantCount     int  `json:"participant_count"`
	IsDynamicPartitioned bool `json:"is_dynamic_partitioned"`
}

// ListOpenChannels .
func (c *SendbirdClient) ListOpenChannels(ctx context.Context, req *ListOpenChannelsRequest) (*ListOpenChannelsResponse, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[sendbirdapi] ListOpenChannels")
	defer span.End()

	respData := ListOpenChannelsResponse{}
	resp, err := c.R().
		SetContext(ctx).
		SetQueryParamsFromValues(*req.ToURLValues()).
		SetResult(&respData).
		Get(c.appHost + listOpenChannelsURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "sendbird list open channels error", map[string]interface{}{
			"err": err.Error(),
			"req": req,
		})
		return nil, resp, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "sendbird list open channels error", map[string]interface{}{
			"resp": resp.String(),
			"req":  req,
		})
		return nil, resp, fmt.Errorf("sendbird list open channels error: %v", resp.String())
	}

	return &respData, resp, nil
}

// ListGroupChannelsRequest .
type ListGroupChannelsRequest struct {
	Token string `json:"token,omitempty"`
	Limit int    `json:"limit,omitempty"`
	// DistinctMode                        string `json:"distinct_mode,omitempty"`  // all, distinct, nondistinct
	// PublicMode                          string `json:"public_mode,omitempty"`    // all, public, private
	// SuperMode                           string `json:"super_mode,omitempty"`     // all, super, nonsuper
	// CreatedAfter                        int64  `json:"created_after,omitempty"`  // in unix milliseconds
	// CreatedBefore                       int64  `json:"created_before,omitempty"` // in unix milliseconds
	ShowEmpty bool `json:"show_empty,omitempty"`
	// ShowMember                          bool   `json:"show_member,omitempty"`
	// ShowDeliveryReceipt                 bool   `json:"show_delivery_receipt,omitempty"`
	// ShowReadReceipt                     bool   `json:"show_read_receipt,omitempty"`
	// ShowMetadata                        bool   `json:"show_metadata,omitempty"`
	// ShowFrozen                          bool   `json:"show_frozen,omitempty"`
	// Order                               string `json:"order,omitempty"` // chronological, latest_last_message, channel_name_alphabetical, metadata_value_alphabetical, metadata_order_key
	// MetadataOrderKey                    string `json:"metadata_order_key,omitempty"`
	CustomTypes string `json:"custom_types,omitempty"`
	// CustomTypesStartswith               string `json:"custom_types_startswith,omitempty"`
	// ChannelURLs                         string `json:"channel_urls,omitempty"`
	// Name                                string `json:"name,omitempty"`
	// NameContains                        string `json:"name_contains,omitempty"`
	// NameStartswith                      string `json:"name_startswith,omitempty"`
	MembersExactlyIn string `json:"members_exactly_in,omitempty"`
	MembersIncludeIn string `json:"members_include_in,omitempty"`
	// QueryType                           string `json:"query_type,omitempty"` // AND, OR
	// MembersNickname                     string `json:"members_nickname,omitempty"`
	// MembersNicknameContains             string `json:"members_nickname_contains,omitempty"`
	// MetadataKey                         string `json:"metadata_key,omitempty"`
	// MetadataValues                      string `json:"metadata_values,omitempty"`
	// MetadataValueStartswith             string `json:"metadata_value_startswith,omitempty"`
	// MetacounterKey                      string `json:"metacounter_key,omitempty"`
	// MetacounterValues                   string `json:"metacounter_values,omitempty"`
	// MetacounterValueGte                 int    `json:"metacounter_value_gte,omitempty"`
	// MetacounterValueLte                 int    `json:"metacounter_value_lte,omitempty"`
	// MetacounterValueGt                  int    `json:"metacounter_value_gt,omitempty"`
	// MetacounterValueLt                  int    `json:"metacounter_value_lt,omitempty"`
	// IncludeSortedMetaarrayInLastMessage bool   `json:"include_sorted_metaarray_in_last_message,omitempty"`
}

// ToURLValues convert ListGroupChannelsRequest to url.Values
func (params *ListGroupChannelsRequest) ToURLValues() *url.Values {
	values := &url.Values{}
	if params.Token != "" {
		values.Set("token", params.Token)
	}
	if params.Limit > 0 {
		values.Set("limit", strconv.Itoa(params.Limit))
	}
	if params.ShowEmpty {
		values.Set("show_empty", "true")
	}
	if params.CustomTypes != "" {
		values.Set("custom_types", params.CustomTypes)
	}
	if params.MembersExactlyIn != "" {
		values.Set("members_exactly_in", params.MembersExactlyIn)
	}
	if params.MembersIncludeIn != "" {
		values.Set("members_include_in", params.MembersIncludeIn)
	}
	return values
}

// ListGroupChannelsResponse .
type ListGroupChannelsResponse struct {
	Channels []*GroupChannel `json:"channels"`
	Next     string          `json:"next"`
}

// GroupChannel .
type GroupChannel struct {
	*Channel
	IsDistinct           bool             `json:"is_distinct"`
	IsPublic             bool             `json:"is_public"`
	IsSuper              bool             `json:"is_super"`
	IsAccessCodeRequired bool             `json:"is_access_code_required"`
	MemberCount          int              `json:"member_count"`
	JoinedMemberCount    int              `json:"joined_member_count"`
	Members              []*User          `json:"members"`
	DeliveryReceipt      map[string]int64 `json:"delivery_receipt"`
	ReadReceipt          map[string]int64 `json:"read_receipt"`
	UnreadMessageCount   int              `json:"unread_message_count"`
	UnreadMentionCount   int              `json:"unread_mention_count"`
	LastMessage          *Message         `json:"last_message"`
	CreatedBy            struct {
		UserID                     string `json:"user_id"`
		Nickname                   string `json:"nickname"`
		ProfileURL                 string `json:"profile_url"`
		RequireAuthForProfileImage bool   `json:"require_auth_for_profile_image"`
	} `json:"created_by"`
}

// ListGroupChannels .
func (c *SendbirdClient) ListGroupChannels(ctx context.Context, req *ListGroupChannelsRequest) (*ListGroupChannelsResponse, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[sendbirdapi] ListGroupChannels")
	defer span.End()

	respData := ListGroupChannelsResponse{}
	resp, err := c.R().
		SetContext(ctx).
		SetQueryParamsFromValues(*req.ToURLValues()).
		SetResult(&respData).
		Get(c.appHost + listGroupChannelsURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "sendbird list group channels error", map[string]interface{}{
			"err": err.Error(),
			"req": req,
		})
		return nil, resp, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "sendbird list group channels error", map[string]interface{}{
			"resp": resp.String(),
			"req":  req,
		})
		return nil, resp, fmt.Errorf("sendbird list group channels error: %v", resp.String())
	}

	return &respData, resp, nil
}
