// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package sendbirdapi

import (
	"errors"
	"fmt"
)

const (
	// MentionTypeUsers is a MentionType of type users.
	MentionTypeUsers MentionType = "users"
	// MentionTypeChannel is a MentionType of type channel.
	MentionTypeChannel MentionType = "channel"
)

var ErrInvalidMentionType = errors.New("not a valid MentionType")

// String implements the Stringer interface.
func (x MentionType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x MentionType) IsValid() bool {
	_, err := ParseMentionType(string(x))
	return err == nil
}

var _MentionTypeValue = map[string]MentionType{
	"users":   MentionTypeUsers,
	"channel": MentionTypeChannel,
}

// ParseMentionType attempts to convert a string to a MentionType.
func ParseMentionType(name string) (MentionType, error) {
	if x, ok := _MentionTypeValue[name]; ok {
		return x, nil
	}
	return MentionType(""), fmt.Errorf("%s is %w", name, ErrInvalidMentionType)
}

const (
	// MessageTypeMESG is a MessageType of type MESG.
	MessageTypeMESG MessageType = "MESG"
	// MessageTypeFILE is a MessageType of type FILE.
	MessageTypeFILE MessageType = "FILE"
)

var ErrInvalidMessageType = errors.New("not a valid MessageType")

// String implements the Stringer interface.
func (x MessageType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x MessageType) IsValid() bool {
	_, err := ParseMessageType(string(x))
	return err == nil
}

var _MessageTypeValue = map[string]MessageType{
	"MESG": MessageTypeMESG,
	"FILE": MessageTypeFILE,
}

// ParseMessageType attempts to convert a string to a MessageType.
func ParseMessageType(name string) (MessageType, error) {
	if x, ok := _MessageTypeValue[name]; ok {
		return x, nil
	}
	return MessageType(""), fmt.Errorf("%s is %w", name, ErrInvalidMessageType)
}
