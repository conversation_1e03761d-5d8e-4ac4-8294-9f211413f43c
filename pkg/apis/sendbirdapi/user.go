package sendbirdapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	usersURI      = "/v3/users"
	singleUserURI = "/v3/users/{user_id}"
)

// CreateAUserRequest .
type CreateAUserRequest struct {
	UserID     string `json:"user_id"`
	Nickname   string `json:"nickname"`
	ProfileURL string `json:"profile_url"`
}

// User .
type User struct {
	commonResponse
	UserID             string            `json:"user_id"`
	Nickname           string            `json:"nickname"`
	ProfileURL         string            `json:"profile_url"`
	AccessToken        string            `json:"access_token"`
	HasEverLoggedIn    bool              `json:"has_ever_logged_in"`
	IsActive           bool              `json:"is_active"`
	IsOnline           bool              `json:"is_online"`
	DiscoveryKeys      []string          `json:"discovery_keys"`
	PreferredLanguages []string          `json:"preferred_languages"`
	CreatedAt          int64             `json:"created_at"`
	LastSeenAt         int64             `json:"last_seen_at"`
	Metadata           map[string]string `json:"metadata"`
	IsBlockingMe       bool              `json:"is_blocking_me"`
	IsBlockedByMe      bool              `json:"is_blocked_by_me"`
}

// CreateAUser .
func (c *SendbirdClient) CreateAUser(ctx context.Context, req *CreateAUserRequest) (*User, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[sendbirdapi] CreateAUser")
	defer span.End()

	resp := &User{}
	restyResp, err := c.R().
		SetContext(ctx).
		SetBody(req).
		SetResult(resp).
		Post(c.appHost + usersURI)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "sendbird create a user error", map[string]interface{}{
			"err": err.Error(),
			"req": req,
		})
		return nil, restyResp, err
	}

	if restyResp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "sendbird create a user error", map[string]interface{}{
			"resp": restyResp.String(),
			"req":  req,
		})
		return nil, restyResp, fmt.Errorf("sendbird create a user error: %v", restyResp.String())
	}
	return resp, restyResp, nil
}

// UpdateAUserRequest .
type UpdateAUserRequest struct {
	Nickname                *string  `json:"nickname,omitempty"`
	ProfileURL              *string  `json:"profile_url,omitempty"`
	IssueAccessToken        bool     `json:"issue_access_token,omitempty"`
	IsActive                bool     `json:"is_active,omitempty"`
	LastSeenAt              int64    `json:"last_seen_at,omitempty"`
	DiscoveryKeys           []string `json:"discovery_keys,omitempty"`
	PreferredLanguages      []string `json:"preferred_languages,omitempty"`
	LeaveAllWhenDeactivated bool     `json:"leave_all_when_deactivated,omitempty"`
}

// UpdateAUser .
func (c *SendbirdClient) UpdateAUser(ctx context.Context, userID string, req *UpdateAUserRequest) (*User, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[sendbirdapi] UpdateAUser")
	defer span.End()

	resp := &User{}
	restyResp, err := c.R().
		SetContext(ctx).
		SetBody(req).
		SetResult(resp).
		SetPathParam("user_id", userID).
		Put(c.appHost + singleUserURI)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "sendbird update a user error", map[string]interface{}{
			"err": err.Error(),
			"req": req,
		})
		return nil, restyResp, err
	}

	if restyResp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "sendbird update a user error", map[string]interface{}{
			"resp": restyResp.String(),
			"req":  req,
		})
		return nil, restyResp, fmt.Errorf("sendbird update a user error: %v", restyResp.String())
	}

	return resp, restyResp, nil
}
