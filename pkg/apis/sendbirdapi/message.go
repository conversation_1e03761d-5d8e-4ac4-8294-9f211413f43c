//go:generate go-enum
package sendbirdapi

// MessageType .
// ENUM(MESG, FILE)
type MessageType string

// MentionType .
// ENUM(users, channel)
type MentionType string

// Message .
type Message struct {
	MessageID       int64       `json:"message_id"`
	Type            MessageType `json:"type"`
	CustomType      string      `json:"custom_type"`
	ChannelURL      string      `json:"channel_url"`
	User            *User       `json:"user"`
	MentionType     MentionType `json:"mention_type"`
	MentionedUsers  []*User     `json:"mentioned_users"`
	IsRemoved       bool        `json:"is_removed"`
	Message         string      `json:"message"`
	Translations    interface{} `json:"translations"`
	Data            string      `json:"data"`
	SortedMetaarray interface{} `json:"sorted_metaarray"`
	MessageEvents   struct {
		SendPushNotification string `json:"send_push_notification"`
		UpdateUnreadCount    bool   `json:"update_unread_count"`
		UpdateMentionCount   bool   `json:"update_mention_count"`
		UpdateLastMessage    bool   `json:"update_last_message"`
	} `json:"message_events"`
	OgTag struct {
		OgURL         string `json:"og:url"`
		OgTitle       string `json:"og:title"`
		OgDescription string `json:"og:description"`
		OgImage       struct {
			URL       string `json:"url"`
			SecureURL string `json:"secure_url"`
			Width     int    `json:"width"`
			Height    int    `json:"height"`
		} `json:"og:image"`
	} `json:"og_tag"`
	CreatedAt int64 `json:"created_at"`
	UpdatedAt int64 `json:"updated_at"`
	File      struct {
		URL  string `json:"url"`
		Name string `json:"name"`
		Type string `json:"type"`
		Size int64  `json:"size"`
		Data string `json:"data"`
	} `json:"file"`
	Thumbnails           interface{} `json:"thumbnails"`
	RequireAuth          bool        `json:"require_auth"`
	IsAppleCriticalAlert bool        `json:"is_apple_critical_alert"`
	ThreadInfo           struct {
		ReplyCount    int         `json:"reply_count"`
		MostReplies   interface{} `json:"most_replies"`
		LastRepliedAt int64       `json:"last_replied_at"`
		UpdatedAt     int64       `json:"updated_at"`
	} `json:"thread_info"`
	ParentMessageID   int64       `json:"parent_message_id"`
	ParentMessageInfo interface{} `json:"parent_message_info"`
	IsReplyToChannel  bool        `json:"is_reply_to_channel"`
}
