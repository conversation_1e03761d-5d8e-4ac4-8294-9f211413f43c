//go:generate go-enum

package oasysapi

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// NFT is the type of NFT.
// ENUM(OASYX, OASYX2)
type NFT string

func (x NFT) metadataRoute() string {
	switch x {
	case NFTOASYX:
		return "metadata"
	case NFTOASYX2:
		return "metadata2"
	default:
		return ""
	}
}

func (x NFT) metadata() metadataIntermediate {
	switch x {
	case NFTOASYX:
		return &metadataOasyx{}
	case NFTOASYX2:
		return &metadataOasyx2{}
	default:
		return nil
	}
}

func (x NFT) logoURL() string {
	switch x {
	case NFTOASYX:
		return "https://static2.p2eall.com/games/1551/images/ko/origin/thumbnail_main_1671432023.jpeg"
	case NFTOASYX2:
		return "https://static2.p2eall.com/games/1551/images/ko/origin/thumbnail_main_1671432023.jpeg"
	default:
		return ""
	}
}

func parseNFTFromContractAddress(contractAddress string) (NFT, error) {
	switch strings.ToLower(contractAddress) {
	case OasyxContractAddress:
		return NFTOASYX, nil
	case Oasyx2ContractAddress:
		return NFTOASYX2, nil
	default:
		return "", ErrInvalidNFT
	}
}

// Metadata defines the metadata of NFT.
type Metadata struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Image       string `json:"image"`
	Attributes  []struct {
		TraitType string `json:"trait_type"`
		Value     string `json:"value"`
	} `json:"attributes"`
	contractName string `json:"-"`
}

// MetadataToNftAssetParams defines the params of MetadataToNftAsset.
type MetadataToNftAssetParams struct {
	IsSpam          *bool
	ContractAddress string
	LogoURL         string
	TokenID         string
}

// NftAsset returns the NftAsset of the NFT.
func (m Metadata) NftAsset(ctx context.Context, params MetadataToNftAssetParams) *model.NftAsset {
	_, span := tracing.Start(ctx, "[oasysapi] NftAsset")
	defer span.End()

	nft, _ := parseNFTFromContractAddress(params.ContractAddress)

	collectionImage := params.LogoURL
	if logoURL := nft.logoURL(); logoURL != "" {
		collectionImage = logoURL
	}

	return &model.NftAsset{
		Name:               m.Name,
		CollectionName:     util.Ptr(m.contractName),
		CollectionImageURL: util.Ptr(collectionImage),
		ChainID:            model.ChainIDOasys,
		ContractAddress:    params.ContractAddress,
		TokenID:            params.TokenID,
		CollectionSlug:     util.Ptr(params.ContractAddress),
		ContractSchemaName: util.Ptr("ERC721"),
		ImageURL:           m.Image,
		ImagePreviewURL:    m.Image,
		IsSpam:             params.IsSpam,
		NftDescription:     util.Ptr(m.Description),
	}
}

type metadataIntermediate interface {
	metadata() Metadata
}

type metadataOasyx struct {
	Metadata
	MetaverseBehavior string `json:"metaverse_behavior"`
	ThreeDURL         string `json:"3d_url"`
	AudioURL          string `json:"audio_url"`
	Level             string `json:"level"`
}

func (m metadataOasyx) metadata() Metadata {
	res := m.Metadata

	res.contractName = "OAX"

	return m.Metadata
}

var _ metadataIntermediate = &metadataOasyx{}

type metadataOasyx2 struct {
	Metadata
	AnimationURL string `json:"animation_url"`
	GifURL       string `json:"gif_url"`
}

func (m metadataOasyx2) metadata() Metadata {
	res := m.Metadata

	res.contractName = "OAX2"

	return res
}

var _ metadataIntermediate = &metadataOasyx2{}

// QueryOasysNFTMetadata query the metadata of Oasys NFT.
func QueryOasysNFTMetadata(ctx context.Context, contractAddress, tokenID string) (
	*Metadata, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[oasysapi] QueryOasysNFTMetadata")
	defer span.End()

	nft, err := parseNFTFromContractAddress(contractAddress)
	if err != nil {
		return nil, nil, err
	}

	// oasys nft metadata api response MIME type is octet-stream.
	// so we can't use SetResult.
	resp, err := httpClient.R().
		SetPathParam("metadata_route", nft.metadataRoute()).
		SetPathParam("token_id", tokenID).
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "oasys.NFTMetadata")).
		Get(metadataAPIURL)
	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() >= 400 {
		return nil, resp, errors.New(resp.String())
	}

	m := nft.metadata()
	if err := json.Unmarshal(resp.Body(), m); err != nil {
		return nil, resp, errors.New("json unmarshal error: " + err.Error())
	}

	result := m.metadata()
	return &result, resp, nil
}
