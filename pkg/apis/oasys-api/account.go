package oasysapi

import (
	"context"
	"errors"
	"net/url"
	"strconv"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	errNoTxFound         = "No transactions found"
	errNoInternalTxFound = "No internal transactions found"
	errNoTokenTxFound    = "No token transfers found"
)

// BaseResp defines the response body of txlist, txlistinternal, tokentx.
type BaseResp struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}

// IsSuccess check if the response is success.
func (resp *BaseResp) IsSuccess() bool {
	return resp.Status == "1"
}

// TxParams defines params.
type TxParams struct {
	Address    string `url:"address"`
	Startblock uint32 `url:"start_block"`
	Endblock   uint32 `url:"end_block"`
	Sort       string `url:"sort"`
}

// ToURLValues convert TxParams to url.Values
func (params *TxParams) ToURLValues() *url.Values {
	values := &url.Values{}
	if params.Address != "" {
		values.Set("address", params.Address)
	}

	startBlock := params.Startblock
	if startBlock == 0 && params.Endblock != 0 {
		// a workaround to bypass the etherscan api bug. If startblock is 0, it will return txs that have larger block number than endBlock
		startBlock = 1
	}
	values.Set("startblock", strconv.FormatUint(uint64(startBlock), 10))

	if params.Endblock != 0 {
		values.Set("endblock", strconv.FormatUint(uint64(params.Endblock), 10))
	}

	if params.Sort != "" {
		values.Set("sort", params.Sort)
	} else {
		values.Set("sort", "desc")
	}

	return values
}

// TxListResp defines the response body of txlist.
type TxListResp struct {
	BaseResp
	Result []*TxListResult `json:"result"`
}

// TxListResult txlist
type TxListResult struct {
	BlockNumber       string `json:"blockNumber"`
	TimeStamp         string `json:"timeStamp"`
	Hash              string `json:"hash"`
	Nonce             string `json:"nonce"`
	BlockHash         string `json:"blockHash"`
	TransactionIndex  string `json:"transactionIndex"`
	From              string `json:"from"`
	To                string `json:"to"`
	Value             string `json:"value"`
	Gas               string `json:"gas"`
	GasPrice          string `json:"gasPrice"`
	IsError           string `json:"isError"`
	TxReceiptStatus   string `json:"txreceipt_status"`
	Input             string `json:"input"`
	ContractAddress   string `json:"contractAddress"`
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	GasUsed           string `json:"gasUsed"`
	Confirmations     string `json:"confirmations"`
}

// TxList get external transactions by address
func TxList(ctx context.Context, params *TxParams) (*TxListResp, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[oasysapi] TxList")
	defer span.End()

	respData := TxListResp{}
	resp, err := httpClient.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.txlist")).
		SetHeader("Content-Type", "application/json").
		SetQueryParam("module", "account").
		SetQueryParam("action", "txlist").
		SetQueryParamsFromValues(*params.ToURLValues()).
		SetResult(&respData).
		Get(explorerAPIURL)

	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() >= 400 {
		return &respData, resp, errors.New(resp.String())
	}

	if !respData.BaseResp.IsSuccess() && respData.Message != errNoTxFound {
		return &respData, resp, errors.New(respData.Message)
	}

	return &respData, resp, nil
}

// TxListInternalResp api response
type TxListInternalResp struct {
	BaseResp
	Result []TxListInternalResult `json:"result"`
}

// TxListInternalResult txlist
type TxListInternalResult struct {
	BlockNumber     string `json:"blockNumber"`
	TimeStamp       string `json:"timeStamp"`
	TransactionHash string `json:"transactionHash"`
	From            string `json:"from"`
	To              string `json:"to"`
	Value           string `json:"value"`
	ContractAddress string `json:"contractAddress"`
	Input           string `json:"input"`
	Type            string `json:"type"`
	Gas             string `json:"gas"`
	GasUsed         string `json:"gasUsed"`
	IsError         string `json:"isError"`
	ErrCode         string `json:"errCode"`
	Index           string `json:"index"`
	CallType        string `json:"callType"`
}

// TxListInternal get internal transactions by address
func TxListInternal(ctx context.Context, params *TxParams) (*TxListInternalResp, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[oasysapi] TxListInternal")
	defer span.End()

	respData := TxListInternalResp{}
	resp, err := httpClient.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.txlistinternal")).
		SetHeader("Content-Type", "application/json").
		SetQueryParam("module", "account").
		SetQueryParam("action", "txlistinternal").
		SetQueryParamsFromValues(*params.ToURLValues()).
		SetResult(&respData).
		Get(explorerAPIURL)

	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() >= 400 {
		return &respData, resp, errors.New(resp.String())
	}

	if !respData.BaseResp.IsSuccess() && respData.Message != errNoInternalTxFound {
		return &respData, resp, errors.New(respData.Message)
	}

	return &respData, resp, nil
}

// TokenTxResp api response
type TokenTxResp struct {
	BaseResp
	Result []TokenTxResult `json:"result"`
}

// TokenTxResult txlist
type TokenTxResult struct {
	TokenID           string `json:"tokenID"`
	BlockNumber       string `json:"blockNumber"`
	TimeStamp         string `json:"timeStamp"`
	Hash              string `json:"hash"`
	Nonce             string `json:"nonce"`
	BlockHash         string `json:"blockHash"`
	From              string `json:"from"`
	ContractAddress   string `json:"contractAddress"`
	To                string `json:"to"`
	Value             string `json:"value"`
	TokenName         string `json:"tokenName"`
	TokenSymbol       string `json:"tokenSymbol"`
	TokenDecimal      string `json:"tokenDecimal"`
	TransactionIndex  string `json:"transactionIndex"`
	Gas               string `json:"gas"`
	GasPrice          string `json:"gasPrice"`
	GasUsed           string `json:"gasUsed"`
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	Input             string `json:"input"`
	Confirmations     string `json:"confirmations"`
	LogIndex          string `json:"logIndex"`
}

// TokenTx get token tx by address
func TokenTx(ctx context.Context, params *TxParams) (*TokenTxResp, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[oasysapi] TokenTx")
	defer span.End()

	respData := TokenTxResp{}
	resp, err := httpClient.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.tokentx")).
		SetHeader("Content-Type", "application/json").
		SetQueryParam("module", "account").
		SetQueryParam("action", "tokentx").
		SetQueryParamsFromValues(*params.ToURLValues()).
		SetResult(&respData).
		Get(explorerAPIURL)

	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() >= 400 {
		return &respData, resp, errors.New(resp.String())
	}

	if !respData.BaseResp.IsSuccess() && respData.Message != errNoTokenTxFound {
		return &respData, resp, errors.New(respData.Message)
	}

	return &respData, resp, nil
}
