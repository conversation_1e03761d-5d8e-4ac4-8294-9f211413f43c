// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package oasysapi

import (
	"errors"
	"fmt"
)

const (
	// NFTOASYX is a NFT of type OASYX.
	NFTOASYX NFT = "OASYX"
	// NFTOASYX2 is a NFT of type OASYX2.
	NFTOASYX2 NFT = "OASYX2"
)

var ErrInvalidNFT = errors.New("not a valid NFT")

// String implements the Stringer interface.
func (x NFT) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NFT) IsValid() bool {
	_, err := ParseNFT(string(x))
	return err == nil
}

var _NFTValue = map[string]NFT{
	"OASYX":  NFTOASYX,
	"OASYX2": NFTOASYX2,
}

// ParseNF<PERSON> attempts to convert a string to a NFT.
func ParseNFT(name string) (NFT, error) {
	if x, ok := _NFTValue[name]; ok {
		return x, nil
	}
	return NFT(""), fmt.Errorf("%s is %w", name, ErrInvalidNFT)
}
