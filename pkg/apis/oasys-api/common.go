package oasysapi

import (
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

var (
	timeout    = time.Duration(15 * time.Second)
	httpClient *resty.Client
)

const (
	explorerAPIURL = "https://explorer.oasys.games/api"
	metadataAPIURL = "https://x.oasys.games/{metadata_route}/{token_id}"

	// OasyxContractAddress is the contract address of Oasyx.
	OasyxContractAddress = "******************************************"
	// Oasyx2ContractAddress is the contract address of Oasyx2.
	Oasyx2ContractAddress = "******************************************"
)

func init() {
	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)
}
