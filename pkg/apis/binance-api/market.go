package binanceapi

import (
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// GetSymbolPriceResp is the response struct for GetSymbolPrice
type GetSymbolPriceResp struct {
	Symbol string `json:"symbol"`
	Price  string `json:"price"`
}

// GetSymbolPrice returns the current price of the symbol
func GetSymbolPrice(params []string) ([]GetSymbolPriceResp, error) {
	respData := []GetSymbolPriceResp{}
	paramsBytes, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}
	resp, err := httpClient.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParam("symbols", string(paramsBytes)).
		SetResult(&respData).
		Get(symbolPriceURI)

	if err != nil {
		kglog.WarningWithData("binance api, GetSymbolPrice error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, GetSymbolPrice error", resp.String())
		return nil, errors.New(resp.String())
	}
	return respData, nil
}

// CoinInfo is the response struct for GetCoinInfo
type CoinInfo struct {
	Coin        string        `json:"coin"`
	NetworkList []CoinNetwork `json:"networkList"`
}

// CoinNetwork is the response struct for GetCoinInfo
type CoinNetwork struct {
	Network        string `json:"network"`
	WithdrawDesc   string `json:"withdrawDesc"` // shown only when "withdrawEnable" is false.
	WithdrawEnable bool   `json:"withdrawEnable"`
	WithdrawFee    string `json:"withdrawFee"`
	WithdrawMin    string `json:"withdrawMin"`
}

// GetCoinInfo returns the current price of the symbol
func GetCoinInfo() (*[]CoinInfo, error) {
	respData := []CoinInfo{}
	queryParams := map[string]string{
		"timestamp":  strconv.Itoa(int(util.GetTimestamp(time.Millisecond))),
		"recvWindow": "5000", // 5 seconds
	}

	req := httpClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader(apiKeyHeader, apiKey).
		SetQueryParams(queryParams).
		SetResult(&respData)

	queryParams["signature"] = util.HmacSha256(req.QueryParam.Encode(), secretKey)
	req.SetQueryParams(queryParams)
	resp, err := req.Get(coinFeeURI)

	if err != nil {
		kglog.WarningWithData("Binance, GetCoinInfo error", map[string]interface{}{
			"error": err.Error(),
			"resp":  resp.String(),
		},
		)
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, GetCoinInfo error", resp.String())
		return nil, errors.New(resp.String())
	}
	return &respData, nil
}
