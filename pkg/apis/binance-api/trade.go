package binanceapi

import (
	"errors"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

// OrderStatus is the status of an order
type OrderStatus string

const (
	// OrderStatusNew The order has been accepted by the engine.
	OrderStatusNew OrderStatus = "NEW"
	// OrderStatusPartiallyFilled A part of the order has been filled.
	OrderStatusPartiallyFilled OrderStatus = "PARTIALLY_FILLED"
	// OrderStatusFilled The order has been completed.
	OrderStatusFilled OrderStatus = "FILLED"
	// OrderStatusCanceled The order has been canceled by the user.
	OrderStatusCanceled OrderStatus = "CANCELED"
	// OrderStatusPendingCancel Currently unused
	OrderStatusPendingCancel OrderStatus = "PENDING_CANCEL"
	// OrderStatusRejected The order was not accepted by the engine and not processed.
	OrderStatusRejected OrderStatus = "REJECTED"
	// OrderStatusExpired The order was canceled according to the order type's rules
	// (e.g. LIMIT FOK orders with no fill, LIMIT IOC or MARKET orders that partially fill)
	// or by the exchange, (e.g. orders canceled during liquidation, orders canceled during maintenance)
	OrderStatusExpired OrderStatus = "EXPIRED"
)

// OrderType is the type of an order
type OrderType string

const (
	// OrderTypeLimit A limit order
	OrderTypeLimit OrderType = "LIMIT"
	// OrderTypeMarket A market order
	OrderTypeMarket OrderType = "MARKET"
)

// OrderSide is the side of an order
type OrderSide string

const (
	// OrderSideBuy Buy order
	OrderSideBuy OrderSide = "BUY"
	// OrderSideSell Sell order
	OrderSideSell OrderSide = "SELL"
)

// OrderTimeInForce is the time in force of an order
type OrderTimeInForce string

const (
	// OrderTimeInForceGTC Good Till Cancel
	OrderTimeInForceGTC OrderTimeInForce = "GTC"
	// OrderTimeInForceIOC Immediate or Cancel
	OrderTimeInForceIOC OrderTimeInForce = "IOC"
	// OrderTimeInForceFOK Fill or Kill
	OrderTimeInForceFOK OrderTimeInForce = "FOK"
)

// NewOrderReq is the request struct for the NewOrder function
type NewOrderReq struct {
	Symbol           string
	Side             string
	Type             string
	TimeInForce      string
	Quantity         decimal.Decimal
	QuoteOrderQty    decimal.Decimal
	Price            decimal.Decimal
	NewClientOrderID string
	StrategyID       int
	StrategyType     int
	StopPrice        decimal.Decimal
	TrailingDelta    int64
	IcebbergQty      decimal.Decimal
	NewOrderRespType string
	RecvWindow       int64
	Timestamp        int64
}

// NewOrderResp is the response struct for the NewOrder function
type NewOrderResp struct {
	Symbol                  string `json:"symbol"`
	OrderID                 int64  `json:"orderId"`
	OrderListID             int64  `json:"orderListId"`
	ClientOrderID           string `json:"clientOrderId"`
	TransactTime            int64  `json:"transactTime"`
	Price                   string `json:"price"`
	OrigQty                 string `json:"origQty"`
	ExecutedQty             string `json:"executedQty"`
	CummulativeQuoteQty     string `json:"cummulativeQuoteQty"`
	Status                  string `json:"status"`
	TimeInForce             string `json:"timeInForce"`
	Type                    string `json:"type"`
	Side                    string `json:"side"`
	StrategyID              int    `json:"strategyId"`
	StrategyType            int    `json:"strategyType"`
	WorkingTime             int64  `json:"workingTime"`
	SelfTradePreventionMode string `json:"selfTradePreventionMode"`
	Fills                   []Fill `json:"fills"`
}

// Fill is the fill struct for the NewOrderResp struct
type Fill struct {
	Price           string `json:"price"`
	Qty             string `json:"qty"`
	Commission      string `json:"commission"`
	CommissionAsset string `json:"commissionAsset"`
	TradeID         int64  `json:"tradeId"`
}

// NewOrder sends a new order request to Binance
func NewOrder(params *NewOrderReq) (*NewOrderResp, error) {
	respData := NewOrderResp{}
	queryParams := map[string]string{
		"symbol":           params.Symbol,
		"side":             params.Side,
		"type":             params.Type,
		"quantity":         params.Quantity.String(),
		"newClientOrderId": params.NewClientOrderID,
		"timestamp":        strconv.FormatInt(params.Timestamp, 10),
		"recvWindow":       "5000",
	}

	req := httpClient.R().
		SetHeader(apiKeyHeader, apiKey).
		SetQueryParams(queryParams).
		SetResult(&respData)

	queryParams["signature"] = util.HmacSha256(req.QueryParam.Encode(), secretKey)
	req.SetQueryParams(queryParams)

	uri := testNewOrderURI
	if config.GetString("ENV") == "prod" || config.GetString("ENV") == "staging" {
		uri = newOrderURI
	}
	resp, err := req.SetQueryParams(queryParams).Post(uri)

	if err != nil {
		kglog.ErrorWithData("Binance, NewOrder error", err.Error())
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, NewOrder error", resp.String())
		return nil, errors.New(resp.String())
	}

	return &respData, nil
}

// QueryOrderReq is the request struct for the QueryOrder function
type QueryOrderReq struct {
	Symbol            string
	OrderID           int64
	OrigClientOrderID string
	RecvWindow        int64
	Timestamp         int64
}

// QueryOrderResp is the response struct for the QueryOrder function
type QueryOrderResp struct {
	Symbol                  string           `json:"symbol"`
	OrderID                 int64            `json:"orderId"`
	OrderListID             int64            `json:"orderListId"`
	ClientOrderID           string           `json:"clientOrderId"`
	Price                   string           `json:"price"`
	OrigQty                 string           `json:"origQty"`
	ExecutedQty             string           `json:"executedQty"`
	CummulativeQuoteQty     string           `json:"cummulativeQuoteQty"`
	Status                  OrderStatus      `json:"status"`
	TimeInForce             OrderTimeInForce `json:"timeInForce"`
	Type                    OrderType        `json:"type"`
	Side                    OrderSide        `json:"side"`
	StopPrice               string           `json:"stopPrice"`
	IcebergQty              string           `json:"icebergQty"`
	Time                    int64            `json:"time"`
	UpdateTime              int64            `json:"updateTime"`
	IsWorking               bool             `json:"isWorking"`
	WorkingTime             int64            `json:"workingTime"`
	OrigQuoteOrderQty       string           `json:"origQuoteOrderQty"`
	SelfTradePreventionMode string           `json:"selfTradePreventionMode"`
}

// QueryOrder sends a query order request to Binance
func QueryOrder(params *QueryOrderReq) (*QueryOrderResp, error) {
	respData := QueryOrderResp{}
	queryParams := map[string]string{
		"symbol":            params.Symbol,
		"origClientOrderId": params.OrigClientOrderID,
		"timestamp":         strconv.FormatInt(params.Timestamp, 10),
		"recvWindow":        "5000",
	}

	req := httpClient.R().
		SetHeader(apiKeyHeader, apiKey).
		SetQueryParams(queryParams).
		SetResult(&respData)

	queryParams["signature"] = util.HmacSha256(req.QueryParam.Encode(), secretKey)
	req.SetQueryParams(queryParams)

	resp, err := req.SetQueryParams(queryParams).Get(queryOrderURI)

	if err != nil {
		kglog.ErrorWithData("Binance, QueryOrder error", err.Error())
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, QueryOrder error", resp.String())
		return nil, errors.New(resp.String())
	}

	return &respData, nil
}

// AccountInformationReq is the request struct for the AccountInformation function
type AccountInformationReq struct {
	RecvWindow int64
	Timestamp  int64
}

// AccountInformationResp is the response struct for the AccountInformation function
type AccountInformationResp struct {
	MakerCommission  int `json:"makerCommission"`
	TakerCommission  int `json:"takerCommission"`
	BuyerCommission  int `json:"buyerCommission"`
	SellerCommission int `json:"sellerCommission"`
	CommissionRates  struct {
		Maker  string `json:"maker"`
		Taker  string `json:"taker"`
		Buyer  string `json:"buyer"`
		Seller string `json:"seller"`
	} `json:"commissionRates"`
	CanTrade                   bool   `json:"canTrade"`
	CanWithdraw                bool   `json:"canWithdraw"`
	CanDeposit                 bool   `json:"canDeposit"`
	Brokered                   bool   `json:"brokered"`
	RequireSelfTradePrevention bool   `json:"requireSelfTradePrevention"`
	UpdateTime                 int64  `json:"updateTime"`
	AccountType                string `json:"accountType"`
	Balances                   []struct {
		Asset  string `json:"asset"`
		Free   string `json:"free"`
		Locked string `json:"locked"`
	} `json:"balances"`
	Permissions []string `json:"permissions"`
}

// AccountInformation sends an account information request to Binance
func AccountInformation(params *AccountInformationReq) (*AccountInformationResp, error) {
	respData := AccountInformationResp{}
	queryParams := map[string]string{
		"timestamp":  strconv.FormatInt(params.Timestamp, 10),
		"recvWindow": "5000",
	}

	req := httpClient.R().
		SetHeader(apiKeyHeader, apiKey).
		SetQueryParams(queryParams).
		SetResult(&respData)

	queryParams["signature"] = util.HmacSha256(req.QueryParam.Encode(), secretKey)
	req.SetQueryParams(queryParams)

	resp, err := req.SetQueryParams(queryParams).Get(accountInformationURI)

	if err != nil {
		kglog.ErrorWithData("Binance, AccountInformation error", err.Error())
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, AccountInformation error", resp.String())
		return nil, errors.New(resp.String())
	}

	return &respData, nil
}
