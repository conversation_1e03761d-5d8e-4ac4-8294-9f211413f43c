package binanceapi

import (
	"log"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

const (
	apiKeyHeader = "X-MBX-APIKEY"

	apiHost = "https://api.binance.com"

	symbolPriceURI          = "/api/v3/ticker/price"
	newOrderURI             = "/api/v3/order"
	testNewOrderURI         = "/api/v3/order/test"
	queryOrderURI           = "/api/v3/order"
	withdrawURI             = "/sapi/v1/capital/withdraw/apply"
	withdrawHistoryURI      = "/sapi/v1/capital/withdraw/history"
	coinFeeURI              = "/sapi/v1/capital/config/getall"
	dailyAccountSnapshotURI = "/sapi/v1/accountSnapshot"
	accountInformationURI   = "/api/v3/account"
)

var (
	apiKey     string
	secretKey  string
	timeout    = time.Duration(15 * time.Second)
	httpClient *resty.Client
)

func init() {
	apiKey = config.GetString("BINANCE_API_KEY")
	if apiKey == "" {
		log.Println("Cannot get binance api key")
	}
	secretKey = config.GetString("BINANCE_SECRET_KEY")
	if secretKey == "" {
		log.Println("Cannot get binance secret key")
	}
	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetBaseURL(apiHost).SetTimeout(timeout)
}
