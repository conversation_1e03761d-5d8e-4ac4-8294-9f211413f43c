package binanceapi

import (
	"errors"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

// WithdrawStatus is the status of a withdraw
type WithdrawStatus int

const (
	// WithdrawStatusEmailSent Email Sent
	WithdrawStatusEmailSent WithdrawStatus = 0
	// WithdrawStatusCancelled Cancelled
	WithdrawStatusCancelled WithdrawStatus = 1
	// WithdrawStatusAwaitingApproval Awaiting Approval
	WithdrawStatusAwaitingApproval WithdrawStatus = 2
	// WithdrawStatusRejected Rejected
	WithdrawStatusRejected WithdrawStatus = 3
	// WithdrawStatusProcessing Processing
	WithdrawStatusProcessing WithdrawStatus = 4
	// WithdrawStatusFailure Failure
	WithdrawStatusFailure WithdrawStatus = 5
	// WithdrawStatusCompleted Completed
	WithdrawStatusCompleted WithdrawStatus = 6
)

// WithdrawReq is the request for a withdraw
type WithdrawReq struct {
	Coin               string
	WithdrawOrderID    string
	Network            string
	Address            string
	AddressTag         string
	Amount             decimal.Decimal
	TransactionFeeFlag bool
	Name               string
	WalletType         string
	RecvWindow         int64
	Timestamp          int64
}

// WithdrawResp is the response for a withdraw
type WithdrawResp struct {
	ID string `json:"id"`
}

// Withdraw withdraws a coin
func Withdraw(params *WithdrawReq) (*WithdrawResp, error) {
	respData := WithdrawResp{}
	queryParams := map[string]string{
		"coin":            params.Coin,
		"withdrawOrderId": params.WithdrawOrderID,
		"network":         params.Network,
		"address":         params.Address,
		"amount":          params.Amount.String(),
		"timestamp":       strconv.FormatInt(params.Timestamp, 10),
		"recvWindow":      "5000",
	}
	req := httpClient.R().
		SetHeader(apiKeyHeader, apiKey).
		SetQueryParams(queryParams).
		SetResult(&respData)

	queryParams["signature"] = util.HmacSha256(req.QueryParam.Encode(), secretKey)
	req.SetQueryParams(queryParams)

	resp, err := req.Post(withdrawURI)

	if err != nil {
		kglog.ErrorWithData("Binance, Withdraw error", err.Error())
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, Withdraw error", resp.String())
		return nil, errors.New(resp.String())
	}

	return &respData, nil
}

// WithdrawHistoryReq is the request for a withdraw history
type WithdrawHistoryReq struct {
	Coin            string
	WithdrawOrderID string
	Status          int
	Offset          int
	Limit           int
	StartTime       int64
	EndTime         int64
	RecvWindow      int64
	Timestamp       int64
}

// WithdrawHistoryResp is the response for a withdraw history
type WithdrawHistoryResp struct {
	ID              string         `json:"id"`
	Amount          string         `json:"amount"`
	TransactionFee  string         `json:"transactionFee"`
	Coin            string         `json:"coin"`
	Status          WithdrawStatus `json:"status"`
	Address         string         `json:"address"`
	TxID            string         `json:"txId"`
	ApplyTime       string         `json:"applyTime"`
	Network         string         `json:"network"`
	TransferType    int            `json:"transferType"`
	WithdrawOrderID string         `json:"withdrawOrderId"`
	Info            string         `json:"info"`
	ConfirmNo       int            `json:"confirmNo"`
	WalletType      int            `json:"walletType"`
	TxKey           string         `json:"txKey"`
}

// WithdrawHistory gets the withdraw history
func WithdrawHistory(params *WithdrawHistoryReq) ([]*WithdrawHistoryResp, error) {
	respData := make([]*WithdrawHistoryResp, 0)
	queryParams := map[string]string{
		"withdrawOrderId": params.WithdrawOrderID,
		"timestamp":       strconv.FormatInt(params.Timestamp, 10),
		"recvWindow":      "5000",
	}
	req := httpClient.R().
		SetHeader(apiKeyHeader, apiKey).
		SetQueryParams(queryParams).
		SetResult(&respData)

	queryParams["signature"] = util.HmacSha256(req.QueryParam.Encode(), secretKey)
	req.SetQueryParams(queryParams)

	resp, err := req.Get(withdrawHistoryURI)

	if err != nil {
		kglog.ErrorWithData("Binance, WithdrawHistory error", err.Error())
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, WithdrawHistory error", resp.String())
		return nil, errors.New(resp.String())
	}

	return respData, nil
}

// DailyAccountSnapshotReq is the request for a daily account snapshot
type DailyAccountSnapshotReq struct {
	Type       string
	StartTime  int64
	EndTime    int64
	Limit      int
	RecvWindow int64
	Timestamp  int64
}

// DailyAccountSnapshotResp is the response for a daily account snapshot
type DailyAccountSnapshotResp struct {
	Code        int    `json:"code"`
	Msg         string `json:"msg"`
	SnapshotVos []struct {
		Data struct {
			Balances []struct {
				Asset  string `json:"asset"`
				Free   string `json:"free"`
				Locked string `json:"locked"`
			} `json:"balances"`
			TotalAssetOfBtc string `json:"totalAssetOfBtc"`
		} `json:"data"`
		Type       string `json:"type"`
		UpdateTime int64  `json:"updateTime"`
	} `json:"snapshotVos"`
}

// DailyAccountSnapshot gets the daily account snapshot
func DailyAccountSnapshot(params *DailyAccountSnapshotReq) (*DailyAccountSnapshotResp, error) {
	respData := &DailyAccountSnapshotResp{}
	queryParams := map[string]string{
		"type":       params.Type,
		"recvWindow": "5000",
		"timestamp":  strconv.FormatInt(params.Timestamp, 10),
	}
	req := httpClient.R().
		SetHeader(apiKeyHeader, apiKey).
		SetQueryParams(queryParams).
		SetResult(respData)

	queryParams["signature"] = util.HmacSha256(req.QueryParam.Encode(), secretKey)
	req.SetQueryParams(queryParams)

	resp, err := req.Get(dailyAccountSnapshotURI)

	if err != nil {
		kglog.ErrorWithData("Binance, DailyAccountSnapshot error", err.Error())
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithData("Binance, DailyAccountSnapshot error", resp.String())
		return nil, errors.New(resp.String())
	}

	return respData, nil
}
