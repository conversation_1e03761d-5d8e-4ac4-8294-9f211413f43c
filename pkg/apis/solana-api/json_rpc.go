package solanaapi

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// constants
const (
	DefaultPageLimit  = 1000
	DefaultCommitment = "confirmed"
)

type reqParam struct {
	Method  string        `json:"method"`
	JSONRpc string        `json:"jsonrpc"`
	ID      int           `json:"id"`
	Params  []interface{} `json:"params"`
}

// GetSignaturesForAddressResp response
type GetSignaturesForAddressResp struct {
	Jsonrpc string       `json:"jsonrpc"`
	Result  []Signatures `json:"result"`
	ID      int          `json:"id"`
}

// Signatures .
type Signatures struct {
	BlockTime          int64        `json:"blockTime"`
	ConfirmationStatus string       `json:"confirmationStatus"`
	Err                *interface{} `json:"err"`
	Memo               interface{}  `json:"memo"`
	Signature          string       `json:"signature"`
	Slot               uint32       `json:"slot"`
}

type getSignaturesParam struct {
	Before     string `json:"before,omitempty"`
	Limit      int    `json:"limit"`
	Commitment string `json:"commitment,omitempty"`
}

// GetSignaturesForAddress fetch historical transactions
func (s *solanaAPI) GetSignaturesForAddress(ctx context.Context, address, beforeHash string) (*GetSignaturesForAddressResp, *ResponseMeta, error) {
	ctx, span := tracing.Start(ctx, "json_rpc.GetSignaturesForAddress")
	defer span.End()

	bodyParam := reqParam{
		JSONRpc: "2.0",
		ID:      0,
		Method:  "getSignaturesForAddress",
		Params: []interface{}{
			address,
			getSignaturesParam{
				Before:     beforeHash,
				Limit:      DefaultPageLimit,
				Commitment: DefaultCommitment,
			},
		},
	}

	respData := GetSignaturesForAddressResp{}
	resp, err := s.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "getSignaturesForAddress")).
		SetHeader("Content-Type", "application/json").
		SetBody(bodyParam).
		SetResult(&respData).
		Post("/")

	apiResp := &ResponseMeta{
		ResponseTime: resp.Time(),
		StatusCode:   resp.StatusCode(),
	}

	if err != nil {
		kglog.ErrorfCtx(ctx, "GetSignaturesForAddress err: %v", err)
		return nil, apiResp, fmt.Errorf("GetSignaturesForAddress failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		kglog.ErrorfCtx(ctx, "GetSignaturesForAddress status: %s", resp.Status())
		return nil, apiResp, fmt.Errorf("GetSignaturesForAddress failed: %s", resp.Status())
	}
	return &respData, apiResp, nil
}

// GetTokenAccountsByOwnerResp response
type GetTokenAccountsByOwnerResp struct {
	Jsonrpc string  `json:"jsonrpc"`
	Result  *Result `json:"result"`
	ID      int     `json:"id"`
}

// Result .
type Result struct {
	Context struct {
		APIVersion string `json:"apiVersion"`
		Slot       int    `json:"slot"`
	} `json:"context"`
	Value []*TokenAccount `json:"value"`
}

// TokenAccount .
type TokenAccount struct {
	Account struct {
		Data struct {
			Parsed struct {
				Info struct {
					IsNative    bool   `json:"isNative"`
					Mint        string `json:"mint"`
					Owner       string `json:"owner"`
					State       string `json:"state"`
					TokenAmount struct {
						Amount         string  `json:"amount"`
						Decimals       int     `json:"decimals"`
						UIAmount       float64 `json:"uiAmount"`
						UIAmountString string  `json:"uiAmountString"`
					} `json:"tokenAmount"`
				} `json:"info"`
				Type string `json:"type"`
			} `json:"parsed"`
			Program string `json:"program"`
			// Space   int    `json:"space"`
		} `json:"data"`
		// Executable bool    `json:"executable"`
		// Lamports   int64   `json:"lamports"`
		// Owner      string  `json:"owner"`
		// RentEpoch  big.Int `json:"rentEpoch"`
		// Space      int     `json:"space"`
	} `json:"account"`
	Pubkey string `json:"pubkey"`
}

type getTokenAccountsByOwnerConfig struct {
	ProgramID string `json:"programId"`
}

type getTokenAccountsByOwnerOptions struct {
	Encoding   string `json:"encoding"`
	Commitment string `json:"commitment"`
}

// GetTokenAccountsByOwner fetch token accounts by owner
func (s *solanaAPI) GetTokenAccountsByOwner(ctx context.Context, address string) (*GetTokenAccountsByOwnerResp, *ResponseMeta, error) {
	ctx, span := tracing.Start(ctx, "json_rpc.GetTokenAccountsByOwner")
	defer span.End()

	bodyParam := reqParam{
		JSONRpc: "2.0",
		ID:      1,
		Method:  "getTokenAccountsByOwner",
		Params: []interface{}{
			address,
			getTokenAccountsByOwnerConfig{
				ProgramID: "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
			},
			getTokenAccountsByOwnerOptions{
				Encoding:   "jsonParsed",
				Commitment: DefaultCommitment,
			},
		},
	}

	respData := GetTokenAccountsByOwnerResp{}

	resp, err := s.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "getTokenAccountsByOwner")).
		SetHeader("Content-Type", "application/json").
		SetBody(bodyParam).
		SetResult(&respData).
		Post("/")

	apiResp := &ResponseMeta{
		ResponseTime: resp.Time(),
		StatusCode:   resp.StatusCode(),
	}

	if err != nil {
		kglog.ErrorfCtx(ctx, "GetTokenAccountsByOwner err: %v", err)
		return nil, apiResp, fmt.Errorf("GetTokenAccountsByOwner failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		kglog.ErrorfCtx(ctx, "GetTokenAccountsByOwner status: %s", resp.Status())
		return nil, apiResp, fmt.Errorf("GetTokenAccountsByOwner failed: %s", resp.Status())
	}

	return &respData, apiResp, nil
}

// GetTokenAccountBalanceResp represents the response structure for the getTokenAccountBalance method
type GetTokenAccountBalanceResp struct {
	Jsonrpc string `json:"jsonrpc"`
	Result  struct {
		Context struct {
			APIVersion string `json:"apiVersion"`
			Slot       int    `json:"slot"`
		} `json:"context"`
		Value struct {
			Amount         string  `json:"amount"`
			Decimals       int     `json:"decimals"`
			UIAmount       float64 `json:"uiAmount"`
			UIAmountString string  `json:"uiAmountString"`
		} `json:"value"`
	} `json:"result"`
	Error struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	} `json:"error"`
	ID int `json:"id"`
}

// GetTokenAccountBalance fetches the token balance for a specific token account
func (s *solanaAPI) GetTokenAccountBalance(ctx context.Context, tokenAccount string) (*big.Int, error) {
	ctx, span := tracing.Start(ctx, "json_rpc.GetTokenAccountBalance")
	defer span.End()

	bodyParam := reqParam{
		JSONRpc: "2.0",
		ID:      1,
		Method:  "getTokenAccountBalance",
		Params: []interface{}{
			tokenAccount,
			map[string]string{
				"commitment": DefaultCommitment,
			},
		},
	}

	respData := GetTokenAccountBalanceResp{}

	resp, err := s.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "getTokenAccountBalance")).
		SetHeader("Content-Type", "application/json").
		SetBody(bodyParam).
		SetResult(&respData).
		Post("/")

	if err != nil {
		kglog.ErrorfCtx(ctx, "GetTokenAccountBalance err: %v", err)
		return nil, fmt.Errorf("GetTokenAccountBalance failed: %w", err)
	}

	if resp.StatusCode() != 200 {
		kglog.ErrorfCtx(ctx, "GetTokenAccountBalance status: %s", resp.Status())
		return nil, fmt.Errorf("GetTokenAccountBalance failed: %s", resp.Status())
	}

	// Check if the response contains "could not find account"
	if respData.Error.Code != 0 {
		if strings.Contains(respData.Error.Message, "could not find account") {
			kglog.DebugfCtx(ctx, "Token account not found: %s", tokenAccount)
			return big.NewInt(0), nil
		}
		return nil, fmt.Errorf("GetTokenAccountBalance failed: %s", respData.Error.Message)
	}

	// Parse the amount from the response
	amount, ok := new(big.Int).SetString(respData.Result.Value.Amount, 10)
	if !ok {
		kglog.ErrorfCtx(ctx, "invalid amount: %s", respData.Result.Value.Amount)
		return nil, fmt.Errorf("invalid amount: %s", respData.Result.Value.Amount)
	}

	return amount, nil
}

// GetSlotResponse represents the response structure for the getSlot method.
type GetSlotResponse struct {
	Jsonrpc string `json:"jsonrpc"`
	Result  uint64 `json:"result"`
	ID      int    `json:"id"`
}

func (s *solanaAPI) GetLatestBlockNumber(ctx context.Context) (uint64, error) {
	var rpcResp GetSlotResponse
	resp, err := s.client.R().
		SetContext(ctx).
		SetBody(map[string]interface{}{
			"jsonrpc": "2.0",
			"id":      1,
			"method":  "getSlot",
			"params": []interface{}{
				map[string]string{"commitment": DefaultCommitment},
			},
		}).
		SetResult(&rpcResp).
		Post("/")

	if err != nil {
		return 0, fmt.Errorf("failed to get latest block number: %w", err)
	}

	if resp.StatusCode() >= 400 {
		return 0, fmt.Errorf("HTTP error: %s", resp.Status())
	}

	return rpcResp.Result, nil
}

// GetTransactionResponse represents the response structure for the getTransaction method.
type GetTransactionResponse struct {
	Jsonrpc string `json:"jsonrpc"`
	Error   struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	} `json:"error"`
	Result struct {
		BlockTime   int64       `json:"blockTime"`
		Meta        Meta        `json:"meta"`
		Slot        uint64      `json:"slot"`
		Transaction Transaction `json:"transaction"`
	} `json:"result"`
	ID int `json:"id"`
}

type Meta struct {
	ComputeUnitsConsumed int                    `json:"computeUnitsConsumed"`
	Err                  map[string]interface{} `json:"err"`
	Fee                  int                    `json:"fee"`
	InnerInstructions    []InnerInstruction     `json:"innerInstructions"`
	LogMessages          []string               `json:"logMessages"`
	PostBalances         []int64                `json:"postBalances"`
	PostTokenBalances    []TokenBalance         `json:"postTokenBalances"`
	PreBalances          []int64                `json:"preBalances"`
	PreTokenBalances     []TokenBalance         `json:"preTokenBalances"`
	Rewards              []interface{}          `json:"rewards"`
	Status               Status                 `json:"status"`
}

type TokenBalance struct {
	AccountIndex  int           `json:"accountIndex"`
	Mint          string        `json:"mint"`
	Owner         string        `json:"owner"`
	ProgramId     string        `json:"programId"`
	UITokenAmount UITokenAmount `json:"uiTokenAmount"`
}

type UITokenAmount struct {
	Amount         string  `json:"amount"`
	Decimals       int     `json:"decimals"`
	UIAmount       float64 `json:"uiAmount"`
	UIAmountString string  `json:"uiAmountString"`
}

type Status struct {
	Err interface{} `json:"err"`
}

type Transaction struct {
	Message    Message  `json:"message"`
	Signatures []string `json:"signatures"`
}

type Message struct {
	AccountKeys         []AccountKey         `json:"accountKeys"`
	AddressTableLookups []AddressTableLookup `json:"addressTableLookups"`
	Instructions        []Instruction        `json:"instructions"`
	RecentBlockhash     string               `json:"recentBlockhash"`
}

type Instruction struct {
	Accounts    []string    `json:"accounts"`
	Data        string      `json:"data"`
	ProgramId   string      `json:"programId"`
	StackHeight interface{} `json:"stackHeight"`
	Parsed      parsedInfo  `json:"parsed"`
	Type        string      `json:"type"`
}

type parsedInfo struct {
	Info additionalInfo `json:"info"`
	Type string         `json:"type"`
}

type additionalInfo struct {
	Account           string        `json:"account"`
	Mint              string        `json:"mint"`
	SystemProgram     string        `json:"systemProgram"`
	TokenProgram      string        `json:"tokenProgram"`
	Wallet            string        `json:"wallet"`
	Source            string        `json:"source"`
	Destination       string        `json:"destination"`
	NewAccount        string        `json:"newAccount"`
	Owner             string        `json:"owner"`
	Space             uint64        `json:"space"`
	Amount            string        `json:"amount"`
	Lamports          uint64        `json:"lamports"`
	Authority         string        `json:"authority"`
	MultisigAuthority string        `json:"multisigAuthority"`
	Signers           []string      `json:"signers"`
	TokenAmount       UITokenAmount `json:"tokenAmount"`
}

type AccountKey struct {
	Pubkey   string `json:"pubkey"`
	Signer   bool   `json:"signer"`
	Source   string `json:"source"`
	Writable bool   `json:"writable"`
}

type AddressTableLookup struct {
	AccountKey      string `json:"accountKey"`
	ReadonlyIndexes []int  `json:"readonlyIndexes"`
	WritableIndexes []int  `json:"writableIndexes"`
}

type InnerInstruction struct {
	Index        uint64        `json:"index"`        // Index of the instruction that triggered this inner instruction
	Instructions []Instruction `json:"instructions"` // List of instructions that were executed
	ProgramId    string        `json:"programId"`
	StackHeight  interface{}   `json:"stackHeight"`
}

func (s *solanaAPI) GetTransaction(ctx context.Context, txHash string) (*GetTransactionResponse, error) {
	var rpcResp GetTransactionResponse
	resp, err := s.client.R().
		SetContext(ctx).
		SetBody(map[string]interface{}{
			"jsonrpc": "2.0",
			"id":      1,
			"method":  "getTransaction",
			"params":  []interface{}{txHash, map[string]any{"encoding": "jsonParsed", "maxSupportedTransactionVersion": 0, "commitment": DefaultCommitment}},
		}).
		SetResult(&rpcResp).
		Post("/")

	if err != nil {
		return nil, err
	}

	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("HTTP error: %s", resp.Status())
	}
	// kglog.DebugWithDataCtx(ctx, "solana GetTransaction", map[string]interface{}{"txHash": txHash, "resp": rpcResp})

	return &rpcResp, nil
}
