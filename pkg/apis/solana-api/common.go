//go:generate mockgen -package=solanaapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api -destination=common_mock.go . ISolana
package solanaapi

import (
	"context"
	"math/big"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/shopspring/decimal"
)

// APIHost solana host
const APIHost = "https://solana-mainnet.g.alchemy.com/v2/********************************"

var (
	timeout = time.Duration(15 * time.Second)
)

// ResponseMeta represents common response metadata
type ResponseMeta struct {
	ResponseTime time.Duration
	StatusCode   int
}

// ISolana defines the interface for solanaAPI
type ISolana interface {
	GetSolBalance(ctx context.Context, address string) (decimal.Decimal, error)
	GetSignaturesForAddress(ctx context.Context, address, beforeHash string) (*GetSignaturesForAddressResp, *ResponseMeta, error)
	GetTokenAccountBalance(ctx context.Context, address string) (*big.Int, error)
	GetTokenAccountsByOwner(ctx context.Context, address string) (*GetTokenAccountsByOwnerResp, *ResponseMeta, error)
	GetLatestBlockNumber(ctx context.Context) (uint64, error)
	GetTransaction(ctx context.Context, txHash string) (*GetTransactionResponse, error)
}

type solanaAPI struct {
	client *resty.Client
}

var (
	solanaObj ISolana
	initOnce  sync.Once
)

func InitDefault() {
	initOnce.Do(func() {
		httpClient := resty.New().
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(APIHost).
			SetTimeout(timeout)

		solanaObj = &solanaAPI{client: httpClient}
	})
}

// Set set Solana API singleton
func Set(api ISolana) {
	solanaObj = api
}

// Get returns the Solana API singleton
func Get() ISolana {
	return solanaObj
}
