package solanaapi

import (
	"context"
	"errors"
	"fmt"
	"math/big"

	"github.com/gagliardetto/solana-go"
	"github.com/shopspring/decimal"
)

type getBalanceResp struct {
	Jsonrpc string         `json:"jsonrpc"`
	Result  *balanceResult `json:"result"`
	ID      int            `json:"id"`
}

type balanceResult struct {
	Context struct {
		Slot int `json:"slot"`
	} `json:"context"`
	Value uint64 `json:"value"`
}

// impl GetSolBalance
func (s *solanaAPI) GetSolBalance(ctx context.Context, address string) (decimal.Decimal, error) {
	if address == "" {
		return decimal.Zero, errors.New("address is empty")
	}
	pubKey, err := solana.PublicKeyFromBase58(address)
	if err != nil {
		return decimal.Zero, fmt.Errorf("invalid Solana address: %w", err)
	}

	result := &getBalanceResp{}
	_, err = s.client.R().
		SetContext(ctx).
		SetResult(result).
		SetBody(map[string]interface{}{
			"jsonrpc": "2.0",
			"id":      1,
			"method":  "getBalance",
			"params": []interface{}{
				pubKey.String(),
				map[string]string{"commitment": "processed"},
			},
		}).
		Post("/")

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get balance: %w", err)
	}

	if result.Result == nil {
		return decimal.Zero, errors.New("result is nil")
	}
	return decimal.NewFromBigInt(big.NewInt(int64(result.Result.Value)), -9), nil
}
