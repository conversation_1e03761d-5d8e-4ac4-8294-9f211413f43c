package blockchairapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
)

func (b *blockchairAPI) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Bitcoin}
}

func (b *blockchairAPI) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken}
}

func (b *blockchairAPI) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	if len(chains) != 1 || chains[0] != domain.Bitcoin {
		return nil, domain.ErrUnsupportedChain
	}
	if len(types) != 1 || types[0] != domain.AssetTypeToken {
		return nil, domain.ErrUnsupportedAssetType
	}

	addr := address.String()
	balances, err := b.AddressBalances(ctx, addr)
	if err != nil {
		return nil, err
	}
	if len(balances) == 0 {
		return &domain.AggregatedAssets{}, nil
	}

	tokenAmounts := make([]*domain.TokenAmount, 0)
	if balance, ok := balances[addr]; ok {
		tokenAmounts = append(tokenAmounts, &domain.TokenAmount{
			Token:  domain.Bitcoin.MainToken(),
			Amount: decimal.NewFromFloat(float64(balance) * 1e-8),
		})
	}

	return &domain.AggregatedAssets{
		Tokens: tokenAmounts,
	}, nil
}
