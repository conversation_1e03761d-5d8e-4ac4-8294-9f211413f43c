package blockchairapi

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
)

// constants
const (
	DefaultAddressInfoLimit = 1000
)

// AddressInfoParams .
type AddressInfoParams struct {
	Addresses string
	Limit     int32
	Offset    int32
}

// AddressInfoResp response data
type AddressInfoResp struct {
	Data map[string]*AddressAndTransactions `json:"data"`
}

// AddressAndTransactions .
type AddressAndTransactions struct {
	Address struct {
		Type               string      `json:"type"`
		ScriptHex          string      `json:"script_hex"`
		Balance            int         `json:"balance"`
		BalanceUsd         float64     `json:"balance_usd"`
		Received           int64       `json:"received"`
		ReceivedUsd        float64     `json:"received_usd"`
		Spent              int64       `json:"spent"`
		SpentUsd           float64     `json:"spent_usd"`
		OutputCount        int         `json:"output_count"`
		UnspentOutputCount int         `json:"unspent_output_count"`
		FirstSeenReceiving string      `json:"first_seen_receiving"`
		LastSeenReceiving  string      `json:"last_seen_receiving"`
		FirstSeenSpending  string      `json:"first_seen_spending"`
		LastSeenSpending   string      `json:"last_seen_spending"`
		ScripthashType     interface{} `json:"scripthash_type"`
		TransactionCount   int         `json:"transaction_count"`
	} `json:"address"`
	Transactions []*Transaction `json:"transactions"`
	Utxo         []struct {
		BlockID         int    `json:"block_id"`
		TransactionHash string `json:"transaction_hash"`
		Index           int    `json:"index"`
		Value           int    `json:"value"`
	} `json:"utxo"`
}

// Transaction .
type Transaction struct {
	BlockID       int    `json:"block_id"`
	Hash          string `json:"hash"`
	Time          string `json:"time"`
	BalanceChange int    `json:"balance_change"`
}

// Helper function to check if the rate limit is exceeded
func isRateLimitExceeded(resp *resty.Response) bool {
	return resp.StatusCode() == 429 || resp.StatusCode() == 402
}

// AddressInfo return address and extended public key (xpub) info. It will always get tx details data.
func (b *blockchairAPI) AddressInfo(ctx context.Context, params *AddressInfoParams) (*AddressInfoResp, time.Duration, error) {
	respData := &AddressInfoResp{}
	limit := DefaultAddressInfoLimit
	if params.Limit > 0 {
		limit = int(params.Limit)
	}
	offset := params.Offset
	execTime := time.Duration(0)

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := b.rl.Wait(ctx, "apikey", time.Second*10)
		if err != nil {
			return err
		}
		resp, err := b.client.R().
			SetContext(ctx).
			SetResult(respData).
			SetPathParam("address", params.Addresses).
			SetQueryParam("offset", fmt.Sprintf("%d,%d", offset, offset)).
			SetQueryParam("limit", fmt.Sprintf("%d,%d", limit, limit)).
			SetQueryParam("transaction_details", "true").
			SetQueryParam("key", config.GetString("BLOCKCHAIR_API_KEY")).
			Get(accountInfoURI)

		if err != nil {
			return err
		}

		execTime = resp.Time()
		if isRateLimitExceeded(resp) {
			kglog.WarningWithDataCtx(ctx, "[blockchair] AddressInfo rate limit exceeded", map[string]interface{}{
				"resp":   resp.String(),
				"params": params,
			})
			return code.ErrRateLimitExceeded
		}

		if resp.StatusCode() >= 400 {
			return fmt.Errorf("blockchair address info error: %v", resp.String())
		}

		return nil
	})

	if retryErr != nil {
		return nil, execTime, retryErr
	}

	return respData, execTime, nil
}

// BlockDetailResponse represents the response structure for block details from Blockchair.
type BlockDetailResponse struct {
	Data map[string]struct {
		Block struct {
			ID        uint64 `json:"id"`
			Hash      string `json:"hash"`
			Height    uint64 `json:"height"`
			Timestamp string `json:"time"`
			TxCount   int    `json:"transaction_count"`
		} `json:"block"`
	} `json:"data"`
}

// GetBlock fetches detailed information about a specific block.
func (b *blockchairAPI) GetBlock(ctx context.Context, blockIdentifier string) (*BlockDetailResponse, error) {
	respData := &BlockDetailResponse{}
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		resp, err := b.client.R().
			SetContext(ctx).
			SetResult(respData).
			Get(fmt.Sprintf("/bitcoin/dashboards/block/%s", blockIdentifier))

		if err != nil {
			return err
		}

		if isRateLimitExceeded(resp) {
			kglog.WarningWithDataCtx(ctx, "[blockchair] GetBlock rate limit exceeded", map[string]interface{}{
				"resp":            resp.String(),
				"blockIdentifier": blockIdentifier,
			})
			return code.ErrRateLimitExceeded
		}

		if resp.StatusCode() >= 400 {
			return fmt.Errorf("blockchair block detail error: %v", resp.String())
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return respData, nil
}

type TransactionInfo struct {
	BlockID int64   `json:"block_id"`
	Hash    string  `json:"hash"`
	Fee     uint64  `json:"fee"`
	FeeUSD  float64 `json:"fee_usd"`
	Time    string  `json:"time"`
}

type TransactionInput struct {
	Recipient string `json:"recipient"`
	Value     uint64 `json:"value"`
}

type TransactionOutput struct {
	Recipient string `json:"recipient"`
	Value     uint64 `json:"value"`
}

type TransactionData struct {
	Transaction TransactionInfo     `json:"transaction"`
	Inputs      []TransactionInput  `json:"inputs"`
	Outputs     []TransactionOutput `json:"outputs"`
}

// TransactionResponse represents the response structure for a transaction from Blockchair.
type TransactionResponse struct {
	Data    map[string]TransactionData `json:"data"`
	Context struct {
		Code int `json:"code"`
	} `json:"context"`
}

func (b *blockchairAPI) GetTransaction(ctx context.Context, txHash string) (*domain.BitcoinTransaction, error) {
	var rawResp json.RawMessage
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := b.rl.Wait(ctx, "apikey", time.Second*10)
		if err != nil {
			return err
		}
		resp, err := b.client.R().
			SetContext(ctx).
			SetResult(&rawResp).
			SetQueryParam("key", config.GetString("BLOCKCHAIR_API_KEY")).
			Get(fmt.Sprintf("/bitcoin/dashboards/transaction/%s", txHash))

		if isRateLimitExceeded(resp) {
			kglog.WarningWithDataCtx(ctx, "[blockchair] GetTransaction rate limit exceeded", map[string]interface{}{
				"resp":   resp.String(),
				"txHash": txHash,
			})
			return code.ErrRateLimitExceeded
		}

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[blockchairAPI] GetTransaction failed", map[string]interface{}{"txHash": txHash, "err": err.Error()})
			return err
		}

		if resp.StatusCode() >= 400 {
			if resp.StatusCode() == 404 {
				return domain.ErrRecordNotFound
			}
			return fmt.Errorf("blockchair get transaction error: %v", resp.String())
		}
		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	// Check if data is empty array
	var temp struct {
		Data json.RawMessage `json:"data"`
	}
	if err := json.Unmarshal(rawResp, &temp); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// when tx hash is not found, it returns empty "array" instead of map. So we need to process it
	if string(temp.Data) == "[]" {
		return nil, domain.ErrRecordNotFound
	}

	// Parse full response
	respData := &TransactionResponse{}
	if err := json.Unmarshal(rawResp, respData); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	txData, exists := respData.Data[txHash]
	if !exists {
		return nil, domain.ErrRecordNotFound
	}

	return txData.toDomain(), nil
}

func (t *TransactionData) toDomain() *domain.BitcoinTransaction {
	txTime, err := time.Parse("2006-01-02 15:04:05", t.Transaction.Time)
	if err != nil {
		kglog.ErrorWithData("Failed to parse transaction time", map[string]interface{}{
			"time": t.Transaction.Time,
			"err":  err.Error(),
		})
		txTime = time.Time{}
	}
	txTime = txTime.UTC()
	return &domain.BitcoinTransaction{
		BlockID: t.Transaction.BlockID,
		Hash:    t.Transaction.Hash,
		Fee:     t.Transaction.Fee,
		Time:    txTime,
		Inputs: lo.Map(t.Inputs, func(input TransactionInput, _ int) domain.BitcoinTransactionInput {
			return domain.BitcoinTransactionInput{
				Address: input.Recipient,
				Value:   input.Value,
			}
		}),
		Outputs: lo.Map(t.Outputs, func(output TransactionOutput, _ int) domain.BitcoinTransactionOutput {
			return domain.BitcoinTransactionOutput{
				Address: output.Recipient,
				Value:   output.Value,
			}
		}),
	}
}
