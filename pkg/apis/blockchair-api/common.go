//go:generate mockgen -package=blockchairapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchair-api -destination=blockchair_api_mock.go . IBlockchair
package blockchairapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiHost        = "https://api.blockchair.com"
	balancesURI    = "/bitcoin/addresses/balances"
	accountInfoURI = "/bitcoin/dashboards/address/{address}"
)

var (
	timeout       = time.Duration(15 * time.Second)
	blockchairObj IBlockchair
)

// IBlockchair is the interface for blockchairAPI
type IBlockchair interface {
	AddressInfo(ctx context.Context, params *AddressInfoParams) (*AddressInfoResp, time.Duration, error)
	AddressBalances(ctx context.Context, addresses string) (map[string]int64, error)
	GetLatestBlockNumber(ctx context.Context) (uint64, error)
	GetBlock(ctx context.Context, blockIdentifier string) (*BlockDetailResponse, error)
	GetTransaction(ctx context.Context, txHash string) (*domain.BitcoinTransaction, error)
	domain.AssetFetcher
}

type blockchairAPI struct {
	client resty.Client
	rl     domain.RateLimiter
}

// InitDefault inits default API
func InitDefault(_rl domain.RateLimiter) {
	client := resty.NewRestyClient()
	api := &blockchairAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).
			SetTimeout(timeout),
		rl: _rl,
	}
	blockchairObj = api
}

// Init inits with resty.Client for mocking
func Init(client resty.Client, _rl domain.RateLimiter) {
	blockchairObj = &blockchairAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).
			SetTimeout(timeout),
		rl: _rl,
	}
}

// Set set Blockchair API singleton
func Set(api IBlockchair) {
	blockchairObj = api
}

// Get get Blockchair API singleton
func Get() IBlockchair {
	return blockchairObj
}
