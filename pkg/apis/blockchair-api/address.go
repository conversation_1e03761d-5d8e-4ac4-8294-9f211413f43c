package blockchairapi

import (
	"context"
	"fmt"
	time "time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// AddressBalancesResp response from blockchair api
type AddressBalancesResp struct {
	Data interface{} `json:"data"`
}

// AddressBalances Address balance mass check
// It's extremely fast and cheap, so no need to use an api key.
func (b *blockchairAPI) AddressBalances(ctx context.Context, address string) (map[string]int64, error) {
	ctx, span := tracing.Start(ctx, "[blockchairapi] AddressBalances")
	defer span.End()

	reqData := map[string]string{
		"addresses": address,
	}
	respData := &AddressBalancesResp{}
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := b.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		resp, err := b.client.R().
			SetContext(ctx).
			SetFormData(reqData).
			SetQueryParam("key", config.GetString("BLOCKCHAIR_API_KEY")).
			SetResult(respData).
			Post(balancesURI)

		if isRateLimitExceeded(resp) {
			kglog.WarningWithDataCtx(ctx, "[blockchair] AddressBalances rate limit exceeded", map[string]interface{}{
				"resp":    resp.String(),
				"address": address,
			})
			return code.ErrRateLimitExceeded
		}

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[blockchairAPI] AddressBalances failed", map[string]interface{}{"address": address, "err": err.Error()})
			return err
		}

		if resp.StatusCode() >= 400 {
			return fmt.Errorf("blockchair address balances error: %v", resp.String())
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	data := map[string]int64{}
	if respData.Data != nil {
		if m, ok := respData.Data.(map[string]interface{}); ok {
			for k, v := range m {
				data[k] = int64(v.(float64))
			}
		}
	}

	return data, nil
}
