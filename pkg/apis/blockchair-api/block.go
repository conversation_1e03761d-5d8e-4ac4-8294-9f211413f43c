package blockchairapi

import (
	"context"
	"fmt"
	time "time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// BlockResponse represents the response structure for the latest block from Blockchair.
type BlockResponse struct {
	Data []struct {
		ID uint64 `json:"id"`
	} `json:"data"`
}

// GetLatestBlockNumber fetches the latest block number.
func (b *blockchairAPI) GetLatestBlockNumber(ctx context.Context) (uint64, error) {
	respData := &BlockResponse{}
	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		resp, err := b.client.R().
			SetContext(ctx).
			SetResult(respData).
			Get("/bitcoin/blocks?limit=1")

		if isRateLimitExceeded(resp) {
			kglog.WarningWithDataCtx(ctx, "[blockchair] GetLatestBlockNumber rate limit exceeded", map[string]interface{}{
				"resp": resp.String(),
			})
			return code.ErrRateLimitExceeded
		}

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[blockchairAPI] GetLatestBlockNumber failed", map[string]interface{}{"err": err.Error()})
			return err
		}

		if resp.StatusCode() >= 400 {
			return fmt.Errorf("blockchair latest block error: %v", resp.String())
		}

		if len(respData.Data) == 0 {
			return fmt.Errorf("no block data found")
		}

		return nil
	})

	if retryErr != nil {
		return 0, retryErr
	}

	return respData.Data[0].ID, nil
}
