package moralisapi

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// GetNFTCollectionMetadataResp response data
type GetNFTCollectionMetadataResp struct {
	TokenAddress string `json:"token_address"`
	Name         string `json:"name"`
	Symbol       string `json:"symbol"`
	ContractType string `json:"contract_type"`
	SyncedAt     string `json:"synced_at"`
}

// GetNFTCollectionMetadata returns the contract level metadata for the given contract
func GetNFTCollectionMetadata(ctx context.Context, chainID, contractAddress string) (*GetNFTCollectionMetadataResp, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "moralis-api.GetNFTCollectionMetadata")
	defer span.End()

	respData := GetNFTCollectionMetadataResp{}
	chain := domain.IDToChain(chainID)
	if chain == nil {
		return nil, nil, code.ErrUnsupportedChainID
	}
	chainNumber := fmt.Sprintf("0x%x", chain.Number())

	resp, err := httpClient.R().
		SetContext(ctx).
		SetPathParam("contract", contractAddress).
		SetHeader("Content-Type", "application/json").
		SetHeader("x-api-key", apiKey).
		SetQueryParam("chain", chainNumber).
		SetResult(&respData).
		Get(nftCollectionMetadataUrl)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetNFTCollectionMetadata error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, resp, err
	}

	if resp.StatusCode() >= 400 {
		return nil, resp, errors.New(resp.String())
	}

	return &respData, resp, nil
}

// GetTokenMetadataResp response data
type GetTokenMetadataResp struct {
	Address     string    `json:"address"`
	Name        string    `json:"name"`
	Symbol      string    `json:"symbol"`
	Decimals    string    `json:"decimals"`
	Logo        string    `json:"logo"`
	LogoHash    string    `json:"logo_hash"`
	Thumbnail   string    `json:"thumbnail"`
	BlockNumber string    `json:"block_number"`
	Validated   int       `json:"validated"`
	CreatedAt   time.Time `json:"created_at"`
}

// GetTokenMetadata Returns metadata (name, symbol, decimals, logo) for a given token contract address
func GetTokenMetadata(ctx context.Context, chainID, contractAddress string) ([]*GetTokenMetadataResp, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "moralis-api.GetTokenMetadata")
	defer span.End()

	respData := make([]*GetTokenMetadataResp, 0)
	chain := domain.IDToChain(chainID)
	if chain == nil {
		return nil, nil, code.ErrUnsupportedChainID
	}
	chainNumber := fmt.Sprintf("0x%x", chain.Number())

	resp, err := httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("x-api-key", apiKey).
		SetQueryParam("chain", chainNumber).
		SetQueryParam("addresses", contractAddress).
		SetResult(&respData).
		Get(erc20MetadataUrl)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "GetTokenMetadata error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, resp, err
	}

	if resp.StatusCode() >= 400 {
		return nil, resp, errors.New(resp.String())
	}

	return respData, resp, nil
}
