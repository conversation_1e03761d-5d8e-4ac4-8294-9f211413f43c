package moralisapi

import (
	"log"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

var (
	apiKey string

	timeout    = time.Duration(30 * time.Second)
	httpClient *resty.Client
)

const (
	nftCollectionMetadataUrl = "https://deep-index.moralis.io/api/v2/nft/{contract}/metadata"
	erc20MetadataUrl         = "https://deep-index.moralis.io/api/v2/erc20/metadata"
)

func init() {
	apiKey = config.GetString("MORALIS_API_KEY")
	if apiKey == "" {
		log.Println("Cannot get Moralis api key")
	}

	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLoggerWithBody()).
		SetTimeout(timeout)
}
