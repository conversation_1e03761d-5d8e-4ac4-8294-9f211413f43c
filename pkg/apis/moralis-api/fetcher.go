package moralisapi

import (
	"context"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type fetcherImpl struct{}

// Fetcher of trust wallet github repo. Its coingecko ID will be empty
func Fetcher() domain.TokenMetadataFetcher {
	return &fetcherImpl{}
}

func (f *fetcherImpl) SupportedChains() []domain.Chain {
	return []domain.Chain{
		domain.Ethereum,
		domain.Polygon,
		domain.BNBChain,
		domain.Arbitrum,
		domain.BaseChain,
		domain.Optimism,
	}
}

func (f *fetcherImpl) FetchMetadata(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
	ctx, span := tracing.Start(ctx, "moralisapi.FetchMetadata")
	defer span.End()

	// try to get fungible token metadata
	resp, _, err := GetTokenMetadata(ctx, chain.ID(), tokenID)
	if err == nil && len(resp) > 0 && resp[0].Symbol != "" {
		metadata := resp[0]
		decimals, err := strconv.Atoi(metadata.Decimals)
		if err != nil {
			return nil, err
		}
		return &domain.TokenMetadata{
			Name:     metadata.Name,
			Symbol:   metadata.Symbol,
			Decimals: uint(decimals),
			LogoUrl:  metadata.Logo,
		}, nil
	}

	// try to get nft metadata
	nftResp, _, err := GetNFTCollectionMetadata(ctx, chain.ID(), tokenID)
	if err == nil && nftResp.Symbol != "" {
		return &domain.TokenMetadata{
			Name:     nftResp.Name,
			Symbol:   nftResp.Symbol,
			Decimals: uint(0),
		}, nil
	}

	return nil, domain.ErrRecordNotFound
}
