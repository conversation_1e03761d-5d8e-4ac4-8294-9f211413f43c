package stripe

import (
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	stripe "github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/client"
)

var (
	stripeSecretKey string
)

func init() {
	stripeSecretKey = config.GetString("STRIPE_SECRET_KEY")
	if stripeSecretKey == "" {
		kglog.Warning("Cannot get stripe secret key")
	}
}

// NewClient .
func NewClient() *client.API {
	config := &stripe.BackendConfig{
		MaxNetworkRetries: 3,
	}

	return client.New(stripeSecretKey, &stripe.Backends{
		API:     stripe.GetBackendWithConfig(stripe.APIBackend, config),
		Uploads: stripe.GetBackendWithConfig(stripe.UploadsBackend, config),
		Connect: stripe.GetBackendWithConfig(stripe.ConnectBackend, config),
	})
}
