//go:generate go-enum
package etherscanapi

import (
	"context"
	"errors"
	"net/url"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	directRetryLimit   = 5
	txMessageNotFound  = "No transactions found"
	txMessageRateLimit = "Max rate limit reached"
)

// TxParams .
type TxParams struct {
	Address    string `url:"address"`
	Startblock uint32 `url:"startblock"`
	Endblock   uint32 `url:"endblock"`
	Sort       string `url:"sort"`
	// 實測 page 和 offset 沒用
}

// ToURLValues convert TxParams to url.Values
func (params *TxParams) ToURLValues() *url.Values {
	values := &url.Values{}
	if params.Address != "" {
		values.Set("address", params.Address)
	}

	startBlock := params.Startblock
	if startBlock == 0 && params.Endblock != 0 {
		// a workaround to bypass the etherscan api bug. If startblock is 0, it will return txs that have larger block number than endBlock
		startBlock = 1
	}
	values.Set("startblock", strconv.FormatUint(uint64(startBlock), 10))

	if params.Endblock != 0 {
		values.Set("endblock", strconv.FormatUint(uint64(params.Endblock), 10))
	}

	if params.Sort != "" {
		values.Set("sort", params.Sort)
	} else {
		values.Set("sort", "desc")
	}

	return values
}

// ScanStatus .
// ENUM(0,1) - 0: failed, 1: success
type ScanStatus string

// TxlistResp api response
type TxlistResp struct {
	Status  ScanStatus      `json:"status"`
	Message string          `json:"message"`
	Result  []*TxlistResult `json:"result"`
}

// TxlistResult txlist
type TxlistResult struct {
	BlockNumber       string `json:"blockNumber"`
	TimeStamp         string `json:"timeStamp"`
	Hash              string `json:"hash"`
	Nonce             string `json:"nonce"`
	BlockHash         string `json:"blockHash"`
	TransactionIndex  string `json:"transactionIndex"`
	From              string `json:"from"`
	To                string `json:"to"`
	Value             string `json:"value"`
	Gas               string `json:"gas"`
	GasPrice          string `json:"gasPrice"`
	IsError           string `json:"isError"`
	TxReceiptStatus   string `json:"txreceipt_status"`
	Input             string `json:"input"`
	ContractAddress   string `json:"contractAddress"`
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	GasUsed           string `json:"gasUsed"`
	Confirmations     string `json:"confirmations"`
	MethodID          string `json:"methodId"`
	FunctionName      string `json:"functionName"`
}

// Txlist get external transactions by address
func (e *etherscanAPI) Txlist(ctx context.Context, chainID string, params *TxParams) (*TxlistResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] Txlist")
	defer span.End()

	respData := TxlistResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := TxlistResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.txlist")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "account").
			SetQueryParam("action", "txlist").
			SetQueryParamsFromValues(*params.ToURLValues()).
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, resp.Time(), err
	}
	if isRateLimitExceeded(resp) {
		return &respData, resp.Time(), code.ErrRateLimitExceeded
	}

	if respData.Status != ScanStatus1 && respData.Message != txMessageNotFound {
		return &respData, resp.Time(), code.ErrorStatusNotOK
	}

	return &respData, resp.Time(), nil
}

// TxlistInternalResp api response
type TxlistInternalResp struct {
	Status  ScanStatus             `json:"status"`
	Message string                 `json:"message"`
	Result  []TxlistInternalResult `json:"result"`
}

// TxlistInternalResult txlist
type TxlistInternalResult struct {
	BlockNumber     string `json:"blockNumber"`
	TimeStamp       string `json:"timeStamp"`
	Hash            string `json:"hash"`
	From            string `json:"from"`
	To              string `json:"to"`
	Value           string `json:"value"`
	ContractAddress string `json:"contractAddress"`
	Input           string `json:"input"`
	Type            string `json:"type"`
	Gas             string `json:"gas"`
	GasUsed         string `json:"gasUsed"`
	TraceID         string `json:"traceId"`
	IsError         string `json:"isError"`
	ErrCode         string `json:"errCode"`
}

// TxlistInternal get internal transactions by address
func (e *etherscanAPI) TxlistInternal(ctx context.Context, chainID string, params *TxParams) (*TxlistInternalResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] TxlistInternal")
	defer span.End()

	respData := TxlistInternalResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := TxlistInternalResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.txlistinternal")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "account").
			SetQueryParam("action", "txlistinternal").
			SetQueryParamsFromValues(*params.ToURLValues()).
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, resp.Time(), err
	}
	if isRateLimitExceeded(resp) {
		return &respData, resp.Time(), code.ErrRateLimitExceeded
	}

	if respData.Status != ScanStatus1 && respData.Message != txMessageNotFound {
		return &respData, resp.Time(), code.ErrorStatusNotOK
	}

	return &respData, resp.Time(), nil
}

// TokenTxResp api response
type TokenTxResp struct {
	Status  ScanStatus      `json:"status"`
	Message string          `json:"message"`
	Result  []TokenTxResult `json:"result"`
}

// TokenTxResult txlist
type TokenTxResult struct {
	BlockNumber       string `json:"blockNumber"`
	TimeStamp         string `json:"timeStamp"`
	Hash              string `json:"hash"`
	Nonce             string `json:"nonce"`
	BlockHash         string `json:"blockHash"`
	From              string `json:"from"`
	ContractAddress   string `json:"contractAddress"`
	To                string `json:"to"`
	Value             string `json:"value"`
	TokenName         string `json:"tokenName"`
	TokenSymbol       string `json:"tokenSymbol"`
	TokenDecimal      string `json:"tokenDecimal"`
	TransactionIndex  string `json:"transactionIndex"`
	Gas               string `json:"gas"`
	GasPrice          string `json:"gasPrice"`
	GasUsed           string `json:"gasUsed"`
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	Input             string `json:"input"`
	Confirmations     string `json:"confirmations"`
}

func (e *etherscanAPI) TokenTx(ctx context.Context, chainID string, params *TxParams) (*TokenTxResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] TokenTx")
	defer span.End()

	respData := TokenTxResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := TokenTxResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.tokentx")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "account").
			SetQueryParam("action", "tokentx").
			SetQueryParamsFromValues(*params.ToURLValues()).
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, resp.Time(), err
	}
	if isRateLimitExceeded(resp) {
		return &respData, resp.Time(), code.ErrRateLimitExceeded
	}

	if respData.Status != ScanStatus1 && respData.Message != txMessageNotFound {
		return &respData, resp.Time(), code.ErrorStatusNotOK
	}
	return &respData, resp.Time(), nil
}

// TokenNftTxResp api response
type TokenNftTxResp struct {
	Status  ScanStatus         `json:"status"`
	Message string             `json:"message"`
	Result  []TokenNftTxResult `json:"result"`
}

// TokenNftTxResult txlist
type TokenNftTxResult struct {
	BlockNumber       string `json:"blockNumber"`
	TimeStamp         string `json:"timeStamp"`
	Hash              string `json:"hash"`
	Nonce             string `json:"nonce"`
	BlockHash         string `json:"blockHash"`
	From              string `json:"from"`
	ContractAddress   string `json:"contractAddress"`
	To                string `json:"to"`
	TokenID           string `json:"tokenID"`
	TokenName         string `json:"tokenName"`
	TokenSymbol       string `json:"tokenSymbol"`
	TokenDecimal      string `json:"tokenDecimal"`
	TransactionIndex  string `json:"transactionIndex"`
	Gas               string `json:"gas"`
	GasPrice          string `json:"gasPrice"`
	GasUsed           string `json:"gasUsed"`
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	Input             string `json:"input"`
	Confirmations     string `json:"confirmations"`
}

// TokenNftTx get token tx by address
func (e *etherscanAPI) TokenNftTx(ctx context.Context, chainID string, params *TxParams) (*TokenNftTxResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] TokenNftTx")
	defer span.End()

	respData := TokenNftTxResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := TokenNftTxResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.tokennfttx")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "account").
			SetQueryParam("action", "tokennfttx").
			SetQueryParamsFromValues(*params.ToURLValues()).
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, resp.Time(), err
	}
	if isRateLimitExceeded(resp) {
		return &respData, resp.Time(), code.ErrRateLimitExceeded
	}

	if respData.Status != ScanStatus1 && respData.Message != txMessageNotFound {
		return &respData, resp.Time(), code.ErrorStatusNotOK
	}

	return &respData, resp.Time(), nil
}

// Token1155TxResp api response
type Token1155TxResp struct {
	Status  ScanStatus          `json:"status"`
	Message string              `json:"message"`
	Result  []Token1155TxResult `json:"result"`
}

// Token1155TxResult txlist
type Token1155TxResult struct {
	BlockNumber       string `json:"blockNumber"`
	TimeStamp         string `json:"timeStamp"`
	Hash              string `json:"hash"`
	Nonce             string `json:"nonce"`
	BlockHash         string `json:"blockHash"`
	TransactionIndex  string `json:"transactionIndex"`
	Gas               string `json:"gas"`
	GasPrice          string `json:"gasPrice"`
	GasUsed           string `json:"gasUsed"`
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	Input             string `json:"input"`
	ContractAddress   string `json:"contractAddress"`
	From              string `json:"from"`
	To                string `json:"to"`
	TokenID           string `json:"tokenID"`
	TokenValue        string `json:"tokenValue"`
	TokenName         string `json:"tokenName"`
	TokenSymbol       string `json:"tokenSymbol"`
	Confirmations     string `json:"confirmations"`
}

// Token1155Tx get token tx by address
func (e *etherscanAPI) Token1155Tx(ctx context.Context, chainID string, params *TxParams) (*Token1155TxResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] Token1155Tx")
	defer span.End()

	respData := Token1155TxResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := Token1155TxResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.token1155tx")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "account").
			SetQueryParam("action", "token1155tx").
			SetQueryParamsFromValues(*params.ToURLValues()).
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, resp.Time(), err
	}
	if isRateLimitExceeded(resp) {
		return &respData, resp.Time(), code.ErrRateLimitExceeded
	}

	if respData.Status != ScanStatus1 && respData.Message != txMessageNotFound {
		return &respData, resp.Time(), code.ErrorStatusNotOK
	}

	return &respData, resp.Time(), nil
}

// TxResp tx api response
type TxResp struct {
	Status  ScanStatus `json:"status"`
	Message string     `json:"message"`
	Result  *struct {
		Status string `json:"status"`
	} `json:"result"`
}

// GetTxStatus get tx receipt status
func (e *etherscanAPI) GetTxStatus(ctx context.Context, chainID, txHash string) (string, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] GetTxStatus")
	defer span.End()

	respData := &TxResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := &TxResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "transaction.gettxreceiptstatus")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "transaction").
			SetQueryParam("action", "gettxreceiptstatus").
			SetQueryParam("txhash", txHash).
			SetResult(localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Airdrop - GetTxStatus error", err.Error())
		return "", err
	}
	if isRateLimitExceeded(resp) {
		return "", code.ErrRateLimitExceeded
	}
	if respData.Status != ScanStatus1 {
		kglog.WarningWithDataCtx(ctx, "Airdrop - GetTxStatus error", respData)
		return "", code.ErrorStatusNotOK
	}
	return respData.Result.Status, nil
}

// GetInternalTxByHashResp .
type GetInternalTxByHashResp struct {
	Status  ScanStatus `json:"status"`
	Message string     `json:"message"`
	Result  []struct {
		BlockNumber     string `json:"blockNumber"`
		TimeStamp       string `json:"timeStamp"`
		From            string `json:"from"`
		To              string `json:"to"`
		Value           string `json:"value"`
		Gas             string `json:"gas"`
		GasUsed         string `json:"gasUsed"`
		IsError         string `json:"isError"` // 0 for success, 1 for error
		Input           string `json:"input"`
		ContractAddress string `json:"contractAddress"`
		Type            string `json:"type"`
		ErrCode         string `json:"errCode"`
	} `json:"result"`
}

func (r *GetInternalTxByHashResp) toModel() []*domain.InternalTx {
	internalTxs := make([]*domain.InternalTx, 0)
	for _, result := range r.Result {
		internalTxs = append(internalTxs, &domain.InternalTx{
			From:            result.From,
			To:              result.To,
			Value:           result.Value,
			ContractAddress: result.ContractAddress,
		})
	}
	return internalTxs
}

func (e *etherscanAPI) getInternalTxByHash(ctx context.Context, chainID, txHash string) (*GetInternalTxByHashResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] getInternalTxByHash")
	defer span.End()

	respData := &GetInternalTxByHashResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := &GetInternalTxByHashResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "account.txlistinternal")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "account").
			SetQueryParam("action", "txlistinternal").
			SetQueryParam("txhash", txHash).
			SetResult(localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, resp.Time(), err
	}
	kglog.DebugWithDataCtx(ctx, "Etherscan getInternalTxByHash", map[string]interface{}{
		"resp": resp.String(),
	})
	if isRateLimitExceeded(resp) {
		return respData, resp.Time(), code.ErrRateLimitExceeded
	}

	if resp.StatusCode() >= 400 {
		return respData, resp.Time(), errors.New(resp.String())
	}

	if respData.Message == txMessageNotFound { // normal case
		return respData, resp.Time(), nil
	}

	if respData.Message == txMessageRateLimit {
		return respData, resp.Time(), errors.New(resp.String())
	}

	if respData.Status != ScanStatus1 {
		return respData, resp.Time(), code.ErrorStatusNotOK
	}
	return respData, resp.Time(), nil
}

// GetInternalTxByHashWithRetry get internal tx by hash with retry
func (e *etherscanAPI) GetInternalTxByHashWithRetry(ctx context.Context, chainID, txHash string) ([]*domain.InternalTx, time.Duration, error) {
	cnt := 0
	waitTime := 1
	for {
		internalTx, respTime, err := e.getInternalTxByHash(ctx, chainID, txHash)
		if err == nil {
			return internalTx.toModel(), respTime, nil
		} else if errors.Is(err, code.ErrRateLimitExceeded) || isClientTimeout(err) {
			kglog.InfoWithDataCtx(ctx, "Etherscan, GetInternalTxByHashWithRetry, retrying due to rate limit or client timeout", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
		} else if err != code.ErrorStatusNotOK {
			kglog.InfoWithDataCtx(ctx, "Etherscan, GetInternalTxByHashWithRetry, other error", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
			return nil, respTime, err
		}

		cnt++
		if cnt > directRetryLimit {
			kglog.ErrorWithDataCtx(ctx, "Etherscan, GetInternalTxByHashWithRetry, retry limit reached", map[string]interface{}{
				"txHash": txHash,
				"err":    err.Error(),
			})
			return nil, respTime, err
		}
		time.Sleep(time.Second * time.Duration(waitTime))
		waitTime = waitTime * 2
	}
}
