// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package etherscanapi

import (
	"errors"
	"fmt"
)

const (
	// ScanStatus0 is a ScanStatus of type 0.
	ScanStatus0 ScanStatus = "0"
	// ScanStatus1 is a ScanStatus of type 1.
	ScanStatus1 ScanStatus = "1"
)

var ErrInvalidScanStatus = errors.New("not a valid ScanStatus")

// String implements the Stringer interface.
func (x ScanStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x ScanStatus) IsValid() bool {
	_, err := ParseScanStatus(string(x))
	return err == nil
}

var _ScanStatusValue = map[string]ScanStatus{
	"0": ScanStatus0,
	"1": ScanStatus1,
}

// ParseScanStatus attempts to convert a string to a ScanStatus.
func ParseScanStatus(name string) (ScanStatus, error) {
	if x, ok := _ScanStatusValue[name]; ok {
		return x, nil
	}
	return ScanStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidScanStatus)
}
