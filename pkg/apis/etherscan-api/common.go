//go:generate mockgen -package=etherscanapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/etherscan-api -destination=common_mock.go . IEvmscan

package etherscanapi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

const (
	timeout = time.Duration(15 * time.Second)
)

var apiUrlV2 = "https://api.etherscan.io/v2/api"

var apiKeyMap = map[domain.Chain]string{
	domain.Ethereum:  config.GetString("ETHERSCAN_API_KEY"),
	domain.Polygon:   config.GetString("POLYGONSCAN_API_KEY"),
	domain.BNBChain:  config.GetString("BSCSCAN_API_KEY"),
	domain.Arbitrum:  config.GetString("ARBISCAN_API_KEY"),
	domain.Optimism:  config.GetString("OPSCAN_API_KEY"),
	domain.BaseChain: config.GetString("BASESCAN_API_KEY"),
}

// TxStatus tx status from etherscan
type TxStatus string

// TxStatus list etherscan tx status
const (
	TxStatusSuccess TxStatus = "1"
	TxStatusFail    TxStatus = "0"
	TxStatusUnknown TxStatus = ""
)

// IEvmscan defines the interface for etherscanAPI
type IEvmscan interface {
	Txlist(ctx context.Context, chainID string, params *TxParams) (*TxlistResp, time.Duration, error)
	TxlistInternal(ctx context.Context, chainID string, params *TxParams) (*TxlistInternalResp, time.Duration, error)
	TokenTx(ctx context.Context, chainID string, params *TxParams) (*TokenTxResp, time.Duration, error)
	TokenNftTx(ctx context.Context, chainID string, params *TxParams) (*TokenNftTxResp, time.Duration, error)
	Token1155Tx(ctx context.Context, chainID string, params *TxParams) (*Token1155TxResp, time.Duration, error)
	GetTxStatus(ctx context.Context, chainID, txHash string) (string, error)
	GetInternalTxByHashWithRetry(ctx context.Context, chainID, txHash string) ([]*domain.InternalTx, time.Duration, error)
	GetTransactionReceipt(ctx context.Context, chainID, txhash string) (*GetTransactionReceiptResp, error)
	EthCall(ctx context.Context, chainID, from, to, data string) (*EthCallResp, error)
	GetBlockByNumber(ctx context.Context, chainID string, blockNum int) (*GetBlockByNumberResp, time.Duration, error)
	VerifyContract(ctx context.Context, chainID string, params *VerifyContractParams) (*VerifyContractResp, error)
	CheckContractStatus(ctx context.Context, chainID, guid string) (bool, error)
}

type etherscanAPI struct {
	client *resty.Client
}

var etherscanObj IEvmscan

// InitDefault inits default API
func InitDefault() {
	client := resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)
	etherscanObj = &etherscanAPI{client: client}
}

// Set inits with mock etherscan api
func Set(mockEtherscan IEvmscan) {
	etherscanObj = mockEtherscan
}

// Get get Etherscan API singleton
func Get() IEvmscan {
	return etherscanObj
}

func (e *etherscanAPI) getAPIKey(chainID string) string {
	if chainID == "ethereum" {
		chainID = "eth"
	}
	if chainID == "polygon" {
		chainID = "matic"
	}
	if chain := domain.IDToChain(chainID); chain != nil {
		return apiKeyMap[chain]
	}
	return ""
}

// Helper function to check if the rate limit is exceeded
func isRateLimitExceeded(resp *resty.Response) bool {
	if resp.StatusCode() == 429 || resp.StatusCode() == 502 {
		return true
	} else if resp.StatusCode() == 200 {
		var data map[string]interface{}
		err := json.Unmarshal(resp.Body(), &data)
		if err == nil {
			if result, ok := data["result"].(string); ok && strings.Contains(result, "rate limit reached") {
				return true
			}
		}
	}
	return false
}

// Helper function to check if the client timeout is exceeded
func isClientTimeout(err error) bool {
	return err != nil && strings.Contains(err.Error(), "Client.Timeout exceeded")
}

// Helper function to make an API request with API key
func (e *etherscanAPI) makeRequestWithAPIKey(ctx context.Context, chainID string, requestFn func(*resty.Request) (*resty.Response, error)) (*resty.Response, error) {
	var resp *resty.Response
	var err error

	request := e.client.R().SetContext(ctx)
	// in etherscan v2 we can use single api key for all chains
	request.SetQueryParam("apikey", e.getAPIKey("eth"))
	chainNum := domain.IDToChain(chainID).Number()
	request.SetQueryParam("chainid", fmt.Sprintf("%d", chainNum))
	resp, err = requestFn(request)

	if err != nil {
		if isClientTimeout(err) {
			return resp, code.ErrClientTimeoutExceeded
		}
		return resp, err
	} else if isRateLimitExceeded(resp) {
		return resp, code.ErrRateLimitExceeded
	} else if resp.StatusCode() >= 400 {
		kglog.ErrorWithDataCtx(ctx, "[Evmscan] API request failed", map[string]interface{}{
			"context":     request.Context().Value(core.RpcMethodKey),
			"status_code": resp.StatusCode(),
			"url":         request.URL,
			"message":     string(resp.Body()),
		})
		return resp, errors.New(resp.String())
	}

	return resp, nil
}
