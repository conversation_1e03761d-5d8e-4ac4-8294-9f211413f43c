package etherscanapi

import (
	"context"
	"errors"
	"fmt"
	"net/url"

	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	compilerVersion       = "v0.8.9+commit.e5eed63a"
	optimizationUsed      = "0"
	runs                  = "200"
	codeFormat            = "solidity-standard-json-input"
	verifiedStatusAlready = "Contract source code already verified"
)

// VerifyContractParams api params
type VerifyContractParams struct {
	SourceCode             string `url:"sourceCode"`
	ContractName           string `url:"contractname"`
	ContractAddress        string `url:"contractaddress"`
	ConstructorArgumentHex string `url:"constructorArguementHex"`
}

// VerifyContractResp api response
type VerifyContractResp struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Result  string `json:"result"`
}

// VerifyContract verify a contract
func (e *etherscanAPI) VerifyContract(ctx context.Context, chainID string, params *VerifyContractParams) (*VerifyContractResp, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] VerifyContract")
	defer span.End()

	formData := map[string]string{
		"sourceCode":           params.SourceCode,
		"contractname":         params.ContractName,
		"contractaddress":      params.ContractAddress,
		"constructorArguments": params.ConstructorArgumentHex,
		"module":               "contract",
		"action":               "verifysourcecode",
		"compilerversion":      compilerVersion,
		"optimizationUsed":     optimizationUsed,
		"codeformat":           codeFormat,
		"apikey":               e.getAPIKey(chainID),
		"runs":                 runs,
	}
	kglog.DebugWithDataCtx(ctx, "etherscan api, VerifyContract", map[string]interface{}{
		"chainID":                chainID,
		"contractname":           params.ContractName,
		"contractaddress":        params.ContractAddress,
		"constructorArgumentHex": params.ConstructorArgumentHex,
	})

	respData := VerifyContractResp{}
	resp, err := e.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "contract.Verify")).
		SetHeaders(map[string]string{
			"Content-Type": "application/x-www-form-urlencoded",
			"Accept":       "application/json",
		}).
		SetFormData(formData).
		SetResult(&respData).
		Post(apiUrlV2)

	if err != nil {
		return nil, err
	}
	if resp.StatusCode() >= 400 {
		return &respData, errors.New(resp.String())
	}
	return &respData, nil
}

// CheckContractStatusResp .
type CheckContractStatusResp struct {
	Status  string `json:"status"`
	Message string `json:"message"`
	Result  string `json:"result"`
}

// VerifyContractStatus .
type VerifyContractStatus string

const (
	// VerifyContractStatusSuccess .
	VerifyContractStatusSuccess VerifyContractStatus = "Pass - Verified"
	// VerifyContractStatusAlready .
	VerifyContractStatusAlready VerifyContractStatus = "Already Verified"
)

// CheckContractStatus check the verifing status of a contract
func (e *etherscanAPI) CheckContractStatus(ctx context.Context, chainID, guid string) (bool, error) {
	if guid == verifiedStatusAlready {
		return true, nil
	}
	ctx, span := tracing.Start(ctx, "[etherscanapi] CheckContractStatus")
	defer span.End()

	urlValue := url.Values{
		"apikey": {e.getAPIKey("eth")},
		"module": {"contract"},
		"action": {"checkverifystatus"},
		"guid":   {guid},
	}

	respData := CheckContractStatusResp{}
	resp, err := e.client.R().
		SetContext(context.WithValue(ctx, core.RpcMethodKey, "contract.checkStatus")).
		SetQueryParamsFromValues(urlValue).
		SetResult(&respData).
		Get(apiUrlV2)

	if err != nil {
		return false, err
	}
	if resp.StatusCode() >= 400 {
		return false, errors.New(resp.String())
	}

	if respData.Result == string(VerifyContractStatusSuccess) {
		return true, nil
	} else if respData.Result == string(VerifyContractStatusAlready) {
		return true, nil
	} else {
		kglog.InfoWithDataCtx(ctx, "CheckContractStatus verify false", fmt.Sprintf("%+v", respData))
		return false, nil
	}

}
