package etherscanapi

import (
	"context"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// GetTransactionReceiptResp api response
type GetTransactionReceiptResp struct {
	JSONRpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		BlockHash         string `json:"blockHash"`
		BlockNumber       string `json:"blockNumber"`
		ContractAddress   string `json:"contractAddress"`
		CumulativeGasUsed string `json:"cumulativeGasUsed"`
		From              string `json:"from"`
		GasUsed           string `json:"gasUsed"`
		LogsBloom         string `json:"logsBloom"`
		Logs              []Log  `json:"logs"`
		Status            string `json:"status"`
		To                string `json:"to"`
		TransactionHash   string `json:"transactionHash"`
		TransactionIndex  string `json:"transactionIndex"`
		Type              string `json:"type"`
	} `json:"result"`
}

// EthCallResp eth_call API response
type EthCallResp struct {
	JSONRpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  string `json:"result"`
}

// Log event log
type Log struct {
	Address          string   `json:"address"`
	BlockHash        string   `json:"blockHash"`
	BlockNumber      string   `json:"blockNumber"`
	Data             string   `json:"data"`
	LogIndex         string   `json:"logIndex"`
	Topics           []string `json:"topics"`
	TransactionHash  string   `json:"transactionHash"`
	TransactionIndex string   `json:"transactionIndex"`
}

// GetTransactionReceipt Returns the receipt of a transaction by transaction hash
func (e *etherscanAPI) GetTransactionReceipt(ctx context.Context, chainID, txhash string) (*GetTransactionReceiptResp, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] GetTransactionReceipt")
	defer span.End()

	respData := GetTransactionReceiptResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := GetTransactionReceiptResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "proxy.eth_getTransactionReceipt")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "proxy").
			SetQueryParam("action", "eth_getTransactionReceipt").
			SetQueryParam("txhash", txhash).
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	_, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, err
	}

	return &respData, nil
}

// EthCall returns result of calling eth_call
func (e *etherscanAPI) EthCall(ctx context.Context, chainID, from, to, data string) (*EthCallResp, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] EthCall")
	defer span.End()

	respData := EthCallResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := EthCallResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "proxy.eth_call")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "proxy").
			SetQueryParam("action", "eth_call").
			SetQueryParam("to", to).
			SetQueryParam("data", data).
			SetQueryParam("from", from).
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	_, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, err
	}

	return &respData, nil
}

// GetBlockByNumberResp information about a block
type GetBlockByNumberResp struct {
	Jsonrpc string `json:"jsonrpc"`
	ID      int    `json:"id"`
	Result  struct {
		Difficulty       string        `json:"difficulty"`
		ExtraData        string        `json:"extraData"`
		GasLimit         string        `json:"gasLimit"`
		GasUsed          string        `json:"gasUsed"`
		Hash             string        `json:"hash"`
		LogsBloom        string        `json:"logsBloom"`
		Miner            string        `json:"miner"`
		MixHash          string        `json:"mixHash"`
		Nonce            string        `json:"nonce"`
		Number           string        `json:"number"`
		ParentHash       string        `json:"parentHash"`
		ReceiptsRoot     string        `json:"receiptsRoot"`
		Sha3Uncles       string        `json:"sha3Uncles"`
		Size             string        `json:"size"`
		StateRoot        string        `json:"stateRoot"`
		Timestamp        string        `json:"timestamp"`
		TotalDifficulty  string        `json:"totalDifficulty"`
		Transactions     []interface{} `json:"transactions"`
		TransactionsRoot string        `json:"transactionsRoot"`
		Uncles           []interface{} `json:"uncles"`
	} `json:"result"`
}

// GetBlockByNumber Returns information about a block by block number.
func (e *etherscanAPI) GetBlockByNumber(ctx context.Context, chainID string, blockNum int) (*GetBlockByNumberResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "[etherscanapi] GetBlockByNumber")
	defer span.End()

	respData := GetBlockByNumberResp{}
	requestFn := func(request *resty.Request) (*resty.Response, error) {
		localRespData := GetBlockByNumberResp{}
		resp, err := request.
			SetContext(context.WithValue(ctx, core.RpcMethodKey, "proxy.eth_getBlockByNumber")).
			SetHeader("Content-Type", "application/json").
			SetQueryParam("module", "proxy").
			SetQueryParam("action", "eth_getBlockByNumber").
			SetQueryParam("tag", util.DecToHexWithPrefix(blockNum)).
			SetQueryParam("boolean", "false").
			SetResult(&localRespData).
			Get(apiUrlV2)
		respData = localRespData
		return resp, err
	}

	resp, err := e.makeRequestWithAPIKey(ctx, chainID, requestFn)
	if err != nil {
		return nil, resp.Time(), err
	}
	if isRateLimitExceeded(resp) {
		return &respData, resp.Time(), code.ErrRateLimitExceeded
	}

	return &respData, resp.Time(), nil
}
