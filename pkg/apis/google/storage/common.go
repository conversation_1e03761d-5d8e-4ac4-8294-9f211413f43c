package storage

import (
	"context"
	"mime"
	"time"

	"cloud.google.com/go/storage"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	storageAPIEndpoint = "https://storage.googleapis.com/storage/v1/"
	timeout            = 10 * time.Second
)

type IStorage interface {
	UploadImage(ctx context.Context, folder, fileName, image, mimeType string) (string, error)
	GetPublicURL(ctx context.Context, objectPath string) (string, error)
}

type storageAPI struct {
	client     resty.Client
	rl         domain.RateLimiter
	bucket     *storage.BucketHandle
	projectID  string
	bucketName string
}

var storageObj IStorage

func InitDefault(_rl domain.RateLimiter) {
	addAdditionalMimeTypes()

	projectID := config.GetString("PROJECT_ID")
	bucketName := config.GetString("GS_BUCKET_NAME")
	if projectID == "" {
		kglog.Error("[google storage] missing project ID")
		return
	}
	if bucketName == "" {
		kglog.Error("[google storage] missing Google Storage bucket name")
		return
	}

	client, err := storage.NewClient(context.Background())
	if err != nil {
		kglog.Error("[google storage] failed to create client: %v", err)
		return
	}

	bucket := client.Bucket(bucketName)

	storageObj = &storageAPI{
		client: resty.NewRestyClient().
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(storageAPIEndpoint).
			SetTimeout(timeout),
		rl:         _rl,
		bucket:     bucket,
		projectID:  projectID,
		bucketName: bucketName,
	}
}

func Get() IStorage {
	return storageObj
}

func addAdditionalMimeTypes() {
	mime.AddExtensionType(".jpg", "image/jpeg")
	mime.AddExtensionType(".jpeg", "image/jpeg")
	mime.AddExtensionType(".png", "image/png")
	mime.AddExtensionType(".gif", "image/gif")
	mime.AddExtensionType(".heic", "image/heic")
	mime.AddExtensionType(".heif", "image/heif")
	mime.AddExtensionType(".avif", "image/avif")
	mime.AddExtensionType(".webp", "image/webp")
}
