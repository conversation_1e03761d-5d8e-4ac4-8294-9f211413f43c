package storage

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"

	"fmt"

	"mime"
	"strings"

	"cloud.google.com/go/storage"
)

// UploadImage uploads a base64 encoded image to Google Storage and returns its public URL
func (gss *storageAPI) UploadImage(ctx context.Context, folder, fileName, image, mimeType string) (string, error) {
	extensions, err := mime.ExtensionsByType(mimeType)
	if err != nil || len(extensions) == 0 {
		return "", fmt.Errorf("unsupported MIME type: %s", mimeType)
	}
	fileExt := strings.TrimPrefix(extensions[0], ".")
	fullName := fmt.Sprintf("%s.%s", fileName, fileExt)

	imageBytes, err := util.Base64Decode([]byte(image))
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Base64 decode error", map[string]interface{}{
			"fileName": fileName,
			"error":    err.<PERSON>rror(),
		})
		return "", fmt.Errorf("base64 decode error: %w", err)
	}

	objectPath := fmt.Sprintf("%s/%s", folder, fullName)
	obj := gss.bucket.Object(objectPath)
	writer := obj.NewWriter(ctx)
	writer.PredefinedACL = "publicread"
	writer.CacheControl = "no-cache"
	_, err = writer.Write(imageBytes)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to write image to storage", map[string]interface{}{
			"objectPath": objectPath,
			"error":      err.Error(),
		})
		return "", fmt.Errorf("failed to write image: %w", err)
	}
	if err := writer.Close(); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to close writer", map[string]interface{}{
			"objectPath": objectPath,
			"error":      err.Error(),
		})
		return "", fmt.Errorf("failed to close writer: %w", err)
	}

	publicURL := fmt.Sprintf("https://storage.googleapis.com/%s/%s", gss.bucketName, objectPath)
	return publicURL, nil
}

// GetPublicURL generates the public URL for the given object path
func (gss *storageAPI) GetPublicURL(ctx context.Context, objectPath string) (string, error) {
	// Verify if the object exists
	obj := gss.bucket.Object(objectPath)
	attr, err := obj.Attrs(ctx)
	if err != nil {
		if errors.Is(err, storage.ErrObjectNotExist) {
			return "", fmt.Errorf("object does not exist: %w", err)
		}
		return "", fmt.Errorf("error fetching object attributes: %w", err)
	}

	// Should have all user read access
	aclFound := lo.ContainsBy(attr.ACL, func(acl storage.ACLRule) bool {
		return acl.Entity == "allUsers" && acl.Role == storage.RoleReader
	})
	if !aclFound {
		return "", fmt.Errorf("object does not have all users read access")
	}

	publicURL := fmt.Sprintf("https://storage.googleapis.com/%s/%s", gss.bucketName, objectPath)
	return publicURL, nil
}
