//go:generate mockgen -package=oauth -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/google/oauth -destination=oauth_mock.go . Service
package oauth

import (
	"net/url"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
)

const (
	oauthEndpoint               = "https://accounts.google.com/o/oauth2/v2/auth"
	exchangeAccessTokenEndpoint = "https://oauth2.googleapis.com/token"
	userInfoEndpoint            = "https://www.googleapis.com/oauth2/v3/userinfo"
	responseType                = "code"
	scope                       = "openid email profile"
	accessType                  = "offline"
	includeGrantedScopes        = "true"
	prompt                      = "consent select_account"

	timeout = 10 * time.Second
)

var (
	clientID     string
	clientSecret string
	redirectURI  string

	httpClient *resty.Client

	// GetService is a function that returns a Service
	GetService func() Service
)

func init() {
	var err error
	clientID = config.GetString("GOOGLE_OAUTH_CLIENT_ID")
	if clientID == "" {
		panic("missing google oauth client id")
	}
	clientSecret = config.GetString("GOOGLE_OAUTH_CLIENT_SECRET")
	if clientSecret == "" {
		panic("missing google oauth client secret")
	}
	redirectURI, err = url.JoinPath(config.GetString("SELF_HOST"), "/v1/oauth/google_callback")
	if err != nil {
		panic("invalid redirectURI")
	}

	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)

	GetService = NewGoogleOAuthService
}

// Service is an interface for interacting with google oauth
type Service interface {
	GenerateOAuthURL(state string) string
	ExchangeAccessToken(oauthCode string) (*AccessTokenResponse, *resty.Response, error)
	GetUserInfo(accessToken string) (*UserInfoResponse, *resty.Response, error)
}

// GoogleOAuthService is a service for interacting with google oauth
type GoogleOAuthService struct{}

// NewGoogleOAuthService returns a new GoogleOAuthService
func NewGoogleOAuthService() Service {
	return &GoogleOAuthService{}
}
