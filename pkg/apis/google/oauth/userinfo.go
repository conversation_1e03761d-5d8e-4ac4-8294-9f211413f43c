package oauth

import (
	"errors"

	"github.com/go-resty/resty/v2"
)

// UserInfoResponse is the response from getting user info
type UserInfoResponse struct {
	Sub           string `json:"sub"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Locale        string `json:"locale"`
}

// GetUserInfo gets user info from google
func (g *GoogleOAuthService) GetUserInfo(accessToken string) (*UserInfoResponse, *resty.Response, error) {
	res := &UserInfoResponse{}
	resp, err := httpClient.R().
		SetQueryParam("access_token", accessToken).
		SetResult(res).
		Get(userInfoEndpoint)
	if err != nil {
		return nil, resp, err
	}
	if resp.StatusCode() >= 400 {
		return nil, resp, errors.New("failed to get user info")
	}
	return res, resp, nil
}
