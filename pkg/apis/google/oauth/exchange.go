package oauth

import (
	"errors"
	"net/url"

	"github.com/go-resty/resty/v2"
)

// GenerateOAuthURL generates oauth url
func (g *GoogleOAuthService) GenerateOAuthURL(state string) string {
	query := url.Values{}
	query.Add("client_id", clientID)
	query.Add("redirect_uri", redirectURI)
	query.Add("response_type", responseType)
	query.Add("scope", scope)
	query.Add("access_type", accessType)
	query.Add("state", state)
	query.Add("include_granted_scopes", includeGrantedScopes)
	query.Add("prompt", prompt)
	return oauthEndpoint + "?" + query.Encode()
}

// AccessTokenResponse is the response from exchanging oauth code to access token
type AccessTokenResponse struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
	TokenType    string `json:"token_type"`
	IdToken      string `json:"id_token"`
}

// ExchangeAccessToken exchanges oauth code to access token
func (g *GoogleOAuthService) ExchangeAccessToken(oauthCode string) (*AccessTokenResponse, *resty.Response, error) {
	res := &AccessTokenResponse{}

	resp, err := httpClient.R().
		SetResult(res).
		SetFormData(map[string]string{
			"client_id":     clientID,
			"client_secret": clientSecret,
			"grant_type":    "authorization_code",
			"redirect_uri":  redirectURI,
			"code":          oauthCode,
		}).
		Post(exchangeAccessTokenEndpoint)

	if err != nil {
		return nil, resp, err
	}

	if resp.StatusCode() >= 400 {
		return nil, resp, errors.New("failed to exchange access token")
	}

	return res, resp, nil
}
