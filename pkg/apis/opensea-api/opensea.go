//go:generate go-enum
package openseaapi

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// define errors from opensea
var (
	Err404 = errors.New("NotFound")
)

type totalStats struct {
	Volume        float64 `json:"volume"`
	Sales         int     `json:"sales"`
	AveragePrice  float64 `json:"average_price"`
	NumOwners     int     `json:"num_owners"`
	MarketCap     float64 `json:"market_cap"`
	FloorPrice    float64 `json:"floor_price"`
	FloorPriceSym string  `json:"floor_price_symbol"`
}

type intervalStats struct {
	Interval     string  `json:"interval"`
	Volume       float64 `json:"volume"`
	VolumeDiff   float64 `json:"volume_diff"`
	VolumeChange float64 `json:"volume_change"`
	Sales        int     `json:"sales"`
	SalesDiff    float64 `json:"sales_diff"`
	AveragePrice float64 `json:"average_price"`
}

type openseaStatsResp struct {
	Total     totalStats      `json:"total"`
	Intervals []intervalStats `json:"intervals"`
}

// Stats .
type Stats struct {
	TotalVolume           float64
	FloorPrice            float64
	ThirtyDayVolume       float64
	AveragePrice          float64
	OneDayAveragePrice    float64
	SevenDayAveragePrice  float64
	ThirtyDayAveragePrice float64
}

// GetStats get stats of a collection
func (o *openseaAPI) GetStats(ctx context.Context, slug string) (*Stats, error) {
	if slug == "" {
		return nil, fmt.Errorf("slug is empty")
	}
	ctx, span := tracing.Start(ctx, "[opensea] GetStats")
	defer span.End()

	respData := openseaStatsResp{}
	resp, err := o.client.R().
		SetContext(ctx).
		SetPathParam("slug", slug).
		SetResult(&respData).
		Get(collectionStatsURI)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() == 404 || resp.StatusCode() == 400 {
		return nil, Err404
	}
	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("opensea, GetStats error, status code: %d. Body: %s", resp.StatusCode(), string(resp.Body()))
	}

	stat := Stats{
		TotalVolume:  respData.Total.Volume,
		FloorPrice:   respData.Total.FloorPrice,
		AveragePrice: respData.Total.AveragePrice,
	}
	for _, interval := range respData.Intervals {
		if interval.Interval == "thirty_day" {
			stat.ThirtyDayVolume = interval.Volume
			stat.ThirtyDayAveragePrice = interval.AveragePrice
		} else if interval.Interval == "seven_day" {
			stat.SevenDayAveragePrice = interval.AveragePrice
		} else if interval.Interval == "one_day" {
			stat.OneDayAveragePrice = interval.AveragePrice
		}
	}

	return &stat, nil
}

// AssetResp .
type AssetResp struct {
	TokenID         string       `json:"identifier"`
	ImageURL        string       `json:"image_url"`
	AnimationURL    string       `json:"animation_url"`
	Name            string       `json:"name"`
	Description     string       `json:"description"`
	ContractAddress string       `json:"contract"`
	SchemaName      string       `json:"token_standard"`
	Slug            string       `json:"collection"`
	Traits          *interface{} `json:"traits"`
}

// NFTAssetResp .
type NFTAssetResp struct {
	AssetResp `json:"nft"`
}

// LastSaleData .
// type LastSaleData struct {
// 	TotalPrice   string `json:"total_price"`
// 	PaymentToken struct {
// 		Symbol   string `json:"symbol"`
// 		Decimals int32  `json:"decimals"`
// 		EthPrice string `json:"eth_price"`
// 		UsdPrice string `json:"usd_price"`
// 	} `json:"payment_token"`
// }

// type sellOrder struct {
// 	ListingTime int32 `json:"listing_time"`
// }

// GetAsset get asset by contract address and tokenID
func (o *openseaAPI) GetAsset(ctx context.Context, chainID, contractAddress, tokenID string) (*AssetResp, error) {
	ctx, span := tracing.Start(ctx, "[opensea] GetAsset")
	defer span.End()

	chainID = ChainIDToOpenSeaChain[chainID]

	respData := NFTAssetResp{}
	resp, err := o.client.R().
		SetContext(ctx).
		SetPathParams(map[string]string{
			"chain_id": chainID,
			"contract": contractAddress,
			"token_id": tokenID,
		}).
		SetResult(&respData).
		Get(assetDetailsURI)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() == 404 {
		return nil, Err404
	}
	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("opensea, GetAsset error, status code: %d", resp.StatusCode())
	}

	assetResp := respData.AssetResp
	// make schema compatible with old data
	assetResp.SchemaName = strings.ToUpper(assetResp.SchemaName)
	return &assetResp, nil
}

// GetAssetsParams .
type GetAssetsParams struct {
	Owner   string  `json:"owner"`
	ChainID string  `json:"chain"`
	Limit   string  `json:"limit"`
	Next    *string `json:"next"`
}

// ToURLValues convert GetAssetsParams to url.Values
func (params *GetAssetsParams) ToURLValues() *url.Values {
	values := &url.Values{}
	if params.Limit != "" {
		values.Set("limit", params.Limit)
	}
	if params.Next != nil && *params.Next != "" {
		values.Set("next", *params.Next)
	}
	return values
}

// AssetsResp .
type AssetsResp struct {
	Next   *string     `json:"next"`
	Assets []AssetResp `json:"nfts"`
}

// GetAssets get assets by params
func (o *openseaAPI) GetAssets(ctx context.Context, params *GetAssetsParams) (*AssetsResp, error) {
	ctx, span := tracing.Start(ctx, "[opensea] GetAssets")
	defer span.End()

	params.ChainID = ChainIDToOpenSeaChain[params.ChainID]
	respData := AssetsResp{}
	resp, err := o.client.
		SetTimeout(10 * time.Second).R().
		SetContext(ctx).
		SetPathParams(map[string]string{
			"chain_id": params.ChainID,
			"address":  params.Owner,
		}).
		SetQueryParamsFromValues(*params.ToURLValues()).
		SetResult(&respData).
		Get(assetsURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "opensea, GetAssets error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}
	if resp.StatusCode() == 404 {
		return nil, Err404
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "opensea, GetAssets error", map[string]interface{}{
			"response": resp.String(),
		})
		return nil, fmt.Errorf("opensea, GetAssets error, status code: %d. %s", resp.StatusCode(), resp.String())
	}

	for i := range respData.Assets {
		asset := &respData.Assets[i]
		asset.SchemaName = strings.ToUpper(asset.SchemaName)
	}
	return &respData, nil
}

// Metadata of an asset
type Metadata struct {
	Image        string        `json:"image"`
	Name         string        `json:"name"`
	Description  *string       `json:"description"`
	AnimationURL *string       `json:"animation_url"`
	Traits       []interface{} `json:"traits"`
}

// GetMetadata get metadata from opensea
func (o *openseaAPI) GetMetadata(ctx context.Context, chainID, contractAddress, tokenID string) (*Metadata, error) {
	ctx, span := tracing.Start(ctx, "[opensea] GetMetadata")
	defer span.End()

	chain := ChainIDToOpenSeaChain[chainID]
	respData := Metadata{}
	resp, err := o.client.R().
		SetContext(ctx).
		SetPathParams(map[string]string{
			"chain":    chain,
			"contract": contractAddress,
			"token_id": tokenID,
		}).
		SetResult(&respData).
		Get(nftMetadataURI)

	if err != nil {
		return nil, err
	}
	if resp.StatusCode() == 404 {
		return nil, Err404
	}
	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("opensea, GetMetadata error, status code: %d. %s", resp.StatusCode(), resp.String())
	}

	imageFilter(&respData)
	return &respData, nil
}

func imageFilter(metadata *Metadata) {
	if metadata == nil {
		return
	}
	// https://opensea.io/collection/vitalic-of-the-apes
	if metadata.Image == "https://openseauserdata.com/files/3477a5cb8accf4449cb24bcb26446f37.jpg" ||
		metadata.Image == "https://openseauserdata.com/files/468fccc90259d83de7a9031c0b0d3502.jpg" {
		metadata.Image = ""
	}
}

// CollectionResp .
type CollectionResp struct {
	Name          string                   `json:"name"`
	Description   string                   `json:"description"`
	ImageURL      string                   `json:"image_url"`
	LargeImageURL string                   `json:"banner_image_url"`
	Contracts     []map[string]interface{} `json:"contracts"`
}

// GetCollection get nft collection metadata by opensea slug
func (o *openseaAPI) GetCollection(ctx context.Context, slug string) (*CollectionResp, error) {
	ctx, span := tracing.Start(ctx, "[opensea] GetCollection")
	defer span.End()

	respData := CollectionResp{}
	resp, err := o.client.SetTimeout(10*time.Second).R().
		SetContext(ctx).
		SetPathParam("slug", slug).
		SetResult(&respData).
		Get(collectionURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "opensea, GetCollection error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}
	if resp.StatusCode() == 404 {
		return nil, Err404
	}
	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("opensea, GetCollection error, status code: %d. %s", resp.StatusCode(), resp.String())
	}

	return &respData, nil
}

// RefreshMetadata refresh metadata of an NFT tokenID, and only for ETH
func (o *openseaAPI) RefreshMetadata(ctx context.Context, chainID, contractAddress, tokenID string) error {
	ctx, span := tracing.Start(ctx, "[opensea] RefreshMetadata")
	defer span.End()

	chainID = ChainIDToOpenSeaChain[chainID]
	_, err := o.client.R().
		SetContext(ctx).
		SetPathParams(map[string]string{
			"chain_id": chainID,
			"contract": contractAddress,
			"token_id": tokenID,
		}).
		Post(assetMetadataRefreshURI)

	if err != nil {
		return err
	}
	return nil
}

// Order .
type Order struct {
	Side      NftOrderSide `json:"side"`
	Price     string       `json:"current_price"`
	StartTime int          `json:"listing_time"`
	EndTime   int          `json:"expiration_time"`
}

// NftOrderSide .
// ENUM(ask, bid)
type NftOrderSide string

// ListingsResp .
type ListingsResp struct {
	Orders []Order `json:"orders"`
}

// GetMarketplaceLists get marketplace lists
func (o *openseaAPI) GetMarketplaceLists(ctx context.Context, chainID, contractAddress, tokenID string) ([]Order, error) {
	ctx, span := tracing.Start(ctx, "[opensea] GetMarketplaceLists")
	defer span.End()

	chainID = ChainIDToOpenSeaChain[chainID]
	respData := ListingsResp{}
	resp, err := o.client.R().
		SetContext(ctx).
		SetPathParams(map[string]string{
			"chain_id": chainID,
			"protocol": marketplaceProtocol,
		}).
		SetQueryParam("asset_contract_address", contractAddress).
		SetQueryParam("token_ids", tokenID).
		SetResult(&respData).
		Get(marketplaceListsURI)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() == 404 {
		return nil, Err404
	}
	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("opensea, GetOrders error, status code: %d. %s", resp.StatusCode(), resp.String())
	}

	return respData.Orders, nil
}
