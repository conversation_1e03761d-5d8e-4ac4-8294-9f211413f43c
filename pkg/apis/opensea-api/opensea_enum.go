// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package openseaapi

import (
	"errors"
	"fmt"
)

const (
	// NftOrderSideAsk is a NftOrderSide of type ask.
	NftOrderSideAsk NftOrderSide = "ask"
	// NftOrderSideBid is a NftOrderSide of type bid.
	NftOrderSideBid NftOrderSide = "bid"
)

var ErrInvalidNftOrderSide = errors.New("not a valid NftOrderSide")

// String implements the Stringer interface.
func (x NftOrderSide) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NftOrderSide) IsValid() bool {
	_, err := ParseNftOrderSide(string(x))
	return err == nil
}

var _NftOrderSideValue = map[string]NftOrderSide{
	"ask": NftOrderSideAsk,
	"bid": NftOrderSideBid,
}

// ParseNftOrderSide attempts to convert a string to a NftOrderSide.
func ParseNftOrderSide(name string) (NftOrderSide, error) {
	if x, ok := _NftOrderSideValue[name]; ok {
		return x, nil
	}
	return NftOrderSide(""), fmt.Errorf("%s is %w", name, ErrInvalidNftOrderSide)
}
