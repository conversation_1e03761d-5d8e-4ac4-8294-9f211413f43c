package openseaapi

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	myModel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

var (
	apiKey string
)

const (
	baseURI                 = "https://api.opensea.io"
	collectionStatsURI      = "/api/v2/collections/{slug}/stats"
	assetDetailsURI         = "/api/v2/chain/{chain_id}/contract/{contract}/nfts/{token_id}"
	assetsURI               = "/api/v2/chain/{chain_id}/account/{address}/nfts"
	nftMetadataURI          = "/api/v2/metadata/{chain}/{contract}/{token_id}"
	collectionURI           = "/api/v2/collections/{slug}"
	assetMetadataRefreshURI = "/api/v2/chain/{chain_id}/contract/{contract}/nfts/{token_id}/refresh"
	marketplaceListsURI     = "/api/v2/orders/{chain_id}/{protocol}/listings"

	chainIDEthereum = "ethereum"
	chainIDMatic    = "matic"
	chainIDPolygon  = "polygon"

	marketplaceProtocol = "seaport"
)

// ChainIDToOpenSeaChain maps chain id to OpenSea chain name
var ChainIDToOpenSeaChain map[string]string = map[string]string{
	myModel.NftChainIDEthereum: chainIDEthereum,
	myModel.NftChainIDPolygon:  chainIDMatic,
	myModel.ChainIDEthereum:    chainIDEthereum,
	myModel.ChainIDPolygon:     chainIDMatic,
}

// OpenseaChainToAssetChainID maps OpenSea chain name to asset chain id
var OpenseaChainToAssetChainID = map[string]string{
	chainIDEthereum: myModel.ChainIDEthereum,
	chainIDMatic:    myModel.ChainIDPolygon,
	chainIDPolygon:  myModel.ChainIDPolygon,
}

// IOpensea defines the interface for openseaAPI
type IOpensea interface {
	GetStats(ctx context.Context, slug string) (*Stats, error)
	GetAsset(ctx context.Context, chainID, contractAddress, tokenID string) (*AssetResp, error)
	GetAssets(ctx context.Context, params *GetAssetsParams) (*AssetsResp, error)
	GetMetadata(ctx context.Context, chainID, contractAddress, tokenID string) (*Metadata, error)
	GetCollection(ctx context.Context, slug string) (*CollectionResp, error)
	RefreshMetadata(ctx context.Context, chainID, contractAddress, tokenID string) error
	GetMarketplaceLists(ctx context.Context, chainID, contractAddress, tokenID string) ([]Order, error)
}

type openseaAPI struct {
	client resty.Client
}

var openseaObj IOpensea

// InitDefault inits default API
func InitDefault() {
	apiKey = config.GetString("OPENSEA_API_KEY")
	if apiKey == "" {
		kglog.Error("Cannot get OpenSea api key")
	}
	client := resty.NewRestyClient()
	openseaObj = &openseaAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(baseURI).
			SetHeader("Content-Type", "application/json").
			SetHeader("Accept", "application/json").
			SetHeader("X-API-KEY", apiKey),
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client) {
	openseaObj = &openseaAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(baseURI).
			SetHeader("Content-Type", "application/json").
			SetHeader("Accept", "application/json").
			SetHeader("X-API-KEY", apiKey),
	}
}

// Get get KCC API singleton
func Get() IOpensea {
	return openseaObj
}
