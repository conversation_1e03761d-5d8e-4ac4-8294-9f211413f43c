package ankr

import (
	context "context"
	"strconv"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/samber/lo"
)

type transactionResp struct {
	Hash     string `json:"hash"`
	From     string `json:"from"`
	To       string `json:"to"`
	Value    string `json:"value"`
	Gas      string `json:"gas"`
	GasPrice string `json:"gasPrice"`
	Nonce    string `json:"nonce"`
	Input    string `json:"input"`
}

type getBlockByNumberResult struct {
	Number       string            `json:"number"`
	Hash         string            `json:"hash"`
	ParentHash   string            `json:"parentHash"`
	Nonce        string            `json:"nonce"`
	Sha3Uncles   string            `json:"sha3Uncles"`
	Transactions []transactionResp `json:"transactions"`
}

type getBlockByNumberResp struct {
	Result *getBlockByNumberResult `json:"result"`
}

func (a *ankrAPI) GetBlockByNumber(ctx context.Context, chainID string, blockNumber int64) (*domain.Block, error) {
	url, err := getRPCUrl(chainID)
	if err != nil {
		return nil, err
	}

	payload := map[string]interface{}{
		"method":  "eth_getBlockByNumber",
		"params":  []interface{}{"0x" + strconv.FormatInt(blockNumber, 16), true},
		"id":      1,
		"jsonrpc": "2.0",
	}

	respData := getBlockByNumberResp{}

	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := getBlockByNumberResp{}
		resp, err := request.
			SetBody(payload).
			SetResult(&localRespData).
			Post(url)
		respData = localRespData
		return resp, err
	}

	_, err = a.makeRequest(ctx, requestFn)
	if err != nil {
		return nil, err
	}

	if respData.Result == nil {
		return nil, code.ErrBlockNotFound
	}

	block := &domain.Block{
		Number:     respData.Result.Number,
		Hash:       respData.Result.Hash,
		ParentHash: respData.Result.ParentHash,
		Nonce:      respData.Result.Nonce,
		Transactions: lo.Map(respData.Result.Transactions, func(tx transactionResp, _ int) *domain.Transaction {
			return &domain.Transaction{
				Hash:  tx.Hash,
				From:  tx.From,
				To:    tx.To,
				Value: tx.Value,
				Input: tx.Input,
			}
		}),
	}

	return block, nil
}
