package ankr

import (
	"context"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

const (
	usdtAddress   = "******************************************"
	transferTopic = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
)

type Log struct {
	BlockNumber      string   `json:"blockNumber"`
	Data             string   `json:"data"`
	Topics           []string `json:"topics"`
	TransactionHash  string   `json:"transactionHash"`
	TransactionIndex string   `json:"transactionIndex"`
	Address          string   `json:"address"`
	BlockHash        string   `json:"blockHash"`
	Removed          bool     `json:"removed"`
	LogIndex         string   `json:"logIndex"`
}

type getTronLogsResp struct {
	Result []Log `json:"result"`
}

// GetLogs get USDT transfer logs
func (a *ankrAPI) GetLogs(ctx context.Context, chainID string, fromBlock int64, toBlock int64) ([]*domain.Log, error) {
	ctx, span := tracing.Start(ctx, "[ankrAPI] GetLogs")
	defer span.End()

	payload := map[string]interface{}{
		"method": "eth_getLogs",
		"params": []map[string]interface{}{
			{
				"fromBlock": "0x" + strconv.FormatInt(fromBlock, 16),
				"toBlock":   "0x" + strconv.FormatInt(toBlock, 16),
				"address":   usdtAddress,
				"topics": []string{
					transferTopic,
				},
			},
		},
		"id":      1,
		"jsonrpc": "2.0",
	}

	respData := getTronLogsResp{}
	url, err := getRPCUrl(chainID)
	if err != nil {
		return nil, err
	}

	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := getTronLogsResp{}
		resp, err := request.
			SetBody(payload).
			SetResult(&localRespData).
			Post(url)
		respData = localRespData
		return resp, err
	}

	_, err = a.makeRequest(ctx, requestFn)
	if err != nil {
		return nil, err
	}

	result := make([]*domain.Log, 0, len(respData.Result))
	for _, log := range respData.Result {
		result = append(result, &domain.Log{
			Address:         log.Address,
			Data:            log.Data,
			Topics:          log.Topics,
			BlockNumber:     log.BlockNumber,
			TransactionHash: log.TransactionHash,
		})
	}
	return result, nil
}
