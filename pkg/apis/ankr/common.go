package ankr

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiHost = "https://rpc.ankr.com/"
	timeout = 10 * time.Second
)

const (
	tronRPCUrl = "tron_jsonrpc"
)

func getRPCUrl(chainID string) (string, error) {
	switch chainID {
	case dbmodel.ChainIDTron:
		return tronRPCUrl, nil
	default:
		return "", code.ErrUnsupportedChainID
	}
}

// IAnkr defines the interface for ankr api
type IAnkr interface {
	GetLogs(ctx context.Context, chainID string, fromBlock int64, toBlock int64) ([]*domain.Log, error)
	GetBlockNumber(ctx context.Context, chainID string) (int64, error)
	GetBlockByNumber(ctx context.Context, chainID string, blockNumber int64) (*domain.Block, error)
}

type ankrAPI struct {
	client resty.Client
}

var ankrObj IAnkr

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	ankrObj = &ankrAPI{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).SetTimeout(timeout),
	}
}

// Get returns the ankr api object
func Get() IAnkr {
	return ankrObj
}

// Set inits with mock ankr api
func Set(mockAnkr *MockIAnkr) {
	ankrObj = mockAnkr
}

// InitResty inits with resty.Client for mocking
func InitResty(client resty.Client) {
	ankrObj = &ankrAPI{
		client: client,
	}
}

// Helper function to make an API request with API key retry logic
func (a *ankrAPI) makeRequest(ctx context.Context, requestFn func(resty.Request) (*resty.Response, error)) (*resty.Response, error) {
	var resp *resty.Response
	var err error

	request := a.client.R().SetContext(ctx)
	resp, err = requestFn(request)

	if err != nil {
		var urlErr *url.Error
		if errors.As(err, &urlErr) && errors.Is(urlErr.Err, context.DeadlineExceeded) {
			return nil, context.DeadlineExceeded
		}
		return resp, err
	}

	if resp.StatusCode() == 429 {
		return nil, code.ErrRateLimitExceeded
	} else if resp.StatusCode() >= 400 {
		return resp, fmt.Errorf("API request failed with status code %d and message: %s", resp.StatusCode(), string(resp.Body()))
	}

	return resp, nil
}
