package ankr

import (
	"context"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type getTronBlockNumberResp struct {
	Result string `json:"result"`
}

// GetBlockNumber get block number
func (a *ankrAPI) GetBlockNumber(ctx context.Context, chainID string) (int64, error) {
	ctx, span := tracing.Start(ctx, "[ankrAPI] GetBlockNumber")
	defer span.End()

	url, err := getRPCUrl(chainID)
	if err != nil {
		return 0, err
	}

	payload := map[string]interface{}{
		"method":  "eth_blockNumber",
		"params":  []interface{}{},
		"id":      1,
		"jsonrpc": "2.0",
	}

	respData := getTronBlockNumberResp{}

	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := getTronBlockNumberResp{}
		resp, err := request.
			SetBody(payload).
			SetResult(&localRespData).
			Post(url)
		respData = localRespData
		return resp, err
	}

	_, err = a.makeRequest(ctx, requestFn)
	if err != nil {
		return 0, err
	}

	blockNumber, err := strconv.ParseInt(respData.Result[2:], 16, 64)
	if err != nil {
		return 0, err
	}

	return blockNumber, nil
}
