package feeeapi

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type createOrderResp struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
	Data    *struct {
		OrderNo   string  `json:"order_no"`
		PayAmount float64 `json:"pay_amount"`
	} `json:"data"`
}

// CreateOrder creates energy rental order for an address. Only supports renting 1h for now. Will return paid TRX amount.
func (c *FeeeClient) CreateOrder(ctx context.Context, address string, energy int) (float64, error) {
	ctx, span := tracing.Start(ctx, "feee.CreateOrder")
	defer span.End()

	respData := createOrderResp{}
	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("key", c.api<PERSON>ey).
		SetBody(map[string]interface{}{
			"resource_type":   1,
			"receive_address": address,
			"resource_value":  energy,
			"rent_duration":   10,
			"rent_time_unit":  "m",
		}).
		SetResult(&respData).
		Post("/v2/order/submit")

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Feee, CreateOrder error", err.Error())
		return 0, err
	}
	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "Feee, CreateOrder status error", resp.String())
		return 0, errors.New(resp.String())
	}
	if respData.Code != 0 {
		kglog.WarningWithDataCtx(ctx, "Feee, CreateOrder code non-zero", respData)
		return 0, errors.New(respData.Message)
	}
	if respData.Data == nil {
		kglog.WarningWithDataCtx(ctx, "Feee, CreateOrder data nil", respData)
		return 0, errors.New("create order data nil")
	}
	kglog.InfoWithDataCtx(ctx, "Feee, CreateOrder success", *respData.Data)
	return (*respData.Data).PayAmount, nil
}
