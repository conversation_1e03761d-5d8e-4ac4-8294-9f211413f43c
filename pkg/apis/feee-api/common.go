//go:generate mockgen -package=feeeapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/feee-api -destination=feee_mock.go . IFeee

package feeeapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

// IFeee .
type IFeee interface {
	AccountInfo(ctx context.Context) (*AccountInfo, error)
	CreateOrder(ctx context.Context, address string, energy int) (float64, error)
}

// FeeeClient .
type FeeeClient struct {
	client resty.Client
	apiKey string
}

var instance IFeee

// Get returns the singleton
func Get() IFeee {
	return instance
}

// default api host
const apiHost = "https://feee.io/open"

// InitDefault inits default API
func InitDefault() {
	apiKey := config.GetString("FEEE_API_KEY")
	if apiKey == "" {
		kglog.Error("Cannot get feee api key")
	}
	timeout := time.Duration(15 * time.Second)
	client := resty.NewRestyClient().
		SetBaseURL(apiHost).
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)
	instance = &FeeeClient{
		client: client,
		apiKey: apiKey,
	}
}

// Init sets the instance directly
func Init(i IFeee) {
	instance = i
}
