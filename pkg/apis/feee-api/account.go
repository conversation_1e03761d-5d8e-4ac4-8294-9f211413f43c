package feeeapi

import (
	"context"
	"errors"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type accountInfoResp struct {
	Code    int          `json:"code"`
	Message string       `json:"msg"`
	Data    *AccountInfo `json:"data"`
}

// AccountInfo
type AccountInfo struct {
	TrxMoney        float64 `json:"trx_money"`
	RechargeAddress string  `json:"recharge_address"`
	TrxAddress      string  `json:"trx_address"` // bound tron address for recharge
}

// AccountInfo gets account info
func (c *FeeeClient) AccountInfo(ctx context.Context) (*AccountInfo, error) {
	respData := accountInfoResp{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		resp, err := c.client.R().
			SetContext(ctx).
			SetHeader("Content-Type", "application/json").
			SetHeader("key", c.apiKey).
			SetResult(&respData).
			Get("/v2/api/query")

		if err != nil {
			kglog.WarningWithDataCtx(ctx, "Feee, AccountInfo error", map[string]interface{}{
				"error": err.Error(),
			})
			return err
		}

		if resp.StatusCode() >= 500 {
			kglog.WarningWithDataCtx(ctx, "Feee, AccountInfo status error", map[string]interface{}{
				"response": resp.String(),
			})
			return code.ErrRateLimitExceeded
		}

		if resp.StatusCode() >= 400 {
			kglog.WarningWithDataCtx(ctx, "Feee, AccountInfo status error", map[string]interface{}{
				"response": resp.String(),
			})
			return errors.New(resp.String())
		}

		if respData.Code != 0 {
			kglog.WarningWithDataCtx(ctx, "Feee, AccountInfo code non-zero", respData)
			return errors.New(respData.Message)
		}

		if respData.Data == nil {
			kglog.WarningWithDataCtx(ctx, "Feee, AccountInfo data nil", respData)
			return errors.New("account info data nil")
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	kglog.InfoWithDataCtx(ctx, "Feee, AccountInfo success", *respData.Data)
	return respData.Data, nil
}
