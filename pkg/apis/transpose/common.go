package transpose

import (
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

var (
	apiKey string

	timeout    = time.Duration(10 * time.Second)
	httpClient *resty.Client
)

const (
	baseURI     = "https://api.transpose.io"
	nftSalesURI = "/nft/sales-by-token-id"
)

func init() {
	apiKey = config.GetString("TRANSPOSE_API_KEY")
	if apiKey == "" {
		kglog.Warning("Cannot get transpose api key")
	}

	httpClient = resty.New().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout).
		SetBaseURL(baseURI).
		SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetHeader("X-API-KEY", apiKey)
}

// Client is the client for interacting with compliance
type Client struct {
	*resty.Client
}

// NewClient returns a new compliance client
func NewClient() domain.NftMarketClientI {
	return &Client{
		httpClient,
	}
}
