package transpose

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// GetNftSales returns nft sales
func (c *Client) GetNftSales(ctx context.Context, params *domain.GetNftSalesParams) (*domain.GetNftSalesData, *resty.Response, error) {
	ctx, span := tracing.Start(ctx, "[transpose] GetNftSales")
	defer span.End()

	// default params
	params.Order = "desc"
	params.Limit = 1

	respData := &domain.GetNftSalesResp{}
	resp, err := c.R().
		SetContext(ctx).
		SetResult(respData).
		SetQueryParam("chain_id", params.ChainID).
		SetQueryParam("contract_address", params.ContractAddress).
		SetQueryParam("token_id", params.TokenID).
		Get(nftSalesURI)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "GetNftSales error", map[string]any{
			"params": fmt.Sprintf("%+v", params),
			"error":  err.Error(),
		})
		return nil, resp, err
	}

	if resp.StatusCode() != 200 {
		kglog.ErrorWithDataCtx(ctx, "GetNftSales error", map[string]any{
			"params":   fmt.Sprintf("%+v", params),
			"resp":     resp.String(),
			"respData": respData,
		})
		return nil, resp, fmt.Errorf("GetNftSales error: %s", resp.String())
	}

	if len(respData.Results) == 0 {
		return nil, resp, fmt.Errorf("GetNftSales error: no result")
	}

	return &respData.Results[0], resp, nil
}
