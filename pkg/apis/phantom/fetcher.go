package phantom

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

type metadataFetcherImpl struct{}

func MetadataFetcher() domain.TokenMetadataFetcher {
	return &metadataFetcherImpl{}
}

func (f *metadataFetcherImpl) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Ethereum, domain.Polygon, domain.BaseChain, domain.Solana}
}

func (f *metadataFetcherImpl) FetchMetadata(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
	ctx, span := tracing.Start(ctx, "phantom.FetchMetadata")
	defer span.End()

	if !lo.Contains(f.Supported<PERSON>hai<PERSON>(), chain) {
		return nil, domain.ErrUnsupportedChain
	}
	phantomChainID := "solana:101"
	if chain.IsEVM() {
		phantomChainID = fmt.Sprintf("eip155:%d", chain.Number())
		tokenID = strings.ToLower(tokenID)
	}

	var response TokenMetadataResponse
	var resp *resty.Response
	var err error

	// Retry logic for 429 status code
	maxRetries := 3
	retryDelays := []time.Duration{500 * time.Millisecond, 1 * time.Second}
	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err = makeRequest(ctx, func(request resty.Request) (*resty.Response, error) {
			return request.
				SetPathParam("address", tokenID).
				SetPathParam("chain", phantomChainID).
				SetResult(&response).
				Get(metadataURL)
		})

		if resp != nil {
			if resp.StatusCode() == 404 {
				return nil, domain.ErrRecordNotFound
			} else if resp.StatusCode() == 429 && attempt < maxRetries {
				// Rate limited, retry after delay
				delay := retryDelays[min(attempt, len(retryDelays)-1)]
				kglog.InfofCtx(ctx, "Rate limited (429), retrying after %v (attempt %d/%d)", delay, attempt+1, maxRetries)
				time.Sleep(delay)
				continue
			} else if resp.StatusCode() >= 400 {
				return nil, errors.New(resp.String())
			}
		}

		// If we got here without a 429 status code, break the retry loop
		break
	}

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to fetch token metadata", map[string]interface{}{
			"error": err.Error(),
			"chain": chain,
			"token": tokenID,
		})
		return nil, err
	}

	metadata := response.Data
	if metadata.Name == "" && metadata.Symbol == "" && metadata.Decimals == 0 {
		return nil, domain.ErrRecordNotFound
	}
	return &domain.TokenMetadata{
		Name:       metadata.Name,
		Symbol:     metadata.Symbol,
		Decimals:   uint(metadata.Decimals),
		LogoUrl:    metadata.LogoURI,
		IsVerified: metadata.SpamStatus != "SPAM" && metadata.SpamStatus != "POSSIBLE_SPAM",
	}, nil
}

type assetFetcherImpl struct{}

func AssetFetcher() domain.AssetFetcher {
	return &assetFetcherImpl{}
}

func (f *assetFetcherImpl) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Solana}
}

func (f *assetFetcherImpl) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken}
}

func (f *assetFetcherImpl) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	ctx, span := tracing.Start(ctx, "phantom.GetAssets")
	defer span.End()

	if len(chains) != 1 || chains[0] != domain.Solana {
		return nil, domain.ErrUnsupportedChain
	}
	if len(types) != 1 || types[0] != domain.AssetTypeToken {
		return nil, domain.ErrUnsupportedAssetType
	}

	// Get tokens
	tokensResp, err := f.getTokens(ctx, address.String())
	if err != nil {
		return nil, err
	}

	// Prepare price requests
	priceReq := PriceRequest{Tokens: make([]TokenPriceRequest, 0, len(tokensResp.Tokens))}

	// Add native SOL
	priceReq.Tokens = append(priceReq.Tokens, TokenPriceRequest{
		Token: TokenIdentifier{
			ChainId:      "solana:101",
			Slip44:       "501",
			ResourceType: "nativeToken",
		},
	})

	// Add other tokens
	for _, token := range tokensResp.Tokens {
		if token.Type == "SPL" {
			priceReq.Tokens = append(priceReq.Tokens, TokenPriceRequest{
				Token: TokenIdentifier{
					ChainId:      "solana:101",
					Address:      token.Data.MintAddress,
					ResourceType: "address",
				},
			})
		}
	}

	// Get prices
	pricesResp, err := getPrices(ctx, priceReq)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to fetch token prices", map[string]interface{}{
			"error": err.Error(),
		})
		// Continue without prices
	}

	// Convert to domain.TokenAmount
	tokenAmounts := make([]*domain.TokenAmount, 0, len(tokensResp.Tokens))
	for _, token := range tokensResp.Tokens {
		amount, err := decimal.NewFromString(token.Data.Amount)
		if err != nil {
			kglog.WarningWithDataCtx(ctx, "Failed to parse token amount", map[string]interface{}{
				"error":  err.Error(),
				"amount": token.Data.Amount,
			})
			continue
		}

		// Convert to actual amount by dividing by 10^decimals
		amount = amount.Div(decimal.New(1, int32(token.Data.Decimals)))

		var price *float64
		if pricesResp != nil {
			var priceKey string
			if token.Type == "SolanaNative" {
				priceKey = "solana:101/nativeToken:501"
			} else {
				priceKey = fmt.Sprintf("solana:101/address:%s", token.Data.MintAddress)
			}
			if priceData, ok := pricesResp.Prices[priceKey]; ok {
				price = &priceData.Price
			} else {
				price = util.Ptr(float64(0))
			}
		}

		tokenID := token.Data.MintAddress
		if token.Type == "SolanaNative" {
			tokenID = domain.Solana.MainToken().ID()
		}

		isSpam := token.Data.SpamStatus == "SPAM" || token.Data.SpamStatus == "POSSIBLE_SPAM" || util.Val(price) == 0
		tokenAmounts = append(tokenAmounts, &domain.TokenAmount{
			Token: domain.NewToken(
				domain.Solana,
				tokenID,
				token.Data.Name,
				token.Data.Symbol,
				token.Data.LogoUri,
				uint(token.Data.Decimals),
				!isSpam,
			),
			Amount: amount,
			Price:  price,
			Metadata: domain.SolanaWalletTokenMetadata{
				TokenAccount: token.Data.SplTokenAccountKey,
			},
		})
	}

	return &domain.AggregatedAssets{
		Tokens: tokenAmounts,
	}, nil
}

func (f *assetFetcherImpl) getTokens(ctx context.Context, address string) (*TokensResponse, error) {
	req := TokensRequest{
		Addresses: []AddressRequest{{
			ChainId: "solana:101",
			Address: address,
		}},
	}

	var resp TokensResponse
	httpResp, err := makeRequest(ctx, func(request resty.Request) (*resty.Response, error) {
		return request.
			SetBody(req).
			SetResult(&resp).
			Post(baseURL + "/tokens/v1")
	})

	if err != nil {
		return nil, err
	}

	if httpResp.StatusCode() >= 400 {
		return nil, errors.New(httpResp.String())
	}

	return &resp, nil
}

func getPrices(ctx context.Context, req PriceRequest) (*PriceResponse, error) {
	var resp PriceResponse
	httpResp, err := makeRequestWithoutRateLimit(ctx, func(request resty.Request) (*resty.Response, error) {
		return request.
			SetBody(req).
			SetResult(&resp).
			Post(baseURL + "/price/v1")
	})

	if err != nil {
		return nil, err
	}

	if httpResp.StatusCode() >= 400 {
		return nil, errors.New(httpResp.String())
	}

	return &resp, nil
}

var _ domain.PriceFetcher = &PriceFetcher{}

type PriceFetcher struct{}

func NewPriceFetcher() *PriceFetcher {
	return &PriceFetcher{}
}

func (f *PriceFetcher) GetPrices(ctx context.Context, tokens []domain.CoingeckoID) (map[domain.CoingeckoID]domain.Price, error) {
	panic("not implemented")
}

func (f *PriceFetcher) PricesByContractSupportedChains() []domain.Chain {
	return []domain.Chain{domain.Solana}
}

func (f *PriceFetcher) GetPricesByContract(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]domain.Price, error) {
	result := make(map[domain.ChainToken]domain.Price)
	priceReq := PriceRequest{Tokens: make([]TokenPriceRequest, 0, len(tokens))}

	// Add tokens to request
	for _, token := range tokens {
		// Only support Solana tokens
		if token.Chain != domain.Solana {
			continue
		}

		// For native SOL
		if token.TokenID == domain.Solana.MainToken().ID() {
			priceReq.Tokens = append(priceReq.Tokens, TokenPriceRequest{
				Token: TokenIdentifier{
					ChainId:      "solana:101",
					Slip44:       "501",
					ResourceType: "nativeToken",
				},
			})
		} else {
			// For SPL tokens
			priceReq.Tokens = append(priceReq.Tokens, TokenPriceRequest{
				Token: TokenIdentifier{
					ChainId:      "solana:101",
					Address:      token.TokenID,
					ResourceType: "address",
				},
			})
		}
	}
	if len(priceReq.Tokens) == 0 {
		return result, nil
	}

	// Get prices
	pricesResp, err := getPrices(ctx, priceReq)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to fetch token prices", map[string]interface{}{
			"error": err.Error(),
		})
		return result, err
	}

	// Map response to result
	for _, token := range tokens {
		if token.Chain.ID() != "sol" {
			continue
		}

		var priceKey string
		if token.TokenID == domain.Solana.MainToken().ID() {
			priceKey = "solana:101/nativeToken:501"
		} else {
			priceKey = fmt.Sprintf("solana:101/address:%s", token.TokenID)
		}
		if priceData, ok := pricesResp.Prices[priceKey]; ok {
			result[token] = domain.Price(priceData.Price)
		} else {
			result[token] = domain.Price(0)
		}
	}
	return result, nil
}
