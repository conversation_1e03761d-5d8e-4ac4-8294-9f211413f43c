package phantom

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMetadataFetcher_FetchMetadata(t *testing.T) {
	ctx := context.Background()
	fetcher := MetadataFetcher()

	t.Run("fetch DWEAM token metadata", func(t *testing.T) {
		metadata, err := fetcher.FetchMetadata(ctx, domain.Solana, "4hGYiv6w7SyNXjASvVFoG9XTiAtjiTWmf4krcp2apump")
		require.NoError(t, err)
		require.NotNil(t, metadata)

		assert.Equal(t, "Dweam", metadata.Name)
		assert.Equal(t, "dweam", metadata.Symbol)
		assert.Equal(t, uint(6), metadata.Decimals)
		assert.Equal(t, "https://ipfs.io/ipfs/QmazwsNH17hwdY3qV3RY7uh6G5PyjfGE8rVLqMvNJ92Qs3", metadata.LogoUrl)
		assert.True(t, metadata.IsVerified) // NOT_VERIFIED is not SPAM
	})

	t.Run("fetch BMP token metadata", func(t *testing.T) {
		metadata, err := fetcher.FetchMetadata(ctx, domain.Ethereum, "******************************************")
		require.NoError(t, err)
		require.NotNil(t, metadata)

		assert.Equal(t, "BitmapPunks", metadata.Name)
		assert.Equal(t, "BMP", metadata.Symbol)
		assert.Equal(t, uint(18), metadata.Decimals)
		assert.True(t, metadata.IsVerified) // NOT_VERIFIED is not SPAM
	})

	t.Run("unsupported chain", func(t *testing.T) {
		_, err := fetcher.FetchMetadata(ctx, domain.Arbitrum, "0x123")
		assert.ErrorIs(t, err, domain.ErrUnsupportedChain)
	})

	t.Run("non-exist token", func(t *testing.T) {
		_, err := fetcher.FetchMetadata(ctx, domain.Ethereum, "******************************************")
		assert.ErrorIs(t, err, domain.ErrRecordNotFound)
	})
}

func TestAssetFetcher_GetAssets(t *testing.T) {
	ctx := context.Background()
	fetcher := AssetFetcher()

	t.Run("fetch assets for address", func(t *testing.T) {
		addr := domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7")
		assets, err := fetcher.GetAssets(ctx, addr, []domain.Chain{domain.Solana}, []domain.AssetType{domain.AssetTypeToken})
		require.NoError(t, err)
		require.NotNil(t, assets)

		assert.True(t, len(assets.Tokens) > 1)
		for _, token := range assets.Tokens {
			t.Logf("got token: %s %s %d %t %f", token.Name(), token.Symbol(), token.Decimals(), token.IsVerified(), util.Val(token.Price))
		}

		// Check SOL token
		solToken := assets.Tokens[0]
		assert.Equal(t, "Solana", solToken.Name())
		assert.Equal(t, "SOL", solToken.Symbol())
		assert.Equal(t, uint(9), solToken.Decimals())
		assert.True(t, solToken.IsVerified())
		assert.NotNil(t, solToken.Price)
	})

	t.Run("unsupported chain", func(t *testing.T) {
		_, err := fetcher.GetAssets(ctx, domain.NewStrAddress("0x123"), []domain.Chain{domain.Ethereum}, []domain.AssetType{domain.AssetTypeToken})
		assert.ErrorIs(t, err, domain.ErrUnsupportedChain)
	})

	t.Run("unsupported asset type", func(t *testing.T) {
		_, err := fetcher.GetAssets(ctx, domain.NewStrAddress("0x123"), []domain.Chain{domain.Solana}, []domain.AssetType{domain.AssetTypeNft})
		assert.ErrorIs(t, err, domain.ErrUnsupportedAssetType)
	})
}

func TestPriceFetcher_GetPricesByContract(t *testing.T) {
	ctx := context.Background()
	fetcher := NewPriceFetcher()

	t.Run("fetch prices for SOL and token", func(t *testing.T) {
		// Create the tokens to test
		solToken := domain.ChainToken{
			Chain:   domain.Solana,
			TokenID: domain.Solana.MainToken().ID(), // Native SOL token
		}
		specificToken := domain.ChainToken{
			Chain:   domain.Solana,
			TokenID: "FUAfBo2jgks6gB4Z4LfZkqSZgzNucisEHqnNebaRxM1P",
		}

		tokens := []domain.ChainToken{solToken, specificToken}

		// Get prices
		prices, err := fetcher.GetPricesByContract(ctx, tokens)
		require.NoError(t, err)
		require.NotNil(t, prices)

		// Assert prices exist for both tokens
		solPrice, solExists := prices[solToken]
		assert.True(t, solExists, "Price for SOL token should exist")
		assert.Greater(t, float64(solPrice), 0.0, "SOL price should be greater than 0")

		specificPrice, specificExists := prices[specificToken]
		assert.True(t, specificExists, "Price for specific token should exist")
		assert.Greater(t, float64(specificPrice), 0.0, "Specific token price should be greater than 0")

		// Log the prices for verification
		t.Logf("SOL price: %f", float64(solPrice))
		t.Logf("Token price: %f", float64(specificPrice))
	})

	t.Run("unsupported chain", func(t *testing.T) {
		ethToken := domain.ChainToken{
			Chain:   domain.Ethereum,
			TokenID: "******************************************",
		}

		prices, err := fetcher.GetPricesByContract(ctx, []domain.ChainToken{ethToken})
		require.NoError(t, err)
		assert.Empty(t, prices, "Should return empty map for unsupported chain")
	})
}
