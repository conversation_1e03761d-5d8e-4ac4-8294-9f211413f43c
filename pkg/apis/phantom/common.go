package phantom

import (
	"context"
	"errors"
	"fmt"
	"io"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	baseURL     = "https://api.phantom.app"
	timeout     = 30 * time.Second
	metadataURL = baseURL + "/tokens/v1/{chain}/address/{address}"
)

var httpClient resty.Client

func init() {
	httpClient = resty.NewRestyClient().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)
}

// Helper function to check if the rate limit is exceeded for phantom api
func isRateLimitExceeded(resp *resty.Response, err error) bool {
	if err != nil {
		if errors.Is(err, io.EOF) {
			return true
		}
		if errors.Is(err, context.DeadlineExceeded) {
			return true
		}
	} else {
		if resp.StatusCode() == 429 || resp.StatusCode() == 418 {
			return true
		}
	}

	return false
}

// Helper function to make an API request within ratelimit
func makeRequest(ctx context.Context, requestFn func(resty.Request) (*resty.Response, error)) (*resty.Response, error) {
	var resp *resty.Response
	var err error

	client := httpClient.Clone()
	request := client.R().SetContext(ctx)
	resp, err = requestFn(request)

	if isRateLimitExceeded(resp, err) {
		return resp, code.ErrRateLimitExceeded
	}

	if err != nil {
		return resp, err
	} else if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "[Phantom] API request failed", map[string]interface{}{
			"status_code": resp.StatusCode(),
			"message":     string(resp.Body()),
		})
		return resp, fmt.Errorf("[Phantom] API request failed with status code %d and message: %s", resp.StatusCode(), string(resp.Body()))
	}

	return resp, nil
}

// Helper function to make an API request without ratelimit
func makeRequestWithoutRateLimit(ctx context.Context, requestFn func(resty.Request) (*resty.Response, error)) (*resty.Response, error) {
	var resp *resty.Response
	var err error

	// It's said that Clone is not for concurrent usage, but let's try
	client := httpClient.Clone()
	request := client.R().SetContext(ctx)

	resp, err = requestFn(request)

	if isRateLimitExceeded(resp, err) {
		return resp, code.ErrRateLimitExceeded
	}

	if err != nil {
		return resp, err
	} else if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "[Phantom] API request failed", map[string]interface{}{
			"status_code": resp.StatusCode(),
			"message":     string(resp.Body()),
		})
		return resp, fmt.Errorf("[Phantom] API request failed with status code %d and message: %s", resp.StatusCode(), string(resp.Body()))
	}

	return resp, nil
}
