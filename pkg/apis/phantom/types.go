package phantom

type TokenMetadataResponse struct {
	Data TokenMetadata `json:"data"`
}

type TokenMetadata struct {
	Chain      Chain  `json:"chain"`
	Address    string `json:"address"`
	Name       string `json:"name"`
	Symbol     string `json:"symbol"`
	Decimals   int    `json:"decimals"`
	LogoURI    string `json:"logoURI"`
	SpamStatus string `json:"spamStatus"`
	IsVerified bool   `json:"-"`
}

type Chain struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Symbol   string `json:"symbol"`
	ImageURL string `json:"imageUrl"`
}

type TokensRequest struct {
	Addresses []AddressRequest `json:"addresses"`
}

type AddressRequest struct {
	ChainId string `json:"chainId"`
	Address string `json:"address"`
}

type TokensResponse struct {
	Tokens    []TokenData `json:"tokens"`
	IsTrimmed bool        `json:"isTrimmed"`
	Errors    []string    `json:"errors"`
}

type TokenData struct {
	Type string     `json:"type"`
	Data TokenState `json:"data"`
}

type TokenState struct {
	Chain              Chain  `json:"chain"`
	WalletAddress      string `json:"walletAddress"`
	MintAddress        string `json:"mintAddress"`
	SplTokenAccountKey string `json:"splTokenAccountPubkey"`
	Decimals           int    `json:"decimals"`
	Amount             string `json:"amount"`
	LogoUri            string `json:"logoUri"`
	Name               string `json:"name"`
	Symbol             string `json:"symbol"`
	CoingeckoId        string `json:"coingeckoId"`
	ProgramId          string `json:"programId"`
	SpamStatus         string `json:"spamStatus"`
}

type PriceRequest struct {
	Tokens []TokenPriceRequest `json:"tokens"`
}

type TokenPriceRequest struct {
	Token TokenIdentifier `json:"token"`
}

type TokenIdentifier struct {
	ChainId      string `json:"chainId"`
	Address      string `json:"address,omitempty"`
	Slip44       string `json:"slip44,omitempty"`
	ResourceType string `json:"resourceType"`
}

type PriceResponse struct {
	Prices map[string]PriceData `json:"prices"`
}

type PriceData struct {
	Price          float64 `json:"price"`
	PriceChange24h float64 `json:"priceChange24h"`
}
