package exchangerateapi

import (
	"context"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

const (
	apiHost    = "https://open.er-api.com/v6"
	getRateURI = "/latest/{quote}"
)

var (
	timeout = time.Duration(15 * time.Second)
)

// NewClient returns a new  ExchangeRateClient
func NewClient() domain.ExchangeRateClientI {
	return &ExchangeRateClient{
		resty.New().
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiHost).
			SetTimeout(timeout),
	}
}

// ExchangeRateClient exchangeRate client
type ExchangeRateClient struct {
	*resty.Client
}

// FetchRateResp fetch rate response
type FetchRateResp struct {
	BaseCode string             `json:"base_code"`
	Rates    map[string]float64 `json:"rates"`
}

// FetchRate fetch exchange rate
func (c *ExchangeRateClient) FetchRate(ctx context.Context, base, quote string) (float64, error) {
	data := FetchRateResp{}
	resp, err := c.R().
		SetContext(ctx).
		SetPathParams(map[string]string{
			"quote": quote,
		}).
		SetResult(&data).
		Get(getRateURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "ExchangeRateClient, FetchRate error", map[string]interface{}{
			"err": err.Error(),
			// "response": resp.String(),
		})
		return 0, err
	}

	if resp.StatusCode() >= 400 {
		kglog.WarningWithDataCtx(ctx, "ExchangeRateClient, FetchRate error", map[string]interface{}{
			"response": resp.String(),
		})
		return 0, fmt.Errorf("ExchangeRateClient, FetchRate http status code>=400: %v", resp.String())
	}

	rate, ok := data.Rates[base]
	if !ok {
		kglog.WarningWithDataCtx(ctx, "ExchangeRateClient, FetchRate error", map[string]interface{}{
			"response": resp.String(),
			"base":     base,
		})
		return 0, fmt.Errorf("ExchangeRateClient, FetchRate: rate not found")
	}

	return rate, nil
}
