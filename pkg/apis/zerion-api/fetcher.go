package zerionapi

import (
	"context"
	"fmt"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

func (z *zerionAPIJustToken) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Ethereum, domain.Polygon, domain.Arbitrum, domain.BNBChain, domain.BaseChain, domain.Optimism}
}

func (z *zerionAPIJustToken) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken}
}

func (z *zerionAPIJustToken) GetAssets(ctx context.Context,
	address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	return getAssets(ctx, z, address, chains, types)
}

func (z *zerionAPIForBNBDefi) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.BNBChain}
}

func (z *zerionAPIForBNBDefi) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeDefi}
}

func (z *zerionAPIForBNBDefi) GetAssets(ctx context.Context,
	address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	return getAssets(ctx, z, address, chains, types)
}

func getAssets(ctx context.Context, z IZerion, address domain.Address,
	chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	// input validation
	for _, chain := range chains {
		if !lo.Contains(z.SupportedChains(), chain) {
			return nil, domain.ErrUnsupportedChain
		}
	}
	for _, assetType := range types {
		if !lo.Contains(z.SupportedTypes(), assetType) {
			return nil, domain.ErrUnsupportedAssetType
		}
	}
	if len(types) != len(z.SupportedTypes()) {
		return nil, fmt.Errorf("only support %s for now", strings.Join(
			lo.Map(z.SupportedTypes(), func(assetType domain.AssetType, _ int) string {
				return string(assetType)
			}), " + "))
	}

	// get wallet positions
	resp, err := z.WalletPositions(ctx, address.String(), chains)
	if err != nil {
		return nil, err
	}
	positions := lo.Filter(resp.Data, func(position *WalletPosition, _ int) bool {
		return position.Attributes.Quantity.Float != 0
	})

	// process tokens and defi assets
	tokenAmounts := make([]*domain.TokenAmount, 0)
	defiAssets := make([]*domain.DefiAsset, 0)
	if lo.Contains(types, domain.AssetTypeToken) {
		tokenAmounts, err = processTokenAmounts(positions)
		if err != nil {
			return nil, err
		}
	}
	if lo.Contains(types, domain.AssetTypeDefi) {
		defiAssets, err = processDefiAssets(positions)
		if err != nil {
			return nil, err
		}
	}
	return &domain.AggregatedAssets{
		Tokens: tokenAmounts,
		Defi:   defiAssets,
	}, nil
}

func (p *WalletPosition) TokenInfo() (address string, decimals int) {
	for _, impl := range p.Attributes.FungibleInfo.Implementations {
		if impl.ChainID == p.Relationships.Chain.Data.ID {
			return impl.Address, impl.Decimals
		}
	}
	return "", 0
}

func (p *WalletPosition) Amount() decimal.Decimal {
	address, decimals := p.TokenInfo()
	if address == "" {
		return decimal.Zero
	}
	if len(p.Attributes.Quantity.Int) > 0 {
		return util.StringBalanceToDecimal(p.Attributes.Quantity.Int, decimals)
	}
	return decimal.Zero
}

func processTokenAmounts(positions []*WalletPosition) ([]*domain.TokenAmount, error) {
	tokenAmounts := make([]*domain.TokenAmount, 0)

	for _, position := range positions {
		if position.Attributes.Protocol != nil {
			// skip defi
			continue
		}
		name := position.Attributes.FungibleInfo.Name
		if strings.HasPrefix(name, "Aave ") {
			// a workaround to filter aave tokens, their balances are already included in aave positions
			continue
		}
		zerionChain := position.Relationships.Chain.Data.ID
		chain := zerionIdToChain[zerionChain]
		address, decimals := position.TokenInfo()
		symbol := position.Attributes.FungibleInfo.Symbol
		price := position.Attributes.Price
		amount := position.Amount()

		var token domain.Token
		if address == zerionMainTokenId[zerionChain] {
			token = chain.MainToken()
		} else {
			logoUrl := util.Val(position.Attributes.FungibleInfo.Icon.URL)
			isVerified := !position.Attributes.Flags.IsTrash
			token = domain.NewToken(chain, address, name, symbol, logoUrl, uint(decimals), isVerified)
		}

		// KW-908 remove token: aBNBc & aBNBb
		if skipWrongAsset(chain, token.ID()) {
			continue
		}
		tokenAmounts = append(tokenAmounts, &domain.TokenAmount{
			Token:  token,
			Price:  util.Ptr(price),
			Amount: amount,
		})
	}

	return tokenAmounts, nil
}

func processDefiAssets(positions []*WalletPosition) ([]*domain.DefiAsset, error) {
	type key struct {
		chain string
		id    string
	}
	defiMap := map[key][]*WalletPosition{}
	for _, position := range positions {
		if position.Attributes.Protocol != nil {
			// defi
			key := key{chain: position.Relationships.Chain.Data.ID, id: position.Attributes.Name}
			defiMap[key] = append(defiMap[key], position)
		}
	}

	defiAssets := make([]*domain.DefiAsset, 0)
	for key, positions := range defiMap {
		zerionChainID, assetId := key.chain, key.id
		chain := zerionIdToChain[zerionChainID]

		supplyTokens := make([]*domain.DefiToken, 0)
		rewardTokens := make([]*domain.DefiToken, 0)
		borrowTokens := make([]*domain.DefiToken, 0)
		siteUrl := ""
		for _, position := range positions {
			positionType := position.Attributes.PositionType
			if positionType == nil {
				continue
			}
			if position.Attributes.ApplicationMetadata.URL != "" {
				siteUrl = position.Attributes.ApplicationMetadata.URL
			}

			address, _ := position.TokenInfo()
			token := &domain.DefiToken{
				ID:      address,
				Name:    position.Attributes.FungibleInfo.Name,
				Symbol:  position.Attributes.FungibleInfo.Symbol,
				LogoUrl: position.Attributes.FungibleInfo.Icon.URL,
				Amount:  position.Amount(),
				Price:   position.Attributes.Price,
			}
			switch *positionType {
			case PositionTypeReward:
				rewardTokens = append(rewardTokens, token)
			case PositionTypeLoan:
				borrowTokens = append(borrowTokens, token)
			default:
				// position type should be staked, deposit, locked. assume it is a supply token
				supplyTokens = append(supplyTokens, token)
			}
		}
		supplyTokenNames := lo.Map(supplyTokens, func(token *domain.DefiToken, _ int) string {
			return token.Name
		})
		assetName := assetId + " " + strings.Join(supplyTokenNames, "/")
		asset := domain.NewDefiAsset(chain, assetId, assetName, siteUrl)
		asset.SupplyTokens = supplyTokens
		asset.RewardTokens = rewardTokens
		asset.BorrowTokens = borrowTokens
		defiAssets = append(defiAssets, asset)
	}
	return defiAssets, nil
}

func skipWrongAsset(chain domain.Chain, tokenID string) bool {
	if chain == domain.BNBChain && strings.EqualFold(tokenID, "0xE85aFCcDaFBE7F2B096f268e31ccE3da8dA2990A") {
		// aBNBc
		return true
	}
	if chain == domain.BNBChain && strings.EqualFold(tokenID, "0xbb1aa6e59e5163d8722a122cd66eba614b59df0d") {
		// aBNBb
		return true
	}
	return false
}
