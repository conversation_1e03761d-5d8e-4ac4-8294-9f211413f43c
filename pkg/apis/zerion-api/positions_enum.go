// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package zerionapi

import (
	"errors"
	"fmt"
)

const (
	// PositionTypeDeposit is a PositionType of type deposit.
	PositionTypeDeposit PositionType = "deposit"
	// PositionTypeLoan is a PositionType of type loan.
	PositionTypeLoan PositionType = "loan"
	// PositionTypeLocked is a PositionType of type locked.
	PositionTypeLocked PositionType = "locked"
	// PositionTypeStaked is a PositionType of type staked.
	PositionTypeStaked PositionType = "staked"
	// PositionTypeReward is a PositionType of type reward.
	PositionTypeReward PositionType = "reward"
	// PositionTypeWallet is a PositionType of type wallet.
	PositionTypeWallet PositionType = "wallet"
	// PositionTypeAirdrop is a PositionType of type airdrop.
	PositionTypeAirdrop PositionType = "airdrop"
	// PositionTypeMargin is a PositionType of type margin.
	PositionTypeMargin PositionType = "margin"
)

var ErrInvalidPositionType = errors.New("not a valid PositionType")

// String implements the Stringer interface.
func (x PositionType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x PositionType) IsValid() bool {
	_, err := ParsePositionType(string(x))
	return err == nil
}

var _PositionTypeValue = map[string]PositionType{
	"deposit": PositionTypeDeposit,
	"loan":    PositionTypeLoan,
	"locked":  PositionTypeLocked,
	"staked":  PositionTypeStaked,
	"reward":  PositionTypeReward,
	"wallet":  PositionTypeWallet,
	"airdrop": PositionTypeAirdrop,
	"margin":  PositionTypeMargin,
}

// ParsePositionType attempts to convert a string to a PositionType.
func ParsePositionType(name string) (PositionType, error) {
	if x, ok := _PositionTypeValue[name]; ok {
		return x, nil
	}
	return PositionType(""), fmt.Errorf("%s is %w", name, ErrInvalidPositionType)
}
