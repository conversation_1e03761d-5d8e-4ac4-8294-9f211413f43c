//go:generate go-enum
package zerionapi

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/samber/lo"
	"go.opentelemetry.io/otel/attribute"
)

// WalletPositionsResponse .
type WalletPositionsResponse struct {
	Links struct {
		Self string `json:"self"`
	} `json:"links"`
	Data []*WalletPosition `json:"data"`
}

// WalletPosition .
type WalletPosition struct {
	Type          string        `json:"type"`
	ID            string        `json:"id"`
	Attributes    Attributes    `json:"attributes"`
	Relationships RelationShips `json:"relationships"`
}

// PositionType .
// ENUM(deposit, loan, locked, staked, reward, wallet, airdrop, margin)
type PositionType string

// Attributes .
type Attributes struct {
	Parent              *string       `json:"parent"`
	Protocol            *string       `json:"protocol"`
	Name                string        `json:"name"`
	PositionType        *PositionType `json:"position_type"`
	Quantity            Quantity      `json:"quantity"`
	Value               *float64      `json:"value"`
	Price               float64       `json:"price"`
	Changes             Changes       `json:"changes"`
	FungibleInfo        FungibleInfo  `json:"fungible_info"`
	ApplicationMetadata struct {
		Name string `json:"name"`
		URL  string `json:"url"`
	} `json:"application_metadata"`
	Flags          Flags  `json:"flags"`
	UpdatedAt      string `json:"updated_at"`
	UpdatedAtBlock int    `json:"updated_at_block"`
}

// Quantity .
type Quantity struct {
	Int      string  `json:"int"`
	Decimals int     `json:"decimals"`
	Float    float64 `json:"float"`
	Numeric  string  `json:"numeric"`
}

// Changes .
type Changes struct {
	Absolute1D float64 `json:"absolute_1d"`
	Percent1D  float64 `json:"percent_1d"`
}

// FungibleInfo .
type FungibleInfo struct {
	Name        string `json:"name"`
	Symbol      string `json:"symbol"`
	Description string `json:"description"`
	Icon        Icon   `json:"icon"`
	Flags       struct {
		Verified bool `json:"verified"`
	} `json:"flags"`
	Implementations []FungibleInfoImpl `json:"implementations"`
}

// Icon
type Icon struct {
	URL *string `json:"url"`
}

// FungibleInfoImpl .
type FungibleInfoImpl struct {
	ChainID  string `json:"chain_id"`
	Address  string `json:"address"`
	Decimals int    `json:"decimals"`
}

// Flags .
type Flags struct {
	Displayable bool `json:"displayable"`
	IsTrash     bool `json:"is_trash"`
}

// RelationShips .
type RelationShips struct {
	Chain    RelationShip `json:"chain"`
	Fungible RelationShip `json:"fungible"`
}

// RelationShip .
type RelationShip struct {
	Links struct {
		Related string `json:"related"`
	} `json:"links"`
	Data RelationShipData `json:"data"`
}

// RelationShipData .
type RelationShipData struct {
	Type string `json:"type"`
	ID   string `json:"id"`
}

// WalletPositions
func (z *zerionAPIJustToken) WalletPositions(ctx context.Context,
	address string, chains []domain.Chain) (*WalletPositionsResponse, error) {
	return walletPositions(ctx, z.client, address, chains)
}

func (z *zerionAPIForBNBDefi) WalletPositions(ctx context.Context,
	address string, chains []domain.Chain) (*WalletPositionsResponse, error) {
	return walletPositions(ctx, z.client, address, chains)
}

func walletPositions(ctx context.Context, client resty.Client, address string,
	chains []domain.Chain) (*WalletPositionsResponse, error) {
	ctx, span := tracing.Start(ctx, "positions.WalletPositions")
	span.SetAttributes(attribute.String("address", address))
	defer span.End()

	zerionChainIds := lo.Map(chains, func(chain domain.Chain, _ int) string {
		return chainToZerionId[chain]
	})
	respData := &WalletPositionsResponse{}
	resp, err := client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetPathParam("address", address).
		SetQueryParam("currency", "usd").
		SetQueryParam("sort", "value").
		SetQueryParam("filter[trash]", "no_filter").
		SetQueryParam("filter[positions]", "no_filter").
		SetQueryParam("filter[chain_ids]", strings.Join(zerionChainIds, ",")).
		SetResult(respData).
		Get(restfulApiHost + listWalletPositionsURI)

	if err != nil {
		kglog.WarningWithDataCtx(ctx, "zerion, WalletPositions error", map[string]interface{}{
			"err":      err.Error(),
			"response": resp.String(),
		})
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		kglog.WarningWithDataCtx(ctx, "zerion, WalletPositions error", map[string]interface{}{
			"response": resp.String(),
		})
		return nil, fmt.Errorf("status code: %d", resp.StatusCode())
	}

	return respData, nil
}
