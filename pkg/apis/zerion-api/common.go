//go:generate mockgen -package=zerionapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api -destination=zerion_api_mock.go . IZerion
package zerionapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/samber/lo"
)

const (
	restfulApiHost         = "https://api.zerion.io"
	listWalletPositionsURI = "/v1/wallets/{address}/positions/"
)

var (
	apiKeyV2        string
	timeout         = time.Duration(30 * time.Second)
	chainToZerionId = map[domain.Chain]string{
		domain.Ethereum:  "ethereum",
		domain.Polygon:   "polygon",
		domain.BNBChain:  "binance-smart-chain",
		domain.Arbitrum:  "arbitrum",
		domain.BaseChain: "base",
		domain.Optimism:  "optimism",
	}
	zerionIdToChain   = lo.Invert(chainToZerionId)
	zerionMainTokenId = map[string]string{
		"ethereum":            "",
		"polygon":             "******************************************",
		"binance-smart-chain": "",
		"arbitrum":            "",
		"base":                "",
		"optimism":            "",
	}
)

// IZerion defines the interface for zerionAPI
type IZerion interface {
	WalletPositions(ctx context.Context, address string, chains []domain.Chain) (*WalletPositionsResponse, error)
	domain.AssetFetcher
}

type zerionAPIJustToken struct {
	client resty.Client
}

type zerionAPIForBNBDefi struct {
	client resty.Client
}

var (
	zerionImplJustToken  IZerion
	zerionImplForBNBDefi IZerion
)

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	apiKeyV2 = config.GetString("ZERION_API_KEY_V2")
	if apiKeyV2 == "" {
		kglog.Error("Cannot get Zerion api key v2")
	}
	zerionImplJustToken = &zerionAPIJustToken{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout).
			SetBasicAuth(apiKeyV2, ""),
	}
	zerionImplForBNBDefi = &zerionAPIForBNBDefi{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout).
			SetBasicAuth(apiKeyV2, ""),
	}
}

// Init inits with resty.Client for mocking
func Init(client resty.Client) {
	zerionImplJustToken = &zerionAPIJustToken{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout).
			SetBasicAuth(apiKeyV2, ""),
	}
	zerionImplForBNBDefi = &zerionAPIForBNBDefi{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetTimeout(timeout).
			SetBasicAuth(apiKeyV2, ""),
	}
}

// SetJustToken zerion instance
func SetJustToken(obj IZerion) {
	zerionImplJustToken = obj
}

// GetFetcherJustToken get zerion API singleton for just token
func GetFetcherJustToken() IZerion {
	return zerionImplJustToken
}

// GetFetcherBNBDefi get zerion API singleton for BNB defi
func GetFetcherBNBDefi() IZerion {
	return zerionImplForBNBDefi
}
