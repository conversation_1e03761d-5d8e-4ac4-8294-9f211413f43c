package tronscanapi

import (
	"context"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// TokenBalance user token balance
type TokenBalance struct {
	Amount           *interface{} `json:"amount"`
	AmountInFloat64  float64      `json:"amountInFloat64"`
	TokenID          string       `json:"tokenId"`
	TokenName        string       `json:"tokenName"`
	TokenDecimal     int          `json:"tokenDecimal"`
	TokenAbbr        string       `json:"tokenAbbr"`
	TokenCanShow     int          `json:"tokenCanShow"`
	TokenType        string       `json:"tokenType"`
	TokenLogo        string       `json:"tokenLogo"`
	TokenPriceInTrx  float64      `json:"tokenPriceInTrx"`
	TokenPriceInUsd  float64      `json:"tokenPriceInUsd"`
	Quantity         interface{}  `json:"quantity"` // may be string or number
	AmountInUsd      float64      `json:"amountInUsd"`
	NrOfTokenHolders int64        `json:"nrOfTokenHolders"`
	TransferCount    int64        `json:"transferCount"`
	Vip              bool         `json:"vip"`
	Balance          string       `json:"balance"`
}

// TokensResp Token list response
type TokensResp struct {
	Total int            `json:"total"`
	Data  []TokenBalance `json:"data"`
}

// Tokens Get user token balance
func (t *tronscanAPI) Tokens(ctx context.Context, address string) (*TokensResp, error) {
	ctx, span := tracing.Start(ctx, "account.Tokens")
	defer span.End()

	var respData *TokensResp

	// Define the request function
	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := &TokensResp{}
		resp, err := request.
			SetQueryParam("address", address).
			SetResult(localRespData).
			Get(tokensURI)
		respData = localRespData
		return resp, err
	}

	_, err := t.makeRequest(ctx, requestFn)

	if err != nil {
		return nil, err
	}

	// Convert amount to float64
	for idx, v := range respData.Data {
		if v.Amount == nil {
			continue
		}
		// Determine amount data type
		switch (*v.Amount).(type) {
		case string:
			if amt, err := strconv.ParseFloat((*v.Amount).(string), 64); err == nil {
				respData.Data[idx].AmountInFloat64 = amt
			}
		case float64:
			respData.Data[idx].AmountInFloat64 = (*v.Amount).(float64)
		}
	}

	return respData, nil
}
