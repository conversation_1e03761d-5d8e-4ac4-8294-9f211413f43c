package tronscanapi

import (
	"context"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
)

func (t *tronscanAPI) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Tron}
}

func (t *tronscanAPI) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken}
}

func (t *tronscanAPI) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	// Validate input
	if len(chains) != 1 || chains[0] != domain.Tron {
		return nil, domain.ErrUnsupportedChain
	}
	if len(types) != 1 || types[0] != domain.AssetTypeToken {
		return nil, domain.ErrUnsupportedAssetType
	}

	// Fetch token balances
	resp, err := t.Tokens(ctx, address.String())
	if err != nil {
		return nil, err
	}

	tokenAmounts := make([]*domain.TokenAmount, 0, len(resp.Data))
	for _, token := range resp.Data {
		amount, err := decimal.NewFromString(fmt.Sprintf("%v", token.Quantity))
		if err != nil {
			return nil, fmt.Errorf("failed to parse token amount: %w", err)
		}

		var tokenAmount *domain.TokenAmount
		if token.TokenID == domain.Tron.MainToken().ID() {
			tokenAmount = &domain.TokenAmount{
				Token:  domain.Tron.MainToken(),
				Amount: amount,
				Price:  &token.TokenPriceInUsd,
			}
		} else {
			tokenType := domain.TrcType(token.TokenType)
			tokenAmount = &domain.TokenAmount{
				Token: domain.NewTronToken(
					domain.Tron,
					token.TokenID,
					token.TokenName,
					token.TokenAbbr,
					token.TokenLogo,
					uint(token.TokenDecimal),
					true,
					tokenType,
				),
				Amount: amount,
				Price:  &token.TokenPriceInUsd,
			}
		}
		tokenAmounts = append(tokenAmounts, tokenAmount)
	}

	return &domain.AggregatedAssets{
		Tokens: tokenAmounts,
	}, nil
}
