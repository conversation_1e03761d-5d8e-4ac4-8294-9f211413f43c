{"total": 53, "contractInfo": {}, "rangeTotal": 53, "token_transfers": [{"transaction_id": "f02a7a0664b65948942d4ec118b89fa31d68863c6453cba2addb680ff15a91d0", "status": 0, "block_ts": 1719470493000, "from_address": "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", "from_address_tag": {"from_address_tag": "", "from_address_tag_logo": ""}, "to_address": "TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU", "to_address_tag": {"to_address_tag_logo": "", "to_address_tag": ""}, "block": 62941945, "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "trigger_info": {"method": "transfer(address _to,uint256 _value)", "data": "a9059cbb00000000000000000000004131d1c7751eaa6374d4138597e8c7b5a1605cc43c00000000000000000000000000000000000000000000000000000000000f4240", "parameter": {"_value": "1000000", "_to": "TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU"}, "methodName": "transfer", "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "call_value": 0}, "quant": "1000000", "approval_amount": "0", "event_type": "Transfer", "confirmed": true, "contractRet": "SUCCESS", "finalResult": "SUCCESS", "tokenInfo": {"tokenId": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "tokenAbbr": "USDT", "tokenName": "Tether USD", "tokenDecimal": 6, "tokenCanShow": 1, "tokenType": "trc20", "tokenLogo": "https://static.tronscan.org/production/logo/usdtlogo.png", "tokenLevel": "2", "issuerAddr": "THPvaUhoh2Qn2y9THCZML3H815hhFhn5YC", "vip": true}, "revert": false, "contract_type": "trc20", "fromAddressIsContract": false, "toAddressIsContract": false, "riskTransaction": false}, {"transaction_id": "8687588642ce023f6c53e60a4ad9cc140267bb404ab452f466e4ba9ee421dfbd", "status": 0, "block_ts": 1718297712000, "from_address": "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", "from_address_tag": {"from_address_tag": "", "from_address_tag_logo": ""}, "to_address": "TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU", "to_address_tag": {"to_address_tag_logo": "", "to_address_tag": ""}, "block": 62551232, "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "trigger_info": {"method": "transfer(address _to,uint256 _value)", "data": "a9059cbb00000000000000000000004131d1c7751eaa6374d4138597e8c7b5a1605cc43c00000000000000000000000000000000000000000000000000000000000f4240", "parameter": {"_value": "1000000", "_to": "TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU"}, "methodName": "transfer", "contract_address": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "call_value": 0}, "quant": "1000000", "approval_amount": "0", "event_type": "Transfer", "confirmed": true, "contractRet": "SUCCESS", "finalResult": "SUCCESS", "tokenInfo": {"tokenId": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "tokenAbbr": "USDT", "tokenName": "Tether USD", "tokenDecimal": 6, "tokenCanShow": 1, "tokenType": "trc20", "tokenLogo": "https://static.tronscan.org/production/logo/usdtlogo.png", "tokenLevel": "2", "issuerAddr": "THPvaUhoh2Qn2y9THCZML3H815hhFhn5YC", "vip": true}, "revert": false, "contract_type": "trc20", "fromAddressIsContract": false, "toAddressIsContract": false, "riskTransaction": false}], "normalAddressInfo": {"TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU": {"risk": false}, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn": {"risk": false}}}