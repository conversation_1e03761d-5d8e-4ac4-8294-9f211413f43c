package tronscanapi

import (
	"context"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

type ContractInfo struct {
	IsToken bool   `json:"isToken"`
	Tag1    string `json:"tag1"`
	Tag1Url string `json:"tag1Url"`
	Name    string `json:"name"`
	Risk    bool   `json:"risk"`
	Vip     bool   `json:"vip"`
}

type Trc20TransferInfo struct {
	IconUrl         string `json:"icon_url"`
	Symbol          string `json:"symbol"`
	Level           string `json:"level"`
	ToAddress       string `json:"to_address"`
	ContractAddress string `json:"contract_address"`
	Type            string `json:"type"`
	Decimals        int    `json:"decimals"`
	Name            string `json:"name"`
	Vip             bool   `json:"vip"`
	TokenType       string `json:"tokenType"`
	FromAddress     string `json:"from_address"`
	AmountStr       string `json:"amount_str"`
	Status          int    `json:"status"`
}

type TransfersAllList struct {
	IconUrl         string `json:"icon_url"`
	Symbol          string `json:"symbol"`
	Level           string `json:"level"`
	ToAddress       string `json:"to_address"`
	Type            string `json:"type"`
	ContractAddress string `json:"contract_address"`
	TokenId         string `json:"token_id"`
	Decimals        int    `json:"decimals"`
	Name            string `json:"name"`
	Vip             bool   `json:"vip"`
	TokenType       string `json:"tokenType"`
	AmountStr       string `json:"amount_str"`
	FromAddress     string `json:"from_address"`
	Status          int    `json:"status"`
}

type Cost struct {
	NetFeeCost         int   `json:"net_fee_cost"`
	DateCreated        int   `json:"date_created"`
	Fee                int64 `json:"fee"`
	EnergyFeeCost      int   `json:"energy_fee_cost"`
	NetUsage           int   `json:"net_usage"`
	MultiSignFee       int   `json:"multi_sign_fee"`
	NetFee             int64 `json:"net_fee"`
	EnergyPenaltyTotal int   `json:"energy_penalty_total"`
	EnergyUsage        int   `json:"energy_usage"`
	EnergyFee          int64 `json:"energy_fee"`
	EnergyUsageTotal   int   `json:"energy_usage_total"`
	MemoFee            int64 `json:"memoFee"`
	OriginEnergyUsage  int   `json:"origin_energy_usage"`
}

type InternalTransaction struct {
	TransactionID string `json:"transaction_id"`
	CallerAddress string `json:"caller_address"`
	Note          string `json:"note"`
	Rejected      bool   `json:"rejected"`
	ValueInfoList string `json:"value_info_list"`
	DateCreated   int64  `json:"date_created"`
	Contract      string `json:"contract"`
	Block         int    `json:"block"`
	TokenList     []struct {
		TokenID   string `json:"token_id"`
		CallValue int64  `json:"call_value"`
		TokenInfo struct {
			TokenLevel   string `json:"tokenLevel"`
			TokenId      string `json:"tokenId"`
			TokenName    string `json:"tokenName"`
			TokenDecimal int    `json:"tokenDecimal"`
			TokenAbbr    string `json:"tokenAbbr"`
			TokenCanShow int    `json:"tokenCanShow"`
			TokenType    string `json:"tokenType"`
			Vip          bool   `json:"vip"`
			TokenLogo    string `json:"tokenLogo"`
		} `json:"tokenInfo"`
	} `json:"token_list"`
	Confirmed         bool   `json:"confirmed"`
	Hash              string `json:"hash"`
	TransferToAddress string `json:"transfer_to_address"`
}

type TransactionInfoResp struct {
	ContractMap          map[string]bool                  `json:"contract_map"`
	ContractRet          string                           `json:"contractRet"`
	Data                 string                           `json:"data"`
	ContractInfo         map[string]ContractInfo          `json:"contractInfo"`
	ContractType         int                              `json:"contractType"`
	EventCount           int                              `json:"event_count"`
	Project              string                           `json:"project"`
	ToAddress            string                           `json:"toAddress"`
	Confirmed            bool                             `json:"confirmed"`
	Trc20TransferInfo    []Trc20TransferInfo              `json:"trc20TransferInfo"`
	TransfersAllList     []TransfersAllList               `json:"transfersAllList"`
	Block                int                              `json:"block"`
	RiskTransaction      bool                             `json:"riskTransaction"`
	Timestamp            int64                            `json:"timestamp"`
	Info                 map[string]interface{}           `json:"info"`
	NormalAddressInfo    map[string]map[string]bool       `json:"normalAddressInfo"`
	Cost                 Cost                             `json:"cost"`
	NoteLevel            int                              `json:"noteLevel"`
	AddressTag           map[string]interface{}           `json:"addressTag"`
	Revert               bool                             `json:"revert"`
	Confirmations        int                              `json:"confirmations"`
	FeeLimit             int                              `json:"fee_limit"`
	TokenTransferInfo    Trc20TransferInfo                `json:"tokenTransferInfo"`
	ContractTypeStr      string                           `json:"contract_type"`
	TriggerInfo          map[string]interface{}           `json:"trigger_info"`
	SignatureAddresses   []string                         `json:"signature_addresses"`
	OwnerAddress         string                           `json:"ownerAddress"`
	SrConfirmList        []map[string]interface{}         `json:"srConfirmList"`
	Hash                 string                           `json:"hash"`
	ContractData         map[string]interface{}           `json:"contractData"`
	InternalTransactions map[string][]InternalTransaction `json:"internal_transactions"`
}

type TransactionInfo struct {
	InternalTransactions []domain.InternalTx
	NetFee               int64
}

func (t *tronscanAPI) TransactionDetail(ctx context.Context, txHash string) (*TransactionInfo, error) {
	// Start a new span using your existing tracing package
	ctx, span := tracing.Start(ctx, "transaction.TransactionDetail")
	defer span.End()

	respData := &TransactionInfoResp{}

	// Define the request function
	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := &TransactionInfoResp{}
		resp, err := request.
			SetQueryParam("hash", txHash).
			SetResult(localRespData).
			Get(transactionInfoURI)
		respData = localRespData
		return resp, err
	}

	// Use makeRequest to perform the request with retry logic
	_, err := t.makeRequest(ctx, requestFn)

	if err != nil {
		return nil, err
	}

	internalTxs := []domain.InternalTx{}
	for _, txs := range respData.InternalTransactions {
		for _, tx := range txs {
			for _, token := range tx.TokenList {
				if token.CallValue > 0 {
					internalTxs = append(internalTxs, domain.InternalTx{
						From:            tx.TransferToAddress,
						To:              tx.CallerAddress,
						Value:           strconv.FormatInt(token.CallValue, 10),
						ContractAddress: token.TokenID,
					})
				}
			}
		}
	}
	result := &TransactionInfo{
		InternalTransactions: internalTxs,
		NetFee:               respData.Cost.NetFee,
	}

	return result, nil
}
