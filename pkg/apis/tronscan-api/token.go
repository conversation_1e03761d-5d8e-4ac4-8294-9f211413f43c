package tronscanapi

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// Trc20TransferResp Token list response
type Trc20TransferResp struct {
	Total          int       `json:"total"`
	RangeTotal     int       `json:"rangeTotal"`
	TokenTransfers []TokenTx `json:"token_transfers"`
	ContractInfo   struct {
	} `json:"contractInfo"`
	Message string `json:"message"`
	Error   string `json:"error"`
}

// TokenTx .
type TokenTx struct {
	TransactionID  string `json:"transaction_id"`
	BlockTs        int64  `json:"block_ts"`
	FromAddress    string `json:"from_address"`
	FromAddressTag struct {
		FromAddressTag     string `json:"from_address_tag"`
		FromAddressTagLogo string `json:"from_address_tag_logo"`
	} `json:"from_address_tag"`
	ToAddress    string `json:"to_address"`
	ToAddressTag struct {
		ToAddressTagLogo string `json:"to_address_tag_logo"`
		ToAddressTag     string `json:"to_address_tag"`
	} `json:"to_address_tag"`
	Block           uint32 `json:"block"`
	ContractAddress string `json:"contract_address"`
	TriggerInfo     struct {
		Method    string `json:"method"`
		Data      string `json:"data"`
		Parameter struct {
			Value string `json:"_value"`
			To    string `json:"_to"`
		} `json:"parameter"`
		MethodName      string `json:"methodName"`
		ContractAddress string `json:"contract_address"`
		CallValue       int    `json:"call_value"`
	} `json:"trigger_info"`
	Quant                 string    `json:"quant"`
	ApprovalAmount        string    `json:"approval_amount"`
	EventType             string    `json:"event_type"`
	ContractType          string    `json:"contract_type"`
	Confirmed             bool      `json:"confirmed"`
	ContractRet           string    `json:"contractRet"`
	FinalResult           string    `json:"finalResult"`
	TokenInfo             TokenInfo `json:"tokenInfo"`
	FromAddressIsContract bool      `json:"fromAddressIsContract"`
	ToAddressIsContract   bool      `json:"toAddressIsContract"`
	Revert                bool      `json:"revert"`
}

type TokenInfo struct {
	TokenID      string `json:"tokenId"`
	TokenAbbr    string `json:"tokenAbbr"`
	TokenName    string `json:"tokenName"`
	TokenDecimal int    `json:"tokenDecimal"`
	TokenCanShow int    `json:"tokenCanShow"`
	TokenType    string `json:"tokenType"`
	TokenLogo    string `json:"tokenLogo"`
	TokenLevel   string `json:"tokenLevel"`
	Vip          bool   `json:"vip"`
}

// Trc20Transfer List the transactions
func (t *tronscanAPI) Trc20Transfer(ctx context.Context, param *RequestParams) (*Trc20TransferResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "transaction.Trc20Transfer")
	defer span.End()

	respData := &Trc20TransferResp{}

	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := &Trc20TransferResp{}
		resp, err := request.
			SetQueryParam("relatedAddress", param.Address).
			SetQueryParam("limit", strconv.Itoa(DefaultPageLimit)).
			SetQueryParam("start", strconv.Itoa(int(param.Start))).
			SetQueryParam("sort", "-timestamp").
			SetQueryParam("count", "true").
			SetResult(&localRespData).
			Get(trc20TransferURI)
		respData = localRespData
		return resp, err
	}

	resp, err := t.makeRequest(ctx, requestFn)

	if err != nil {
		return nil, resp.Time(), err
	}

	if respData.Error != "" || respData.Message != "" {
		message := fmt.Sprintf("[tronscanapi.Trc20Transfer] error: %s, message: %s", respData.Error, respData.Message)
		return nil, resp.Time(), errors.New(message)
	}

	return respData, resp.Time(), nil
}
