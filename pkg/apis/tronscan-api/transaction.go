package tronscanapi

import (
	"context"
	"strconv"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

// constants
const (
	DefaultPageLimit = 50
)

// RequestParams params
type RequestParams struct {
	Address string
	Start   uint32
}

// TransactionResp Token list response
type TransactionResp struct {
	Total             int                 `json:"total"`
	RangeTotal        int                 `json:"rangeTotal"`
	Data              []Tx                `json:"data"`
	WholeChainTxCount int64               `json:"wholeChainTxCount"`
	ContractMap       map[string]bool     `json:"contractMap"`
	ContractInfo      map[string]Contract `json:"contractInfo"`
}

// Tx .
type Tx struct {
	Block         uint32   `json:"block"`
	Hash          string   `json:"hash"`
	Timestamp     int64    `json:"timestamp"`
	OwnerAddress  string   `json:"ownerAddress"`
	ToAddressList []string `json:"toAddressList"`
	ToAddress     string   `json:"toAddress"`
	ContractType  int      `json:"contractType"`
	Confirmed     bool     `json:"confirmed"`
	Revert        bool     `json:"revert"`
	ContractData  struct {
		Amount          int    `json:"amount"`
		AssetName       string `json:"asset_name"`
		OwnerAddress    string `json:"owner_address"`
		ToAddress       string `json:"to_address"`
		Data            string `json:"data"`
		ContractAddress string `json:"contract_address"`
		TokenInfo       struct {
			TokenID      string `json:"tokenId"`
			TokenAbbr    string `json:"tokenAbbr"`
			TokenName    string `json:"tokenName"`
			TokenDecimal int    `json:"tokenDecimal"`
			TokenCanShow int    `json:"tokenCanShow"`
			TokenType    string `json:"tokenType"`
			TokenLogo    string `json:"tokenLogo"`
			TokenLevel   string `json:"tokenLevel"`
			Vip          bool   `json:"vip"`
		} `json:"tokenInfo"`
		NewContract struct {
			OriginAddress string `json:"origin_address"`
		} `json:"new_contract,omitempty"`
	} `json:"contractData,omitempty"`
	SmartCalls  string `json:"SmartCalls"`
	Events      string `json:"Events"`
	ID          string `json:"id"`
	Data        string `json:"data"`
	Fee         string `json:"fee"`
	ContractRet string `json:"contractRet"`
	Result      string `json:"result"`
	Amount      string `json:"amount"`
	Cost        struct {
		NetFee            int `json:"net_fee"`
		EnergyUsage       int `json:"energy_usage"`
		Fee               int `json:"fee"`
		EnergyFee         int `json:"energy_fee"`
		EnergyUsageTotal  int `json:"energy_usage_total"`
		OriginEnergyUsage int `json:"origin_energy_usage"`
		NetUsage          int `json:"net_usage"`
	} `json:"cost"`
	TokenInfo struct {
		TokenID      string `json:"tokenId"`
		TokenAbbr    string `json:"tokenAbbr"`
		TokenName    string `json:"tokenName"`
		TokenDecimal int    `json:"tokenDecimal"`
		TokenCanShow int    `json:"tokenCanShow"`
		TokenType    string `json:"tokenType"`
		TokenLogo    string `json:"tokenLogo"`
		TokenLevel   string `json:"tokenLevel"`
		Vip          bool   `json:"vip"`
	} `json:"tokenInfo"`
	TokenType   string `json:"tokenType"`
	TriggerInfo struct {
		Method    string `json:"method"`
		Data      string `json:"data"`
		Parameter struct {
			Value string `json:"_value"`
			To    string `json:"_to"`
		} `json:"parameter"`
		MethodName      string `json:"methodName"`
		ContractAddress string `json:"contract_address"`
		CallValue       int    `json:"call_value"`
	} `json:"trigger_info,omitempty"`
	OwnerAddressTag string `json:"ownerAddressTag,omitempty"`
}

// Contract .
type Contract struct {
	Tag1    string `json:"tag1"`
	Tag1URL string `json:"tag1Url"`
	Name    string `json:"name"`
	Vip     bool   `json:"vip"`
}

// Transaction List the transactions
func (t *tronscanAPI) Transaction(ctx context.Context, param *RequestParams) (*TransactionResp, time.Duration, error) {
	ctx, span := tracing.Start(ctx, "transaction.Transaction")
	defer span.End()

	respData := &TransactionResp{}

	// Define the request function
	requestFn := func(request resty.Request) (*resty.Response, error) {
		localRespData := &TransactionResp{}
		resp, err := request.
			SetQueryParam("address", param.Address).
			SetQueryParam("limit", strconv.Itoa(DefaultPageLimit)).
			SetQueryParam("start", strconv.Itoa(int(param.Start))).
			SetQueryParam("sort", "-timestamp").
			SetQueryParam("count", "true").
			SetResult(localRespData).
			Get(transactionURI)
		respData = localRespData
		return resp, err
	}

	resp, err := t.makeRequest(ctx, requestFn)

	if err != nil {
		return nil, resp.Time(), err
	}

	return respData, resp.Time(), nil
}
