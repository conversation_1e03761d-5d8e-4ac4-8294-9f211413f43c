//go:generate mockgen -source=common.go -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api -destination=common_mock.go -package=tronscanapi ITronscan
package tronscanapi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	rls "github.com/kryptogo/kg-wallet-backend/pkg/service/ratelimit"
)

const (
	apiHost            = "https://apilist.tronscan.org"
	tokensURI          = "/api/account/tokens"
	transactionURI     = "/api/transaction"
	transactionInfoURI = "/api/transaction-info"
	trc20TransferURI   = "/api/token_trc20/transfers"
)

var (
	timeout = time.Duration(15 * time.Second)
)

// ITronscan is the interface for tronscanAPI
type ITronscan interface {
	Tokens(ctx context.Context, address string) (*TokensResp, error)
	Trc20Transfer(ctx context.Context, param *RequestParams) (*Trc20TransferResp, time.Duration, error)
	Transaction(ctx context.Context, param *RequestParams) (*TransactionResp, time.Duration, error)
	TransactionDetail(ctx context.Context, txHash string) (*TransactionInfo, error)
	domain.AssetFetcher
}

type tronscanAPI struct {
	client resty.Client
}

var tronscanObj ITronscan

func newTronscanClient(client resty.Client) resty.Client {
	return client.
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetBaseURL(apiHost).
		SetTimeout(timeout)
}

// InitDefault inits default API
func InitDefault() {
	client := resty.NewRestyClient()
	tronscanObj = &tronscanAPI{
		client: newTronscanClient(client),
	}
}

// Set inits with ITronscan mock
func Set(mock ITronscan) {
	tronscanObj = mock
}

// InitResty inits with resty.Client for mocking
func InitResty(client resty.Client) {
	tronscanObj = &tronscanAPI{
		client: newTronscanClient(client),
	}
}

// Get get KCC API singleton
func Get() ITronscan {
	return tronscanObj
}

// Helper function to check if the rate limit is exceeded for tronscan api
//
// There's a very weird design of tronscan API, it returns 403 or 503 when the rate limit is exceeded
// and the time we need to wait is written in the message string of the response, e.g.,
//
// "The key exceeds the frequency limit(15), and the query server is suspended for 30s"
//
// Reference: https://developers.tron.network/reference/rate-limits
func isRateLimitExceeded(resp *resty.Response, err error) bool {
	if err != nil {
		if errors.Is(err, io.EOF) {
			return true
		}
		if errors.Is(err, context.DeadlineExceeded) {
			return true
		}
	} else {
		if resp.StatusCode() == 429 {
			return true
		}
		if resp.StatusCode() == 503 || resp.StatusCode() == 403 {
			// body example
			// {"Error":"request rate exceeded the allowed_rps(3), and the query server is suspended for 120s"}
			// {"Error": "The key exceeds the frequency limit(15), and the query server is suspended for 30s"}
			if resp.Body() != nil {
				var data map[string]interface{}
				err := json.Unmarshal(resp.Body(), &data)
				if err == nil {
					if message, ok := data["Error"].(string); ok {
						if strings.Contains(message, "suspended for") && strings.Contains(message, "exceed") {
							return true
						}
					}
				}
			}
		}
	}

	return false
}

// Helper function to make an API request within ratelimit
func (t *tronscanAPI) makeRequest(ctx context.Context, requestFn func(resty.Request) (*resty.Response, error)) (*resty.Response, error) {
	var resp *resty.Response
	var err error

	proxyUrl, callType := rls.WaitAndGetNextCallParams(ctx, cache.ServiceNameTronscan)
	kglog.InfoWithDataCtx(ctx, "[proxy_rate] getNextCallTypeAndWaitTime", map[string]interface{}{
		"proxy_url": proxyUrl,
		"call_type": callType,
	})

	// It's said that Clone is not for concurrent usage, but let's try
	client := t.client.Clone()
	if proxyUrl != "" {
		proxyURL, err := url.Parse(proxyUrl)
		if err != nil {
			return nil, err
		}

		client.SetTransport(&http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		})
	}

	request := client.R().SetContext(ctx)

	if callType == cache.APICallTypeAPIKey {
		request.SetHeader("TRON-PRO-API-KEY", config.GetString("TRONSCAN_PRO_API_KEY"))
	}

	resp, err = requestFn(request)

	if isRateLimitExceeded(resp, err) {
		return resp, code.ErrRateLimitExceeded
	}

	if err != nil {
		return resp, err
	} else if resp.StatusCode() >= 400 {
		kglog.ErrorWithDataCtx(ctx, "[Tronscan] API request failed", map[string]interface{}{
			"status_code": resp.StatusCode(),
			"message":     string(resp.Body()),
		})
		return resp, fmt.Errorf("[Tronscan] API request failed with status code %d and message: %s", resp.StatusCode(), string(resp.Body()))
	}

	return resp, nil
}
