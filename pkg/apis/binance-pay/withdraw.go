package binancepay

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

type NetworkResponse struct {
	Code   string       `json:"code"`
	Status string       `json:"status"`
	Data   NetworksData `json:"data"`
}

type NetworksData struct {
	Networks   []string     `json:"networks"`
	CoinDetail []CoinDetail `json:"coinDetail"`
}

type CoinDetail struct {
	Coin              string              `json:"coin"`
	NetWorkDetailList []NetworkDetailList `json:"netWorkDetailList"`
}

type NetworkDetailList struct {
	Network         string `json:"network"`
	NetworkName     string `json:"networkName"`
	WithdrawEnable  bool   `json:"withdrawEnable"`
	ContractAddress string `json:"contractAddress"`
}

type PreCreateRequest struct {
	Source   string `json:"source"`
	Currency string `json:"currency"`
	Network  string `json:"network"`
	Address  string `json:"address"`
}

type PreCreateResponse struct {
	Code   string `json:"code"`
	Status string `json:"status"`
	Data   struct {
		TransactionID    string `json:"transactionId"`
		UniversalLinkURL string `json:"universalLinkUrl"`
	} `json:"data"`
}

func (c *clientImpl) GetWithdrawNetworks(ctx context.Context) (*NetworkResponse, error) {
	timestamp := time.Now().UnixMilli()
	nonce := generateNonce()
	body := fmt.Sprintf(`{"source":"%s"}`, config.GetString("BINANCE_PAY_SOURCE"))

	signature, err := signBody(timestamp, nonce, body, c.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate signature: %w", err)
	}

	var result NetworkResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("BinancePay-Timestamp", fmt.Sprintf("%d", timestamp)).
		SetHeader("BinancePay-Nonce", nonce).
		SetHeader("BinancePay-Signature", signature).
		SetBody(body).
		SetResult(&result).
		Post("https://bpay.binanceapi.com/binancepay/openapi/withdraw/networks")

	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed with status code %d: %s", resp.StatusCode(), resp.String())
	}
	if result.Status != "SUCCESS" || result.Code != "000000" {
		return nil, fmt.Errorf("api returned error: code=%s status=%s", result.Code, result.Status)
	}

	return &result, nil
}

func (c *clientImpl) FetchMetadatas(ctx context.Context) (map[domain.ChainToken]*domain.TokenMetadata, error) {
	resp, err := c.GetWithdrawNetworks(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get withdraw networks: %w", err)
	}

	networkToChain := make(map[string]domain.Chain)
	for _, chain := range domain.Chains {
		if !chain.IsTestnet() && chain.BinanceNetwork() != "" {
			networkToChain[chain.BinanceNetwork()] = chain
		}
	}

	metadatas := make(map[domain.ChainToken]*domain.TokenMetadata)
	for _, coin := range resp.Data.CoinDetail {
		for _, network := range coin.NetWorkDetailList {
			chain, ok := networkToChain[network.Network]
			if !ok {
				continue
			}
			key := domain.ChainToken{Chain: chain, TokenID: network.ContractAddress}
			metadatas[key] = &domain.TokenMetadata{
				Name:          coin.Coin,
				Symbol:        coin.Coin,
				IsVerified:    true,
				BinanceTicker: coin.Coin,
			}
		}
	}

	return metadatas, nil
}

type WithdrawURLs struct {
	TransactionID string
	IOSLink       string
	AndroidLink   string
}

func (c *clientImpl) PreCreateWithdraw(ctx context.Context, currency, network, address string) (*WithdrawURLs, error) {
	timestamp := time.Now().UnixMilli()
	nonce := generateNonce()

	req := PreCreateRequest{
		Source:   config.GetString("BINANCE_PAY_SOURCE"),
		Currency: currency,
		Network:  network,
		Address:  address,
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	signature, err := signBody(timestamp, nonce, string(body), c.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate signature: %w", err)
	}

	var result PreCreateResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("BinancePay-Timestamp", fmt.Sprintf("%d", timestamp)).
		SetHeader("BinancePay-Nonce", nonce).
		SetHeader("BinancePay-Signature", signature).
		SetBody(string(body)).
		SetResult(&result).
		Post("https://bpay.binanceapi.com/binancepay/openapi/withdraw/pre-create")

	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed with status code %d: %s", resp.StatusCode(), resp.String())
	}
	if result.Status != "SUCCESS" || result.Code != "000000" {
		return nil, fmt.Errorf("api returned error: code=%s status=%s", result.Code, result.Status)
	}
	txID := result.Data.TransactionID

	// Generate deep links
	ts := fmt.Sprintf("%d", timestamp)
	redirectURL := config.GetString("BINANCE_PAY_REDIRECT_URL")
	deepLinkSig, err := signDeepLink(nonce, timestamp, txID, c.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate deep link signature: %w", err)
	}

	iosDeepLink := fmt.Sprintf("bnc://app.binance.com/payment/onchainpay?transactionId=%s&nonce=%s&timeStamp=%s&sign=%s&redirectUrl=%s", txID, nonce, ts, deepLinkSig, redirectURL)
	androidDeepLink := fmt.Sprintf("bnc://app.binance.com/payment/secpay?extra_key_api_type=on-chain-transfer&transactionId=%s&nonce=%s&sign=%s&timeStamp=%s&redirectUrl=%s", txID, nonce, deepLinkSig, ts, redirectURL)

	link := result.Data.UniversalLinkURL
	iosEncoded := base64.RawURLEncoding.EncodeToString([]byte(iosDeepLink))
	androidEncoded := base64.RawURLEncoding.EncodeToString([]byte(androidDeepLink))

	return &WithdrawURLs{
		TransactionID: txID,
		IOSLink:       fmt.Sprintf("%s?_dp=%s", link, iosEncoded),
		AndroidLink:   fmt.Sprintf("%s?_dp=%s", link, androidEncoded),
	}, nil
}
