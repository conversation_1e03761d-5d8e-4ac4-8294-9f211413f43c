package binancepay

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

// DepositInitRequest represents the request body for the deposit/init endpoint
type DepositInitRequest struct {
	RequestID string `json:"requestId"`
	Network   string `json:"network"`
	Currency  string `json:"currency"`
	Source    string `json:"source"`
}

// DepositInitResponse represents the response from the deposit/init endpoint
type DepositInitResponse struct {
	Code   string `json:"code"`
	Status string `json:"status"`
	Data   struct {
		TransactionID    string `json:"transactionId"`
		UniversalLinkURL string `json:"universalLinkUrl"`
		DpURL            string `json:"dpUrl"`
		DpAppID          string `json:"dpAppId"`
		DpStartPagePath  string `json:"dpStartPagePath"`
	} `json:"data"`
}

// DepositURLs contains the transaction ID and generated deep links for deposit
type DepositURLs struct {
	RequestID     string
	TransactionID string
	UniversalURL  string
}

// DepositQueryRequest represents the request body for the deposit/query endpoint
type DepositQueryRequest struct {
	RequestID     string `json:"requestId"`
	TransactionID string `json:"transactionId"`
	Source        string `json:"source"`
}

// DepositQueryResponse represents the response from the deposit/query endpoint
type DepositQueryResponse struct {
	Code   string `json:"code"`
	Status string `json:"status"`
	Data   struct {
		Authorized bool   `json:"authorized"`
		Address    string `json:"address"`
	} `json:"data"`
}

// DepositReportRequest represents the request body for the deposit/report endpoint
type DepositReportRequest struct {
	Amount        string `json:"amount"`
	TxID          string `json:"txId"`
	Source        string `json:"source"`
	TransactionID string `json:"transactionId"`
}

// DepositReportResponse represents the response from the deposit/report endpoint
type DepositReportResponse struct {
	Code   string `json:"code"`
	Status string `json:"status"`
}

// InitDeposit initializes a deposit request with Binance Pay and generates deep links
func (c *clientImpl) InitDeposit(ctx context.Context, requestID, network, currency string) (*DepositURLs, error) {
	timestamp := time.Now().UnixMilli()
	println("timestamp", timestamp)
	nonce := generateNonce()

	req := DepositInitRequest{
		RequestID: requestID,
		Network:   network,
		Currency:  currency,
		Source:    config.GetString("BINANCE_PAY_SOURCE"),
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	signature, err := signBody(timestamp, nonce, string(body), c.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate signature: %w", err)
	}

	var result DepositInitResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("BinancePay-Timestamp", fmt.Sprintf("%d", timestamp)).
		SetHeader("BinancePay-Nonce", nonce).
		SetHeader("BinancePay-Signature", signature).
		SetBody(string(body)).
		SetResult(&result).
		Post("https://bpay.binanceapi.com/binancepay/openapi/deposit/init")

	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed with status code %d: %s", resp.StatusCode(), resp.String())
	}
	if result.Status != "SUCCESS" || result.Code != "000000" {
		return nil, fmt.Errorf("api returned error: code=%s status=%s", result.Code, result.Status)
	}

	// Generate deep link
	txID := result.Data.TransactionID
	source := config.GetString("BINANCE_PAY_SOURCE")
	ts := fmt.Sprintf("%d", timestamp)

	// Step 1: Build the signature
	deepLinkSig, err := signUniversalLink(nonce, source, timestamp, txID, c.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate deep link signature: %w", err)
	}

	// Step 2: Build "q" and encode with base64url
	q := fmt.Sprintf("nonce=%s&source=%s&timestamp=%s&transactionId=%s&signature=%s",
		nonce, source, ts, txID, deepLinkSig)
	qEncoded := base64.RawURLEncoding.EncodeToString([]byte(q))

	// Step 3: Build the deeplink and encode with base64url
	startPageQuery := fmt.Sprintf("q=%s", qEncoded)
	startPageQueryEncoded := base64.StdEncoding.EncodeToString([]byte(startPageQuery))

	deeplink := fmt.Sprintf("%s?appId=%s&startPagePath=%s&startPageQuery=%s",
		result.Data.DpURL, result.Data.DpAppID, result.Data.DpStartPagePath, startPageQueryEncoded)

	deeplinkEncoded := base64.RawURLEncoding.EncodeToString([]byte(deeplink))

	// Step 4: Build the universalUrl with the deeplink
	universalURL := fmt.Sprintf("%s?_dp=%s", result.Data.UniversalLinkURL, deeplinkEncoded)

	return &DepositURLs{
		RequestID:     requestID,
		TransactionID: txID,
		UniversalURL:  universalURL,
	}, nil
}

type DepositAddress struct {
	Authorized bool
	Address    string
}

// QueryDepositAddress queries the deposit address for a given transaction
func (c *clientImpl) QueryDepositAddress(ctx context.Context, requestID, transactionID string) (*DepositAddress, error) {
	timestamp := time.Now().UnixMilli()
	nonce := generateNonce()

	req := DepositQueryRequest{
		RequestID:     requestID,
		TransactionID: transactionID,
		Source:        config.GetString("BINANCE_PAY_SOURCE"),
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	signature, err := signBody(timestamp, nonce, string(body), c.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to generate signature: %w", err)
	}

	var result DepositQueryResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("BinancePay-Timestamp", fmt.Sprintf("%d", timestamp)).
		SetHeader("BinancePay-Nonce", nonce).
		SetHeader("BinancePay-Signature", signature).
		SetBody(string(body)).
		SetResult(&result).
		Post("https://bpay.binanceapi.com/binancepay/openapi/deposit/query")

	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed with status code %d: %s", resp.StatusCode(), resp.String())
	}
	fmt.Printf("Deposit query result: %+v", resp)

	if result.Status != "SUCCESS" || result.Code != "000000" {
		return nil, fmt.Errorf("api returned error: code=%s status=%s", result.Code, result.Status)
	}
	// Log the result for debugging purposes

	return &DepositAddress{
		Authorized: result.Data.Authorized,
		Address:    result.Data.Address,
	}, nil
}

// ReportDeposit reports a deposit transaction to Binance Pay
func (c *clientImpl) ReportDeposit(ctx context.Context, amount, txID, transactionID string) error {
	timestamp := time.Now().UnixMilli()
	nonce := generateNonce()

	req := DepositReportRequest{
		Amount:        amount,
		TxID:          txID,
		Source:        config.GetString("BINANCE_PAY_SOURCE"),
		TransactionID: transactionID,
	}

	body, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	signature, err := signBody(timestamp, nonce, string(body), c.privateKey)
	if err != nil {
		return fmt.Errorf("failed to generate signature: %w", err)
	}

	var result DepositReportResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("BinancePay-Timestamp", fmt.Sprintf("%d", timestamp)).
		SetHeader("BinancePay-Nonce", nonce).
		SetHeader("BinancePay-Signature", signature).
		SetBody(string(body)).
		SetResult(&result).
		Post("https://bpay.binanceapi.com/binancepay/openapi/deposit/report")

	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}

	if !resp.IsSuccess() {
		return fmt.Errorf("request failed with status code %d: %s", resp.StatusCode(), resp.String())
	}
	if result.Status != "SUCCESS" || result.Code != "000000" {
		return fmt.Errorf("api returned error: code=%s status=%s", result.Code, result.Status)
	}

	return nil
}
