package binancepay

import (
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"math/big"
)

// hexToECDSAPrivate<PERSON>ey converts a hex string to an ECDSA private key
func hexToECDSAPrivateKey(hexKey string) (*ecdsa.PrivateKey, error) {
	// Decode the hex string to bytes
	privateKeyBytes, err := hex.DecodeString(hexKey)
	if err != nil {
		return nil, fmt.Errorf("failed to decode hex string: %w", err)
	}

	curve := elliptic.P256()

	// Convert bytes to a big.Int for the D value of the private key
	d := new(big.Int).SetBytes(privateKeyBytes)
	if d.Cmp(curve.Params().N) >= 0 || d.Sign() <= 0 {
		return nil, fmt.Errorf("invalid private key: D is out of range")
	}

	// Create the ECDSA private key
	priv := &ecdsa.PrivateKey{
		PublicKey: ecdsa.PublicKey{
			Curve: curve,
		},
		D: d,
	}

	// Calculate the public key point
	priv.X, priv.Y = curve.ScalarBaseMult(privateKeyBytes)
	if priv.X == nil || priv.Y == nil {
		return nil, fmt.Errorf("failed to compute public key from private key")
	}

	return priv, nil
}

func generateNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 32)
	for i := range b {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		b[i] = charset[n.Int64()]
	}
	return string(b)
}

func sign(payload string, privateKey *ecdsa.PrivateKey) (string, error) {
	hash := sha256.Sum256([]byte(payload))
	signature, err := ecdsa.SignASN1(rand.Reader, privateKey, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to sign payload: %w", err)
	}
	return base64.RawURLEncoding.EncodeToString(signature), nil
}

func signBody(timestamp int64, nonce, body string, privateKey *ecdsa.PrivateKey) (string, error) {
	payload := fmt.Sprintf("%d\n%s\n%s\n", timestamp, nonce, body)
	return sign(payload, privateKey)
}

func signDeepLink(nonce string, timestamp int64, transactionID string, privateKey *ecdsa.PrivateKey) (string, error) {
	payload := fmt.Sprintf("nonce=%s&timestamp=%d&transactionId=%s", nonce, timestamp, transactionID)
	return sign(payload, privateKey)
}

func signUniversalLink(nonce string, source string, timestamp int64, transactionID string, privateKey *ecdsa.PrivateKey) (string, error) {
	payload := fmt.Sprintf("nonce=%s&source=%s&timestamp=%d&transactionId=%s", nonce, source, timestamp, transactionID)
	return sign(payload, privateKey)
}
