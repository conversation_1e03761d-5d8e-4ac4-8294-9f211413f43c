package binancepay

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

type IClient interface {
	GetWithdrawNetworks(ctx context.Context) (*NetworkResponse, error)
	PreCreateWithdraw(ctx context.Context, currency, network, address string) (*WithdrawURLs, error)
	InitDeposit(ctx context.Context, requestID, network, currency string) (*DepositURLs, error)
	domain.TokenMetadataUpdater
	QueryDepositAddress(ctx context.Context, requestID, transactionID string) (*DepositAddress, error)
	ReportDeposit(ctx context.Context, amount, txID, transactionID string) error
}

type clientImpl struct {
	httpClient resty.Client
	privateKey *ecdsa.PrivateKey
}

var (
	instance IClient
)

func InitDefault() {
	var privateKey *ecdsa.PrivateKey
	privateKeyHex := config.GetString("BINANCE_PAY_PRIVATE_KEY")
	if privateKeyHex == "" {
		kglog.Warning("BINANCE_PAY_PRIVATE_KEY is not set")
	} else {
		// Parse hex private key to ECDSA P256 key
		var err error
		privateKey, err = hexToECDSAPrivateKey(privateKeyHex)
		if err != nil {
			panic(fmt.Errorf("failed to convert private key: %w", err))
		}
	}

	timeout := time.Duration(10 * time.Second)
	client := resty.NewRestyClient().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetTimeout(timeout)
	instance = &clientImpl{
		httpClient: client,
		privateKey: privateKey,
	}
}

func Set(c IClient) {
	instance = c
}

func Get() IClient {
	if instance == nil {
		panic("binancepay client not initialized")
	}
	return instance
}
