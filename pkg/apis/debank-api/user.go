package debankapi

// PortfolioItemObject protocol portfolio
type PortfolioItemObject struct {
	Stats       Stats              `json:"stats"`
	AssetDict   map[string]float64 `json:"asset_dict"`
	UpdatedAt   float64            `json:"updated_at"`
	Name        string             `json:"name"`
	DetailTypes []string           `json:"detail_types"`
	Detail      Detail             `json:"detail"`
	ProxyDetail ProxyDetail        `json:"proxy_detail"`
	Pool        Pool               `json:"pool"`
	SiteURL     string             `json:"site_url"`
}

// Stats protocol stats
type Stats struct {
	NetUsdValue   float64 `json:"net_usd_value"`
	AssetUsdValue float64 `json:"asset_usd_value"`
	DebtUsdValue  float64 `json:"debt_usd_value"`
}

// Detail protocol token detail
type Detail struct {
	SupplyTokenList []TokenObject `json:"supply_token_list"`
	RewardTokenList []TokenObject `json:"reward_token_list"`
	BorrowTokenList []TokenObject `json:"borrow_token_list"`
}

// ProxyDetail proxy detail
type ProxyDetail struct {
	Project         Project `json:"project"`
	ProxyContractID string  `json:"proxy_contract_id"`
}

// Pool protocol pool
type Pool struct {
	ID         string `json:"id"`
	Chain      string `json:"chain"`
	ProjectID  string `json:"project_id"`
	AdapterID  string `json:"adapter_id"`
	Controller string `json:"controller"`
	Index      string `json:"index"`
	TimeAt     int64  `json:"time_at"`
}

// TokenObject protocol token object
type TokenObject struct {
	ID              string  `json:"id"`
	Chain           string  `json:"chain"`
	Name            string  `json:"name"`
	Symbol          string  `json:"symbol"`
	DisplaySymbol   string  `json:"display_symbol"`
	OptimizedSymbol string  `json:"optimized_symbol"`
	Decimals        int     `json:"decimals"`
	LogoURL         string  `json:"logo_url"`
	ProtocolID      string  `json:"protocol_id"`
	Price           float64 `json:"price"`
	IsVerified      bool    `json:"is_verified"`
	IsCore          bool    `json:"is_core"`
	IsWallet        bool    `json:"is_wallet"`
	TimeAt          float64 `json:"time_at"`
	Amount          float64 `json:"amount"`
	IsCollateral    bool    `json:"is_collateral"`
}

// Project project
type Project struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	SiteURL string `json:"site_url"`
	LogoURL string `json:"logo_url"`
}
