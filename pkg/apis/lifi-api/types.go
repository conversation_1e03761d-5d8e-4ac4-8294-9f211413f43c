package lifiapi

import "github.com/kryptogo/kg-wallet-backend/domain"

type Token struct {
	Address  string `json:"address"`
	ChainId  int64  `json:"chainId"`
	Symbol   string `json:"symbol"`
	Decimals int    `json:"decimals"`
	Name     string `json:"name"`
	CoinKey  string `json:"coinKey"`
	LogoURI  string `json:"logoURI"`
	PriceUSD string `json:"priceUSD"`
}

type Action struct {
	FromChainId               int64   `json:"fromChainId"`
	FromAmount                string  `json:"fromAmount"`
	FromToken                 Token   `json:"fromToken"`
	FromAddress               string  `json:"fromAddress"`
	ToChainId                 int64   `json:"toChainId"`
	ToToken                   Token   `json:"toToken"`
	ToAddress                 string  `json:"toAddress"`
	Slippage                  float64 `json:"slippage"`
	DestinationGasConsumption string  `json:"destinationGasConsumption,omitempty"`
}

// Request parameter types
type QuoteParams struct {
	FromChain      domain.Chain
	FromAddress    string
	FromToken      string
	FromAmount     string
	ToChain        domain.Chain
	ToToken        string
	ToAddress      string
	SkipSimulation bool
}

type GasCost struct {
	Amount    string `json:"amount"`
	AmountUSD string `json:"amountUSD"`
	Token     Token  `json:"token"`
	Estimate  string `json:"estimate"`
	Limit     string `json:"limit"`
	Price     string `json:"price"`
	Type      string `json:"type"`
}

type FeeCost struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Token       Token  `json:"token"`
	Amount      string `json:"amount"`
	AmountUSD   string `json:"amountUSD"`
	Percentage  string `json:"percentage"`
	Included    bool   `json:"included"`
}

type Estimate struct {
	FromAmount        string    `json:"fromAmount"`
	ToAmount          string    `json:"toAmount"`
	ToAmountMin       string    `json:"toAmountMin"`
	ApprovalAddress   string    `json:"approvalAddress"`
	GasCosts          []GasCost `json:"gasCosts"`
	FeeCosts          []FeeCost `json:"feeCosts"`
	ExecutionDuration int       `json:"executionDuration"`
	FromAmountUSD     string    `json:"fromAmountUSD"`
	ToAmountUSD       string    `json:"toAmountUSD"`
	Tool              string    `json:"tool,omitempty"`
}

type ToolDetails struct {
	Key     string `json:"key"`
	Name    string `json:"name"`
	LogoURI string `json:"logoURI"`
}

type Step struct {
	Id          string      `json:"id"`
	Type        string      `json:"type"`
	Tool        string      `json:"tool"`
	Action      Action      `json:"action"`
	Estimate    Estimate    `json:"estimate"`
	ToolDetails ToolDetails `json:"toolDetails"`
}

type TransactionRequest struct {
	Data     string `json:"data"`
	To       string `json:"to"`
	Value    string `json:"value"`
	From     string `json:"from"`
	ChainId  int    `json:"chainId"`
	GasPrice string `json:"gasPrice"`
	GasLimit string `json:"gasLimit"`
}

type Quote struct {
	Id                 string             `json:"id"`
	Type               string             `json:"type"`
	Tool               string             `json:"tool"`
	ToolDetails        ToolDetails        `json:"toolDetails"`
	Action             Action             `json:"action"`
	Estimate           Estimate           `json:"estimate"`
	IncludedSteps      []Step             `json:"includedSteps"`
	Integrator         string             `json:"integrator"`
	TransactionRequest TransactionRequest `json:"transactionRequest"`
}

type ErrResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}
