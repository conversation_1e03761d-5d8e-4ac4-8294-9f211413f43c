package lifiapi

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	apiBaseURL               = "https://li.quest/v1"
	defaultTimeout           = 15 * time.Second
	evmNativeTokenAddress    = "******************************************"
	solanaNativeTokenAddress = "SOL"
)

var (
	lifiObj    ILifi
	chainIdMap = map[domain.Chain]string{
		domain.Ethereum:  "eth",
		domain.Polygon:   "pol",
		domain.Arbitrum:  "arb",
		domain.BNBChain:  "bsc",
		domain.BaseChain: "bas",
		domain.Solana:    "sol",
		domain.Optimism:  "opt",
	}
)

// ILifi defines the interface for Li.Fi API operations
type ILifi interface {
	GetQuote(ctx context.Context, params *QuoteParams) (*Quote, error)
}

type Client struct {
	client resty.Client
	rl     domain.RateLimiter
}

// InitDefault initializes the default Li.Fi API client
func InitDefault(rl domain.RateLimiter) {
	client := resty.NewRestyClient()
	lifiObj = &Client{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiBaseURL).
			SetTimeout(defaultTimeout),
		rl: rl,
	}
}

// Init initializes with custom client for testing
func Init(client resty.Client, rl domain.RateLimiter) {
	lifiObj = &Client{
		client: client.
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(apiBaseURL).
			SetTimeout(defaultTimeout),
		rl: rl,
	}
}

// Set sets the Li.Fi API singleton
func Set(api ILifi) {
	lifiObj = api
}

// Get gets the Li.Fi API singleton
func Get() ILifi {
	return lifiObj
}
