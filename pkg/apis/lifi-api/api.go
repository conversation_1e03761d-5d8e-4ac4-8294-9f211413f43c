package lifiapi

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

func getNormalizedTokenAddresses(tokenID string, chain domain.Chain) string {
	if tokenID == chain.MainToken().ID() {
		if chain == domain.Solana {
			return solanaNativeTokenAddress
		} else {
			return evmNativeTokenAddress
		}
	}
	return tokenID
}

func (c *Client) GetQuote(ctx context.Context, params *QuoteParams) (*Quote, error) {
	fromToken := getNormalizedTokenAddresses(params.FromToken, params.FromChain)
	toToken := getNormalizedTokenAddresses(params.ToToken, params.ToChain)
	urlParams := url.Values{
		"fromChain":      []string{chainIdMap[params.FromChain]},
		"toChain":        []string{chainIdMap[params.ToChain]},
		"fromToken":      []string{fromToken},
		"toToken":        []string{toToken},
		"toAddress":      []string{params.ToAddress},
		"fromAmount":     []string{params.FromAmount},
		"fromAddress":    []string{params.FromAddress},
		"skipSimulation": []string{"true"},
		"slippage":       []string{"0.05"},
	}

	pathWithParams := fmt.Sprintf("%s/quote?%s", apiBaseURL, urlParams.Encode())
	resp := &Quote{}

	retryErr := util.RetryWrapper(ctx, time.Second*15, func() error {
		err := c.rl.Wait(ctx, "", time.Second*10)
		if err != nil {
			return err
		}

		r, err := c.client.R().
			SetContext(ctx).
			SetResult(resp).
			Get(pathWithParams)

		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[lifiAPI] GetQuote failed", map[string]interface{}{
				"params": params,
				"err":    err.Error(),
			})
			return err
		}

		if r.StatusCode() >= 400 {
			kglog.WarningWithDataCtx(ctx, "lifi quote error", map[string]interface{}{"status": r.StatusCode(), "body": r.String()})
			errResp := &ErrResponse{}
			_ = json.Unmarshal(r.Body(), errResp)
			if errResp.Code == 1002 { // No available quotes
				return code.ErrDexAmountTooSmall
			}
			return fmt.Errorf("lifi quote error: %d", r.StatusCode())
		}

		return nil
	})

	if retryErr != nil {
		return nil, retryErr
	}

	return resp, nil
}
