package payment

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/domain"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/shopspring/decimal"
)

const (
	// kgDeepLinkFormat defines the URL structure for the KryptoGO deeplink
	kgDeepLinkFormat = "https://kryptogo.page.link/send?to=%s&chainId=%s&assetGroup=%s&amount=%s"
)

// PricingMode represents the pricing mode for a payment intent
type PricingMode string

const (
	// PricingModeFiat indicates the amount is specified in fiat currency (e.g., USD, EUR)
	PricingModeFiat PricingMode = "fiat"
	// PricingModeCrypto indicates the amount is specified directly in cryptocurrency
	PricingModeCrypto PricingMode = "crypto"
)

// IsValid checks if the pricing mode is valid
func (p PricingMode) IsValid() bool {
	return p == PricingModeFiat || p == PricingModeCrypto
}

// String returns the string representation of the pricing mode
func (p PricingMode) String() string {
	return string(p)
}

type CreateIntentParams struct {
	PricingMode PricingMode
	Amount      string

	Currency *string

	PayToken string
	Chain    domain.Chain

	ClientID            string
	Origin              string
	OrderData           map[string]any
	CallbackURL         *string
	GroupKey            *string
	PayoutTargetAddress *domain.Address
}

// CreateIntent creates a payment intent from web sdk. It also verifies the client id and origin.
func CreateIntent(ctx context.Context, params CreateIntentParams) (*Intent, *code.KGError) {
	ctx, span := tracing.Start(ctx, "payment.CreateIntent")
	defer span.End()

	// Validate pricing mode
	if params.PricingMode == "" {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("pricing mode is required"), nil)
	}

	// Validate PayToken (must be either USDC or USDT)
	payToken := strings.ToUpper(params.PayToken)
	if payToken != "USDC" && payToken != "USDT" {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest,
			fmt.Errorf("pay_token must be either USDC or USDT, got: %s", params.PayToken), nil)
	}

	// Validate chain
	if params.Chain == nil {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("chain is required"), nil)
	}

	orgID, kgErr := application.GetApplicationOrgId(ctx, params.ClientID)
	if kgErr != nil {
		return nil, kgErr
	}

	// Validate amount
	amount, err := decimal.NewFromString(params.Amount)
	if err != nil || amount.LessThanOrEqual(decimal.Zero) {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid amount"), nil)
	}

	// Validate callback URL if provided
	if params.CallbackURL != nil {
		err = validateCallbackURL(*params.CallbackURL)
		if err != nil {
			return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid callback url: %w", err), nil)
		}
	}

	// Get token address and metadata
	tokenAddress, err := getTokenAddress(params.Chain, payToken)
	if err != nil {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("get token address failed: %w", err), nil)
	}
	token, err := tokenmeta.Get(ctx, params.Chain, tokenAddress)
	if err != nil {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("get token metadata failed: %w", err), nil)
	}

	var cryptoAmount, fiatAmount decimal.Decimal
	var fiatCurrency string
	var cryptoPriceDecimal decimal.Decimal

	// Determine the target fiat currency for fetching the crypto price
	var targetFiatForPrice string
	if params.PricingMode == PricingModeFiat {
		if params.Currency == nil { // Should have been validated earlier, but as a safeguard
			return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("currency is required for fiat pricing mode"), nil)
		}
		targetFiatForPrice = *params.Currency
	} else { // PricingModeCrypto
		if params.Currency != nil {
			targetFiatForPrice = *params.Currency
		} else {
			targetFiatForPrice = "usd" // Default to USD if no currency specified in crypto mode
		}
	}

	// Always fetch the exchange rate for CryptoPrice
	isSupported, err := coingeckoapi.Get().IsCurrencySupported(ctx, strings.ToLower(targetFiatForPrice))
	if err != nil {
		kglog.ErrorfCtx(ctx, "failed to check currency support for CryptoPrice: %v", err)
		return nil, code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, err, nil)
	}
	if !isSupported {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest,
			fmt.Errorf("unsupported fiat currency for CryptoPrice: %s", targetFiatForPrice), nil)
	}

	cID := domain.CoingeckoID(token.CoingeckoID)
	priceMap, err := coingeckoapi.Get().SimplePrice(ctx, []domain.CoingeckoID{cID}, strings.ToLower(targetFiatForPrice))
	if err != nil {
		kglog.ErrorfCtx(ctx, "failed to get exchange rate for CryptoPrice: %v", err)
		return nil, code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, err, nil)
	}
	rate := priceMap[cID]
	if rate == 0 {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid exchange rate for CryptoPrice: %f", rate), nil)
	}
	cryptoPriceDecimal = decimal.NewFromFloat(rate)

	// Calculate crypto and fiat amounts based on pricing mode
	if params.PricingMode == PricingModeCrypto {
		cryptoAmount = amount // amount from params is cryptoAmount

		// If a currency was explicitly provided in crypto mode, calculate corresponding fiatAmount
		if params.Currency != nil {
			fiatAmount = cryptoAmount.Mul(cryptoPriceDecimal)
			fiatCurrency = *params.Currency
		}
		// If no currency provided in crypto mode, FiatAmount & FiatCurrency remain unset (nil)
	} else { // PricingModeFiat
		fiatAmount = amount         // amount from params is fiatAmount
		if params.Currency == nil { // Should be caught by validation, but defensive
			return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("currency is required for fiat pricing mode"), nil)
		}
		fiatCurrency = *params.Currency
		if cryptoPriceDecimal.IsZero() { // Avoid division by zero if rate was 0 (though checked above)
			return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, errors.New("crypto price is zero, cannot divide"), nil)
		}
		cryptoAmount = fiatAmount.Div(cryptoPriceDecimal)
	}

	// Generate deterministic salt for the wallet address
	// Using clientID and current timestamp to ensure uniqueness
	saltInput := params.ClientID + time.Now().String()
	salt := crypto.Keccak256Hash([]byte(saltInput))

	// Use kg wallet as the owner address
	ownerAddress, kgErr := getOrganizationAddress(ctx, params.Chain, 1)
	if kgErr != nil {
		return nil, kgErr
	}
	paymentAddress, err := erc4337.GetAccountAddress(params.Chain, ownerAddress, salt.Big())
	if err != nil {
		kglog.ErrorfCtx(ctx, "failed to calculate wallet address: %v", err)
		return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}

	saltHex := salt.String()[2:] // Remove the "0x" prefix
	intent := &domain.PaymentIntent{
		ClientID:            params.ClientID,
		OrgID:               orgID,
		PaymentChain:        params.Chain,
		PaymentAddress:      paymentAddress,
		PayoutTargetAddress: params.PayoutTargetAddress,
		PaymentAddressSalt:  saltHex,
		TokenAddress:        tokenAddress,
		Symbol:              payToken,
		Decimals:            token.Decimals,
		CryptoAmount:        cryptoAmount,
		CryptoPrice:         &cryptoPriceDecimal, // Always set the precise crypto price
		PaymentDeadline:     time.Now().Add(30 * time.Minute),
		Status:              domain.PaymentIntentStatusPending,
		OrderData:           params.OrderData,
		CallbackURL:         params.CallbackURL,
		GroupKey:            params.GroupKey,
		PricingMode:         params.PricingMode.String(),
	}

	// Set FiatAmount and FiatCurrency only if they were determined
	if params.PricingMode == PricingModeFiat || (params.PricingMode == PricingModeCrypto && params.Currency != nil) {
		intent.FiatAmount = &fiatAmount
		intent.FiatCurrency = &fiatCurrency
	} else {
		// Ensure these are explicitly nil if not applicable
		intent.FiatAmount = nil
		intent.FiatCurrency = nil
	}

	createdIntent, err := r.CreatePaymentIntent(ctx, intent)
	if err != nil {
		kglog.ErrorfCtx(ctx, "failed to create payment intent: %v", err)
		return nil, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	if params.Chain.IsEVM() {
		err = user.RegisterAlchemyWebhook(ctx, []string{params.Chain.ID()}, []string{paymentAddress.String()})
		if err != nil {
			kglog.ErrorfCtx(ctx, "failed to register alchemy webhook: %v", err)
			return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
		}
	}

	IntentStatusChanged(ctx, createdIntent)

	return fromDomain(createdIntent), nil
}

// get organization address
func getOrganizationAddress(ctx context.Context, chain domain.Chain, orgID int) (domain.EvmAddress, *code.KGError) {
	orgWallet, kgErr := r.GetWalletsByOrganizationId(ctx, orgID)
	if kgErr != nil {
		return domain.NewEvmAddress(""), kgErr
	}
	if chain.IsEVM() {
		return domain.NewEvmAddress(orgWallet.EvmAddress), nil
	}
	return domain.NewEvmAddress(""), code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("unsupported chain: %s", chain), nil)
}

func GetIntent(ctx context.Context, clientID, id string) (*Intent, *code.KGError) {
	intent, err := r.GetPaymentIntentByID(ctx, id)
	if err != nil {
		if errors.Is(err, domain.ErrRecordNotFound) {
			return nil, code.NewKGError(code.PaymentIntentNotFound, http.StatusNotFound, err, nil)
		}
		kglog.ErrorfCtx(ctx, "failed to get payment intent: %v", err)
		return nil, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	// Verify the client ID matches
	if intent.ClientID != clientID {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid client id"), nil)
	}

	return fromDomain(intent), nil
}

// GetIntents retrieves a list of payment intents based on the provided parameters
func GetIntents(ctx context.Context, params domain.GetPaymentIntentsParams) ([]*Intent, int, *code.KGError) {
	ctx, span := tracing.Start(ctx, "payment.GetIntents")
	defer span.End()

	intents, totalCount, err := r.GetPaymentIntents(ctx, params)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "failed to get payment intents", map[string]interface{}{
			"error":     err.Error(),
			"client_id": params.ClientID,
			"status":    params.Status,
		})
		return nil, 0, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	// Convert domain models to response models
	result := make([]*Intent, len(intents))
	for i, intent := range intents {
		result[i] = fromDomain(intent)
	}

	return result, totalCount, nil
}
