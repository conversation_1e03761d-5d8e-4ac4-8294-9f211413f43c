package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/samber/lo"
)

func main() {
	ctx := context.Background()

	// Parse command-line flags
	newWebhookURL := flag.String("url", "", "New webhook URL")
	network := flag.String("network", "", "Network (e.g., ETH_MAINNET)")
	flag.Parse()

	// Validate required flags
	if *newWebhookURL == "" || *network == "" {
		fmt.Println("Usage: migrate-alchemy-webhook --source=<sourceWebhookID> --url=<newWebhookURL> --network=<network>")
		os.Exit(1)
	}

	// Get Alchemy token from config
	alchemyToken := config.GetString("ALCHEMY_TOKEN")
	if alchemyToken == "" || alchemyToken == "REDACTED" {
		panic("missing ALCHEMY_TOKEN env")
	}

	// Initialize Alchemy API
	alchemyapi.InitDefault()
	api := alchemyapi.Get()

	// Retrieve all webhook addresses from DB
	var allAddresses []string
	allAddresses, err := rdb.GormRepo().GetAllEvmAddresses(ctx, true)
	if err != nil {
		fmt.Println("Error fetching all EVM addresses:", err)
		os.Exit(1)
	}

	uniqueAddresses := lo.Uniq(allAddresses)
	fmt.Printf("uniqueAddresses length: %d\n", len(uniqueAddresses))

	// Chunk the uniqueAddresses into batches of 500
	addressBatches := lo.Chunk(uniqueAddresses, 500)

	if len(addressBatches) == 0 {
		fmt.Println("No addresses to process")
		os.Exit(1)
	}

	// Create the new webhook with the first batch of addresses
	newWebhookID, err := api.CreateWebhook(ctx, *network, *newWebhookURL, addressBatches[0])
	if err != nil {
		fmt.Println("Error creating new webhook:", err)
		os.Exit(1)
	}

	// Update the source webhook to inactive
	err = api.UpdateWebhookIsActive(ctx, newWebhookID, false)
	if err != nil {
		fmt.Println("Error updating source webhook to inactive:", err)
		os.Exit(1)
	}

	// If there are remaining batches, add them using AddSingleWebhookAddresses
	for _, batch := range addressBatches[1:] {
		time.Sleep(1 * time.Second)
		err := api.AddSingleWebhookAddresses(ctx, newWebhookID, batch)
		if err != nil {
			fmt.Println("Error adding addresses to new webhook. Performing binary search to find problematic address.")

			// Binary search to find the problematic address
			left, right := 0, len(batch)-1
			for left <= right {
				mid := (left + right) / 2
				err := api.AddSingleWebhookAddresses(ctx, newWebhookID, batch[:mid+1])
				if err != nil {
					right = mid - 1
				} else {
					if mid == len(batch)-1 || api.AddSingleWebhookAddresses(ctx, newWebhookID, batch[:mid+2]) != nil {
						fmt.Printf("Problematic address found: %s\n", batch[mid+1])
						break
					}
					left = mid + 1
				}
			}

			fmt.Println("Exiting due to error in adding addresses.")
			os.Exit(1)
		}
	}

	fmt.Println("Successfully create new webhook and add addresses to the new webhook.")
}
