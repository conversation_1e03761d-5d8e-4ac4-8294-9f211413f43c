#!/bin/sh

# general
export ENV=local
export APP_PORT=8080
export CORS_ALLOW_ORIGINS='["https://wallet-dev.kryptogo.com","https://wallet.kryptogo.com","http://localhost:3000","http://localhost:3001","https://dashboard-dev.kryptogo.com","http://localhost:4200","https://studio-dev.kryptogo.com","https://store-dev.kryptogo.com","https://sdk-doc.kryptogo.com","https://www.kryptogo.xyz","https://app.kryptogo.xyz","https://app-dev.kryptogo.xyz","https://sweep.kryptogo.xyz","https://pay.kryptogo.com"]'
export PROJECT_ID=testing-kryptogo-wallet-app
export REGION=asia-east1

# database
export MYSQL_HOST=127.0.0.1
export MYSQL_PORT=3306
export MYSQL_USERNAME=root
export MYSQL_PASSWORD=root
export MYSQL_OPTIONS=charset=utf8mb4\&parseTime=True\&loc=UTC
export MYSQL_DATABASE=wallet
export MYSQL_SLOW_THRESHOLD=1000
export MYSQL_DSN="$MYSQL_USERNAME:$MYSQL_PASSWORD@tcp($MYSQL_HOST:$MYSQL_PORT)/$MYSQL_DATABASE?$MYSQL_OPTIONS"
export DEBUG_DATABASE=false

# redis
export REDIS_HOST=127.0.0.1
export REDIS_PORT=6379
export REDIS_AUTH=

# GCP
export CDN_HOST="https://wallet-static-dev.kryptogo.com/"
export GS_BUCKET_NAME="kryptogo-wallet-app-dev.appspot.com"
export FIREBASE_API_KEY=
export IOS_RECAPTCHA_SITE_KEY=
export ANDROID_RECAPTCHA_SITE_KEY=
export WEB_RECAPTCHA_SITE_KEY=
export SERVICE_ACCOUNT_EMAIL=<EMAIL>

# GOOGLE OAUTH
export GOOGLE_OAUTH_CLIENT_ID=REDACTED
export GOOGLE_OAUTH_CLIENT_SECRET=REDACTED

# auth
export JWT_SECRET_KEY=jwt_secret_key
export STUDIO_JWT_SECRET_KEY=studio_jwt_secret_key
export ACCESS_TOKEN_EXP_MINUTES=1440
_OAUTH_CLIENTS="$(cat config/$ENV/oauth-clients.json)"
export OAUTH_CLIENTS="$_OAUTH_CLIENTS"
# should not be updated unless signingServerAPITokenLocal is updated
export KG_TOKEN_SECRET=secret

# stickey
export STICKEY_INTERNAL_HOST="https://api-rtqsyjzxya-de.a.run.app"

# signing
export CLEF_HOST="http://127.0.0.1"
export CLEF_PORT_MATIC=8550
export CLEF_PORT_POLYGON=8550
export CLEF_PORT_GOERLI=8551
export CLEF_PORT_MUMBAI=8552
export TEST_SIGNING_HOST=localhost
export TEST_SIGNING_PORT=8101
export TEST_SHEET_CONTENT='[["Timestamp","Phone","鏈上交易結果(tx_hash)","disable sms"]]'
## signing-wallet
export KG_EVM_PRIVATE_KEY=cjV4WEJJK2N6TjM3S1NRTFhab0NaZ0Q3cmEzUEN5RFluV25OQnUwVkhuZ0lZU0Rsbm82M2tMS21uMk5hY3VjWFV1SUVvcmtVRWt6eGQ4eUxRays5cXFXVUNYWktiSkxFdVZZWEhUMUd4dUJMM3Z0SnZ1U2xpTjR1RE0xZHhidldWa2Z3YllvN2tyemIrd2ZlQmlPb093PT0=
export KG_EVM_ADDRESS=******************************************
export KG_TRON_PRIVATE_KEY=SDQ5VnFQWXcvK3ZjczFkOFdXRG5XSWordXc2clJkRnN6Wnp2WVhkYUl5dk1mM21lZTVwTlpQWk80VGhhNWp5LzRDbGllbThhbVFqUFJwRldtV051TG5KUGhDenlucFAxSWkyY3ozbk1PUzR0UWk1NWlCZndCWGxOamw2RCtKY2psL2RUZkdWZVViVTk4MjFyQlk2bVJnPT0=
export KG_TRON_ADDRESS=TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7
export TAG_EVM_PRIVATE_KEY=d2FTeEtJNjdrTHlYOE10aXFqZFdUNUdjdW1ZdEYzNUlSRU5iUGhWUnp3S1kwUlRiTWYwWk9ON2ZoYXNObUtMZ0QzTFczWUNQL3dpU1k5d2xJbFAzQUhaMDB1bDBoVWpZZ1JnRG5MN0tGRjBXNHVkUVpFZDBDREFhWC9OMVhsbUszUDdSOGlnK2ZrYVlYQmlxRUlsMGZBPT0=
export TAG_EVM_ADDRESS=******************************************
export TAG_LOCALHOST_PRIVATE_KEY=************************************************************************************************************************************************************************************************************
export TAG_LOCALHOST_ADDRESS=******************************************
export TAG_TRON_PRIVATE_KEY=SDQ5VnFQWXcvK3ZjczFkOFdXRG5XSWordXc2clJkRnN6Wnp2WVhkYUl5dk1mM21lZTVwTlpQWk80VGhhNWp5LzRDbGllbThhbVFqUFJwRldtV051TG5KUGhDenlucFAxSWkyY3ozbk1PUzR0UWk1NWlCZndCWGxOamw2RCtKY2psL2RUZkdWZVViVTk4MjFyQlk2bVJnPT0=
export TAG_TRON_ADDRESS=TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7

# sms
export TWILIO_ACCOUNT_SID=
export TWILIO_AUTH_TOKEN=
export TWILIO_SENDER="+***********"
export ENABLE_TWILIO_VERIFY=false
export TWM_USERNAME=
export TWM_PASSWORD=
export TWM_SRC_ADDR=
_SMS_TESTERS="$(cat config/$ENV/sms-tester.json)"
export SMS_TESTERS="$_SMS_TESTERS"
_SMS_QA="$(cat config/$ENV/sms-qa.json)"
export SMS_QA="$_SMS_QA"
_DEVELOPER_PHONES="$(cat config/$ENV/developer-phone.json)"
export DEVELOPER_PHONES="$_DEVELOPER_PHONES"
_SMS_WHITELIST="$(cat config/sms-whitelist.json)"
export SMS_WHITELIST="$_SMS_WHITELIST"

# email
export SENDGRID_API_KEY=REDACTED
_EMAIL_TESTERS="$(cat config/$ENV/email-tester.json)"
export EMAIL_TESTERS="$_EMAIL_TESTERS"
_EMAIL_QA="$(cat config/$ENV/email-qa.json)"
export EMAIL_QA="$_EMAIL_QA"
_PM_EMAILS="$(cat config/$ENV/email-pm.json)"
export PM_EMAILS="$_PM_EMAILS"

# instant messaging
export SENDBIRD_KRYPTOGO_API_TOKEN=REDACTED
export SENDBIRD_STICKEY_API_TOKEN=REDACTED
export SENDBIRD_SESSION_TOKEN_TTL_IN_MINUTES=2

# host
export DYNAMIC_LINK_HOST="https://kryptogodev.page.link"
export COMPLIANCE_HOST="https://api-dev.kryptogo.com"
export SELF_HOST="http://localhost:8080"
export SELF_INTERNAL_HOST="http://localhost:8030"
export ACCOUNT_HOST="http://localhost:8080"
export STUDIO_HOST="http://localhost:8080"
export PWA_HOST="http://localhost:8080"

# 3rd party API
export ALCHEMY_WEBHOOK_ID_MAP='{"eth":"eth-webhook","matic":"matic-webhook","arb":"arb-webhook","bsc":"bsc-webhook","sepolia":"sepolia-webhook","holesky":"holesky-webhook"}'
export ALCHEMY_API_KEY="anE9xy-VEqWQxX2jsgfAzHkLWWLov3GQ"
export STICKEY_ALCHEMY_API_KEY=
export ALCHEMY_TOKEN=
export ARBISCAN_API_KEY=
export STICKEY_ARBISCAN_API_KEY=
export BINANCE_API_KEY=
export BINANCE_SECRET_KEY=
export BLOCKCHAIR_API_KEY=
export BSCSCAN_API_KEY=
export STICKEY_BSCSCAN_API_KEY=
export COVALENT_API_KEY=
export ENCRYPTION_KEY=
export ETHERSCAN_API_KEY=
export STICKEY_ETHERSCAN_API_KEY=
export INFURA_PROJECT_ID=
export STICKEY_INFURA_PROJECT_ID=
export JUMIO_API_KEY=
export KCC_API_KEY=
export MORALIS_API_KEY=
export OPENSEA_API_KEY=
export POLYGONSCAN_API_KEY=
export STICKEY_POLYGONSCAN_API_KEY=
export STRIPE_SECRET_KEY=
export STRIPE_WEBHOOK_SECRET=
export ZERION_API_KEY=
export ZERION_API_KEY_V2=
export SOLSCAN_API_KEY=
export SOLSCAN_API_KEY_V2=
export POAP_API_KEY=
export BLOWFISH_API_KEY=
export COINGECKO_API_KEY=
export TRANSPOSE_API_KEY=
export CRYSTAL_API_KEY=
export TRONSCAN_PRO_API_KEY=
export FEEE_API_KEY=
export TRONGRID_API_KEY=
export QUICKNODE_API_KEY=
export QUICKNODE_API_KEY_V2=0be5fa44c1f086febd940bee2f9296b79a960c25
export SCAM_SNIFFER_API_KEY=
export OPSCAN_API_KEY=
export BASESCAN_API_KEY=

# KYC
export COMPLIANCE_API_KEY=
export DD_TASK_SEARCH_SETTING_ID_TW=1
export DD_TASK_SEARCH_SETTING_ID_EN=205
export COMPLIANCE_SHARED_SECRET=wallet_shared_secret_key

# dashboard and nft event
export DASHBOARD_WHITELIST="******************************************"
export WATCHING_NFT_CONTRACT_ADDRESS=""
export AIRDROP_EVENT_SETTING='{"kryptogo-yacht-club": {"ttl": "", "contract_schema_name": "ERC721"}}'
export AIRDROP_SHEET_INFO='{"kryptogo-yacht-club":[{"spreadsheet_id": "1_mvBBnGltUFxrudIezirvt53cIvSFwWk_p7J1FEGUOw", "token_id": 0, "sheet_name": "Sheet1", "column_phone": 1, "column_disable_sms": 2, "column_hash": 3, "action": "airdrop"}]}'


# buy crypto
export SLACK_WEBHOOK_URL="*******************************************************************************"
export ORDER_ENABLED=true
export STRIPE_ENABLED=false
export BINANCE_ENABLED=false
export BUY_CRYPTO_URL="https://wallet-dev.kryptogo.com/buy-crypto"
export ORDER_DETAIL_URL="https://wallet-dev.kryptogo.com/order-history"
export SINGLE_ORDER_LOWER_LIMIT=100
export WEEK_ORDER_UPPER_LIMIT=1000
export LARGE_TRANSACTION_AMOUNT=500

# Open telemetry tracing
export OTEL_COLLECTOR_HOST=
export OTEL_COLLECTOR_PORT=
export OTEL_COLLECTOR_SERVICE=api

# emulator
export FIRESTORE_EMULATOR_HOST="localhost:8080"
export FIREBASE_AUTH_EMULATOR_HOST="localhost:9099"
export FIREBASE_STORAGE_EMULATOR_HOST="localhost:9199"
export STORAGE_EMULATOR_HOST="localhost:9199"

# for testing or local only var. Var name should start with "TEST_"
export TEST_KMS_HOST=127.0.0.1
export TEST_KMS_PORT=8102
export TEST_3RD_PARTY_API=false
_SECRET_MANAGER_DATA="$(cat config/local-secret-manager.json)"
export TEST_SECRET_MANAGER_DATA="$_SECRET_MANAGER_DATA"

# studio
export WEB_HOST=https://wallet-dev.kryptogo.com
export KGSTORE_BASE_URL=https://store-dev.kryptogo.com

# studio line bot
export LINE_ACCOUNT_LINK_LOGIN_URL="$ACCOUNT_HOST/auth/login"
export LINE_BOT_CALLBACK_URL="$SELF_HOST/v1/studio/line_bot/callback"

# proxy server
export PROXY_SERVERS='[]'
export PROXY_USERNAME=
export PROXY_PASSWORD=

# gas swap service
export GAS_SWAP_CONTRACTS='{"shasta":"TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft"}'
export GAS_SWAP_FEE_PERCENT=10

# gasless send service
export GASLESS_SEND_CONTRACTS='{"shasta":"TYnqfxePKF2K8Fh9pviTGEVaNf4P7FTuX7","tron":"TDjWJXAdbs6akJE6jJdT4sfjY2Y73uGZq5"}'

# ephemeral note service
export EPHEMERAL_NOTES_CONTRACTS='{"sepolia":"******************************************","shasta":"TNv9rtCynE6J6ZjmZbouFY46BhZTMNKFUx","holesky":"******************************************"}'
export EPHEMERAL_NOTES_FEES='{"sepolia":"0.1","shasta":"0.5","holesky":"0.1"}'

# send with fee service
export SEND_WITH_FEE_CONTRACTS='{"shasta":"TYZKKrJFnsxmVANwRrfwBfnKexpxtrsbXK","tron":"TCxUypP7WpQYsCHEj6k3eXB2mL5ToFf1R7"}'

# cloud task
export CLOUD_TASK_API_QUEUE=api-async-tasks-queue
export CLOUD_TASK_TX_WATCH_QUEUE=watch-tx-task-queue
# user repo implementation
export USER_REPO=only_mysql

# Asset Price Update frequrency parameters
export ASSET_PRICE_UPDATE_JOB_BLANK_SEC=10
export ASSET_PRICE_UPDATE_JOB_REGULAR_SEC=300

# chain sync service
export CLOUD_TASK_CHAIN_SYNC_QUEUE=chain-sync-task-queue

# tron energy recharge
export TRON_ENERGY_RECHARGE_THRESHOLD=20
export TRON_ENERGY_RECHARGE_AMOUNT=100

# binance pay
export BINANCE_PAY_SOURCE=
export BINANCE_PAY_PRIVATE_KEY=
export BINANCE_PAY_REDIRECT_URL=https://wallet-dev.kryptogo.com

# send link campaign
export SEND_LINK_CAMPAIGN_ENABLED=false

# payment
export PAYMASTER_CONTRACTS='{"holesky":"******************************************","sepolia":"******************************************","arb":"******************************************","bsc":"******************************************","matic":"******************************************","eth":"******************************************","base":"******************************************","optimism":"******************************************"}'
export ACCOUNT_FACTORY_ADDRESS='******************************************'

# okx
export OKX_ACCESS_KEY=
export OKX_SECRET_KEY=
export OKX_PASSPHRASE=

# kryptogo.xyz
export XYZ_TRADING_FEE=0.001
export XYZ_TRADING_VOLUME_PER_CREDIT=100
export XYZ_CREDIT_SINGLE_PRICE=0.001
export XYZ_CREDIT_10_PACKAGE_PRICE=0.0008
export XYZ_CREDIT_50_PACKAGE_PRICE=0.00065
