package bridge

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/bridge"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func setupBridgeTest(t *testing.T) (*gin.Engine, string) {
	rdb.Reset()
	uRepo := repo.Unified()
	oauth.Init(uRepo)
	application.Init(uRepo)
	bridge.Init(uRepo)
	tokenmeta.Init(uRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	assert.Nil(t, rdb.Get().Create([]model.AssetPrice{
		{
			ChainID:    domain.Arbitrum.ID(),
			AssetGroup: "******************************************",
			Price:      "0.98",
		},
	}).Error)
	// Create test organizations and OAuth applications
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "KryptoGO"},
		{ID: 2, Name: "Test App"},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.OAuthClientConfig{
		{
			ID:           "test_client_id",
			Domain:       "http://test.com",
			IsPrivileged: true,
			Name:         "Test App",
			Secret:       "test_secret",
			IsCustomAuth: false,
		},
		{
			ID:           "another_client_id",
			Domain:       "http://another.com",
			IsPrivileged: true,
			Name:         "Another App",
			Secret:       "another_secret",
			IsCustomAuth: false,
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationClient{
		{
			OrganizationID:  1,
			ClientID:        "test_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
		{
			OrganizationID:  2,
			ClientID:        "another_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationWallet{
		{
			OrganizationID: 1,
			WalletType:     "evm",
			WalletAddress:  "******************************************",
		},
		{
			OrganizationID: 1,
			WalletType:     "tron",
			WalletAddress:  "TJ11111111111111111111111111111111",
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.AssetProProfitRate{
		{
			OrganizationID:   1,
			Service:          string(domain.ProfitRateServiceTypeBridge),
			ProfitRate:       decimal.NewFromFloat(0.2),
			ProfitShareRatio: decimal.NewFromFloat(0.6),
		},
	}).Error)

	server := gin.Default()
	server.GET("/v1/bridge/fee_info", GetFeeInfo)
	server.POST("/v1/bridge/record", CreateBridgeRecord)
	server.PUT("/v1/bridge/record/:from_tx_hash", UpdateBridgeRecord)

	return server, "/v1/bridge"
}

func TestGetFeeInfo(t *testing.T) {
	server, url := setupBridgeTest(t)

	tests := []struct {
		name           string
		chainID        string
		clientID       string
		requestBody    map[string]interface{}
		expectedStatus int
		validateResp   func(t *testing.T, resp map[string]interface{})
	}{
		{
			name:           "eth success",
			clientID:       "test_client_id",
			chainID:        "eth",
			expectedStatus: http.StatusOK,
			validateResp: func(t *testing.T, resp map[string]interface{}) {
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, "eth", data["chain_id"])
				assert.Equal(t, "******************************************", data["fee_receive_address"])
				assert.Equal(t, 0.203, data["fee_rate"])
			},
		},
		{
			name:           "tron success",
			clientID:       "test_client_id",
			chainID:        "tron",
			expectedStatus: http.StatusOK,
			validateResp: func(t *testing.T, resp map[string]interface{}) {
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, "tron", data["chain_id"])
				assert.Equal(t, "TJ11111111111111111111111111111111", data["fee_receive_address"])
				assert.Equal(t, 0.203, data["fee_rate"])
			},
		},
		{
			name:           "missing client ID",
			clientID:       "",
			chainID:        "eth",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid chain ID",
			clientID:       "test_client_id",
			chainID:        "abc",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", fmt.Sprintf("%s/fee_info?chain_id=%s", url, tt.chainID), nil)
			if tt.clientID != "" {
				req.Header.Set("X-Client-ID", tt.clientID)
			}
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			var resp map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &resp)
			t.Logf("resp: %+v", resp)

			if tt.validateResp != nil {
				assert.NoError(t, err)
				tt.validateResp(t, resp)
			}
		})
	}
}

func TestCreateBridgeRecord(t *testing.T) {
	server, url := setupBridgeTest(t)

	requestBody := map[string]interface{}{
		"from_chain_id":        "arb",
		"from_address":         "0xB016Ddf38eB17B7f119fc07bDe6a2B6b68f3eA86",
		"from_token_address":   "******************************************",
		"from_amount":          "5",
		"from_tx_hash":         "0xf98a8eb02ad8da1b176628552347ed2cee72f52ae8ac2ba13ce51a1003c38f36",
		"to_chain_id":          "matic",
		"to_address":           "0xB016Ddf38eB17B7f119fc07bDe6a2B6b68f3eA86",
		"to_token_address":     "0xc2132d05d31c914a87c6611c10748aeb04b58e8f",
		"to_amount":            "4.849878",
		"fee_chain_id":         "arb",
		"fee_receive_address":  "0xcce1d3afec60d74bf18d9ec25a995a02c710ebbc",
		"fee_token_address":    "******************************************",
		"fee_tx_hash":          "0xf98a8eb02ad8da1b176628552347ed2cee72f52ae8ac2ba13ce51a1003c38f36",
		"estimated_fee_amount": "0.15",
	}
	tests := []struct {
		name           string
		chainID        string
		clientID       string
		uid            string
		requestBody    map[string]interface{}
		expectedStatus int
	}{
		{
			name:           "missing client ID",
			clientID:       "",
			chainID:        "eth",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "arb -> matic success without uid",
			clientID:       "test_client_id",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "arb -> matic success with uid",
			clientID:       "test_client_id",
			uid:            "uid123",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// truncate bridge record table
			assert.Nil(t, rdb.Get().Exec("DELETE FROM studio_organization_bridge_records").Error)
			newBody := requestBody
			newBody["uid"] = tt.uid
			body, _ := json.Marshal(newBody)
			req, _ := http.NewRequest("POST", fmt.Sprintf("%s/record", url), bytes.NewBuffer(body))
			if tt.clientID != "" {
				req.Header.Set("X-Client-ID", tt.clientID)
			}
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
