package bridge

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/bridge"
	"github.com/shopspring/decimal"
)

type getFeeInfoReq struct {
	ChainID string `form:"chain_id" binding:"required"`
}

type getFeeRateResponse struct {
	ChainID          string  `json:"chain_id"`
	FeeReceiveWallet string  `json:"fee_receive_address"`
	FeeRate          float64 `json:"fee_rate"`
}

func GetFeeInfo(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.GetFeeInfo")
	defer span.End()

	var req getFeeInfoReq
	if kgErr := util.ToGinContextExt(c).BindQuery(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get client ID from header
	clientID := c.GetHeader("X-Client-ID")
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing X-Client-ID header")
		return
	}

	orgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chain := domain.IDToChain(req.ChainID)
	if chain == nil {
		response.BadRequestWithMsg(c, code.ChainIDNotSupported, "invalid chain id")
		return
	}

	feeRate, wallet, kgErr := bridge.GetFeeInfo(ctx, orgID, chain, domain.ProfitRateServiceTypeBridge)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := getFeeRateResponse{
		ChainID:          chain.ID(),
		FeeRate:          feeRate,
		FeeReceiveWallet: wallet,
	}
	response.OK(c, resp)
}

type createBridgeRecordReq struct {
	UID              *string         `json:"kg_uid"` // If user is not logged in, this field won't be provided
	FromChainID      string          `json:"from_chain_id" binding:"required"`
	FromAddress      string          `json:"from_address" binding:"required"`
	FromTokenAddress string          `json:"from_token_address" binding:"required"`
	FromAmount       decimal.Decimal `json:"from_amount" binding:"required"`
	FromTxHash       string          `json:"from_tx_hash" binding:"required"`

	ToChainID      string          `json:"to_chain_id" binding:"required"`
	ToAddress      string          `json:"to_address" binding:"required"`
	ToTokenAddress string          `json:"to_token_address" binding:"required"`
	ToAmount       decimal.Decimal `json:"to_amount" binding:"required"`
	ToTxHash       *string         `json:"to_tx_hash"` // Usually unknown when creating bridge record

	FeeChainID         string          `json:"fee_chain_id" binding:"required"`
	FeeReceiveAddress  string          `json:"fee_receive_address" binding:"required"`
	FeeTokenAddress    string          `json:"fee_token_address" binding:"required"`
	EstimatedFeeAmount decimal.Decimal `json:"estimated_fee_amount" binding:"required"`
	FeeTxHash          string          `json:"fee_tx_hash" binding:"required"`
}

func CreateBridgeRecord(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.CreateBridgeRecord")
	defer span.End()

	var req createBridgeRecordReq

	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// If applicable, get UID and client ID from context (from access token)
	uid := req.UID
	if uidFromContext := auth.GetUID(c); uidFromContext != "" {
		uid = &uidFromContext
	}

	clientID := auth.GetClientID(c)
	if clientID == "" {
		clientID = c.GetHeader("X-Client-ID")
	}

	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing X-Client-ID header")
		return
	}

	orgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	fromChain := domain.IDToChain(req.FromChainID)
	if fromChain == nil {
		response.BadRequestWithMsg(c, code.ChainIDNotSupported, "invalid from chain id")
		return
	}
	toChain := domain.IDToChain(req.ToChainID)
	if toChain == nil {
		response.BadRequestWithMsg(c, code.ChainIDNotSupported, "invalid to chain id")
		return
	}
	feeChain := domain.IDToChain(req.FeeChainID)
	if feeChain == nil {
		response.BadRequestWithMsg(c, code.ChainIDNotSupported, "invalid fee chain id")
		return
	}

	fromAddress := domain.NewAddressByChain(fromChain, req.FromAddress)
	feeReceiveAddress := domain.NewAddressByChain(feeChain, req.FeeReceiveAddress)
	toAddress := domain.NewAddressByChain(toChain, req.ToAddress)

	params := &domain.CreateBridgeRecordParams{
		OrgID: orgID,
		UID:   uid,

		FromChain:        fromChain,
		FromAddress:      fromAddress,
		FromTokenAddress: req.FromTokenAddress,
		FromAmount:       req.FromAmount,
		FromTxHash:       req.FromTxHash,

		ToChain:        toChain,
		ToAddress:      toAddress,
		ToTokenAddress: req.ToTokenAddress,
		ToAmount:       req.ToAmount,
		ToTxHash:       req.ToTxHash,

		FeeChain:           feeChain,
		FeeReceiveAddress:  feeReceiveAddress,
		FeeTokenAddress:    req.FeeTokenAddress,
		EstimatedFeeAmount: req.EstimatedFeeAmount,
		FeeTxHash:          req.FeeTxHash,
	}

	kgErr = bridge.CreateBridgeRecord(ctx, params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

type updateBridgeRecordReq struct {
	ToTxHash string `json:"to_tx_hash" binding:"required"`
}

func UpdateBridgeRecord(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.UpdateBridgeRecord")
	defer span.End()

	var req updateBridgeRecordReq
	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	fromTxHash := c.Param("from_tx_hash")
	kgErr := bridge.UpdateBridgeRecord(ctx, fromTxHash, &domain.BridgeRecord{
		ToTxHash: &req.ToTxHash,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
