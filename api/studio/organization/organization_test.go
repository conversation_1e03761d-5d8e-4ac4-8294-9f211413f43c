package organization

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	customerrepo "github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"

	"github.com/stretchr/testify/assert"
)

// Seed database with necessary data.
func setup(t *testing.T, m sendgrid.EmailClient) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	user.Init(repo.Unified())

	initParam := organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	}

	if m != nil {
		initParam.SendgridClient = m
	}

	organization.Init(initParam)
}

func TestGetOrganizationInfo(t *testing.T) {
	s := assert.New(t)
	setup(t, nil)

	orgID := 1

	// another user but isn't owner
	s.NoError(rdb.Get().Create(&model.StudioUser{
		OrganizationID: orgID,
		UID:            util.RandString(10),
		Status:         model.StudioUserStatusActive,
		RoleBinding:    []model.StudioRoleBinding{},
	}).Error)

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/info", auth.MockOrgID(orgID), GetOrganizationInfo)

	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/info", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response struct {
		Code int `json:"code"`
		Data struct {
			ID        int                      `json:"id"`
			Name      string                   `json:"name"`
			Modules   StudioOrganizationModule `json:"modules"`
			Owners    []string                 `json:"owners"`
			CreatedAt int64                    `json:"created_at"`
		} `json:"data"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(0, response.Code)

	// check basic info
	s.Equal(orgID, response.Data.ID)
	s.Equal("KryptoGO", response.Data.Name)

	// check modules
	s.Equal(5, len(response.Data.Modules.AssetPro))
	s.Equal(3, len(response.Data.Modules.Compliance))
	s.Equal(2, len(response.Data.Modules.NFTBoost))
	s.Equal(3, len(response.Data.Modules.User360))
	s.Equal(4, len(response.Data.Modules.WalletBuilder))
	s.Equal(1, len(response.Data.Modules.Admin))

	// check owners
	s.Len(response.Data.Owners, 2)
	s.Equal("testuser2", response.Data.Owners[0])
	s.Equal("testuser1", response.Data.Owners[1])

	// check created_at
	s.NotZero(response.Data.CreatedAt)
}

func TestCreateStudioOrganization(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()
	setup(t, nil)

	shutdownSigningServer := setupSigningServer(t)
	defer shutdownSigningServer()

	r := gin.Default()
	r.POST("/_v/studio/organizations", CreateStudioOrganization)
	orgName := util.RandString(10)
	uid := util.RandString(10)
	complianceID := rand.Intn(1000)
	adminName := util.RandString(10)
	adminEmail := util.RandEmail()
	body := map[string]interface{}{
		"name":                       orgName,
		"uid":                        uid,
		"admin_name":                 adminName,
		"compliance_organization_id": complianceID,
		"email":                      adminEmail,
	}
	bodyStr, err := json.Marshal(body)
	s.NoError(err)
	w := httptest.NewRecorder()
	req, err := http.NewRequest("POST", "/_v/studio/organizations", strings.NewReader(string(bodyStr)))
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response struct {
		Code int `json:"code"`
		Data struct {
			ID int `json:"id"`
		} `json:"data"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &response)
	s.NoError(err)
	s.Equal(0, response.Code)
	s.NotZero(response.Data.ID)
	// db check
	org, kgError := rdb.GormRepo().GetOrganizationByID(ctx, response.Data.ID)
	s.Nil(kgError)
	s.Equal(orgName, org.Name)
	s.Equal(complianceID, org.ComplianceOrganizationID)
	user, kgError := rdb.GormRepo().GetStudioUser(ctx, response.Data.ID, uid)
	s.Nil(kgError)
	s.Equal("active", string(user.Status))
	s.Equal(adminName, user.Name)
	s.Contains(user.Roles, domain.StudioRole{Module: "", Name: "owner"})
	s.Equal(adminEmail, user.Email)
	// org wallet
	wallet, err := rdb.GormRepo().GetOrgWallet(ctx, org.ID, "evm")
	s.Nil(err)
	s.NotZero(wallet.ID)
	wallet, err = rdb.GormRepo().GetOrgWallet(ctx, org.ID, "tron")
	s.Nil(err)
	s.NotZero(wallet.ID)

	roles, kgError := organization.GetStudioRoleByUserFromCache(ctx, org.ID, uid)
	s.Nil(kgError)
	s.Contains(roles, domain.StudioRole{Module: "", Name: "owner"})

	// duplicate org name
	w = httptest.NewRecorder()
	req, err = http.NewRequest("POST", "/_v/studio/organizations", strings.NewReader(string(bodyStr)))
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusConflict, w.Code)
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(7013, response.Code)
}

func TestGetOrganizationsByActiveUser(t *testing.T) {
	s := assert.New(t)
	rdb.Reset()
	customer.Init(customerrepo.NewCustomerRepo())

	// setup firebase user
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)
	s.NoError(err)

	organizations := []model.StudioOrganization{
		{ID: 1, Name: "org1", IconURL: util.Ptr("https://hello.world/favicon.ico")},
		{ID: 2, Name: "org2"},
		{ID: 3, Name: "org3"},
	}

	s.NoError(rdb.Get().Create(&organizations).Error)

	studioUsers := []model.StudioUser{
		{
			OrganizationID: 1,
			UID:            uid,
			Status:         model.StudioUserStatusActive,
		},
		{
			OrganizationID: 2,
			UID:            uid,
			Status:         model.StudioUserStatusActive,
		},
	}

	s.NoError(rdb.Get().Create(&studioUsers).Error)

	r := gin.Default()
	r.GET("/v1/studio/organizations", func(c *gin.Context) {
		if c.GetHeader("Authorization") != "Bearer uid" {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		c.Set("uid", uid)
	}, GetOrganizationsByActiveUser)

	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organizations", nil)

		r.ServeHTTP(w, req)

		s.Equal(http.StatusUnauthorized, w.Code)
	}
	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organizations", nil)
		req.Header.Set("Authorization", "Bearer uid unknown")

		r.ServeHTTP(w, req)

		s.Equal(http.StatusUnauthorized, w.Code)
	}
	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organizations", nil)
		req.Header.Set("Authorization", "Bearer uid")

		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code          int `json:"code"`
			Organizations []struct {
				ID      int     `json:"id"`
				Name    string  `json:"name"`
				IconURL *string `json:"icon_url"`
			} `json:"data"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Len(response.Organizations, 2)
		s.Equal(1, response.Organizations[0].ID)
		s.Equal("org1", response.Organizations[0].Name)
		s.Equal("https://hello.world/favicon.ico", *response.Organizations[0].IconURL)
		s.Equal(2, response.Organizations[1].ID)
		s.Equal("org2", response.Organizations[1].Name)
		s.Nil(response.Organizations[1].IconURL)
	}
}

func setupSigningServer(t *testing.T) func() {
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	// signing server
	rSigning := gin.Default()
	rSigning.POST("/v1/wallets", signing.AuthorizeSigningServer, signing.CreateOrganizationWallet)
	// create HTTP server for graceful shutdown
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	time.Sleep(1 * time.Second) // wait for server to start

	return func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}
}

func TestGetOrgAccounts(t *testing.T) {
	s := assert.New(t)
	setup(t, nil)

	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/accounts", auth.MockOrgID(orgID), AccountV2)

	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/accounts", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	type wallet struct {
		ChainID string `json:"chain_id"`
		Address string `json:"address"`
	}
	var response struct {
		Code int      `json:"code"`
		Data []wallet `json:"data"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(0, response.Code)
	s.Equal(8, len(response.Data))

	for _, v := range response.Data {
		if v.ChainID == "tron" || v.ChainID == "shasta" {
			s.Equal("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", v.Address)
		} else {
			s.Equal("******************************************", v.Address)
		}
	}
}
