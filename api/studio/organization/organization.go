package organization

import (
	"errors"
	"net/url"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tokens"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type StudioOrganizationModule struct {
	User360       []domain.User360       `json:"user_360,omitempty"`
	WalletBuilder []domain.WalletBuilder `json:"wallet_builder,omitempty"`
	AssetPro      []domain.AssetPro      `json:"asset_pro,omitempty"`
	NFTBoost      []domain.NFTBoost      `json:"nft_boost,omitempty"`
	Compliance    []domain.Compliance    `json:"compliance,omitempty"`
	Admin         []domain.Admin         `json:"admin,omitempty"`
}

func (o *StudioOrganizationModule) FromDomainModule(domainModule *domain.StudioOrganizationModule) {
	o.User360 = domainModule.User360
	o.WalletBuilder = domainModule.WalletBuilder
	o.AssetPro = domainModule.AssetPro
	o.NFTBoost = domainModule.NFTBoost
	o.Compliance = domainModule.Compliance
	o.Admin = domainModule.Admin
}

// GetOrganizationInfo get studio organization info
func GetOrganizationInfo(c *gin.Context) {
	orgID := c.GetInt("org_id")

	orgInfo, kgErr := organization.GetOrgInfo(c, orgID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	type orgInfoResp struct {
		ID        int                      `json:"id"`
		Name      string                   `json:"name"`
		Modules   StudioOrganizationModule `json:"modules"`
		Owners    []string                 `json:"owners"`
		CreatedAt int64                    `json:"created_at"`
		IconURL   *string                  `json:"icon_url"`
	}
	var resp orgInfoResp

	resp.ID = orgInfo.ID
	resp.Name = orgInfo.Name
	resp.Modules.FromDomainModule(&orgInfo.Modules)
	resp.Owners = orgInfo.Owners
	resp.CreatedAt = orgInfo.CreatedAt
	resp.IconURL = orgInfo.IconURL

	response.OK(c, resp)
}

type updateOrganizationReq struct {
	Name    string  `json:"name" binding:"required"`
	IconURL *string `json:"icon_url"`
}

func (req *updateOrganizationReq) AfterValidate() error {
	if req.IconURL != nil {
		parsedUrl, err := url.ParseRequestURI(*req.IconURL)
		if err != nil {
			return err
		}
		if !(parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https") {
			return errors.New("invalid icon url")
		}
	}

	return nil
}

// SaveOrganizationInfo update studio organization info by studio user.
func SaveOrganizationInfo(c *gin.Context) {
	ctx := c.Request.Context()
	req := updateOrganizationReq{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID := c.GetInt("org_id")

	kgErr = organization.SaveOrganization(ctx, orgID, domain.SaveOrganizationRequest{
		Name:    req.Name,
		IconURL: req.IconURL,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// CreateStudioOrganization create studio organization
func CreateStudioOrganization(c *gin.Context) {
	ctx := c.Request.Context()
	params := organization.CreateOrganizationReq{}
	kgErr := util.ToGinContextExt(c).BindJson(&params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	data, kgErr := organization.CreateOrganization(ctx, &params)
	if kgErr != nil {
		kglog.WarningWithDataCtx(ctx, "create organization failed", kgErr.String())
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, data)
}

// UpdateStudioOrganization updates full studio organization.
func UpdateStudioOrganization(c *gin.Context) {
	ctx := c.Request.Context()
	params := organization.UpdateOrganizationReq{}
	kgErr := util.ToGinContextExt(c).BindJson(&params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid org id")
		return
	}

	kgErr = organization.UpdateOrganization(ctx, orgID, &params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

type org struct {
	ID      int     `json:"id"`
	Name    string  `json:"name"`
	IconURL *string `json:"icon_url"`
}

// GetOrganizationsByActiveUser returns organizations which the user is in.
func GetOrganizationsByActiveUser(c *gin.Context) {
	userID := c.GetString("uid")

	organizations, err := rdb.GormRepo().GetOrganizationsByActiveUser(c.Request.Context(), userID)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	}

	orgs := make([]org, 0, len(organizations))

	for _, organization := range organizations {
		orgs = append(orgs, org{
			ID:      organization.ID,
			Name:    organization.Name,
			IconURL: organization.IconURL,
		})
	}

	response.OK(c, orgs)
}

type createStudioDappReq struct {
	Dapps []studioDappData `json:"dapps" binding:"required"`
}

type studioDappData struct {
	Title    string `json:"title" binding:"required"`
	Desc     string `json:"desc" binding:"required"`
	ImageURL string `json:"image_url" binding:"required"`
	SiteURL  string `json:"site_url" binding:"required"`
	IsActive bool   `json:"is_active" binding:"required"`
}

// CreateStudioDapp create studio dapp
func CreateStudioDapp(c *gin.Context) {
	ctx := c.Request.Context()
	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid org id")
		return
	}

	params := createStudioDappReq{}
	kgErr := util.ToGinContextExt(c).BindJson(&params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	dapps := make([]domain.StudioOrganizationDapp, 0, len(params.Dapps))
	for _, dapp := range params.Dapps {
		dapps = append(dapps, domain.StudioOrganizationDapp{
			Title:    dapp.Title,
			Desc:     dapp.Desc,
			ImageURL: dapp.ImageURL,
			SiteURL:  dapp.SiteURL,
			IsActive: dapp.IsActive,
		})
	}

	kgErr = organization.CreateDapps(ctx, orgID, dapps)
	if kgErr != nil {
		kglog.WarningWithDataCtx(ctx, "create dapps failed", kgErr.String())
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

type wallet struct {
	ChainID string `json:"chain_id"`
	Address string `json:"address"`
}

// AccountV2 get account info v2
func AccountV2(c *gin.Context) {
	orgID := c.GetInt("org_id")

	wallets, err := organization.GetOrgWallets(c.Request.Context(), orgID)
	if err != nil {
		kglog.WarningWithDataCtx(c.Request.Context(), "GetOrgWallets", map[string]interface{}{
			"org_id": orgID,
			"err":    err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}

	supportedTokens := tokens.GetSupportedTokens()
	mSupportedChainIDs := make(map[string]struct{})

	for _, token := range supportedTokens {
		mSupportedChainIDs[token.ChainID] = struct{}{}
	}

	mSupportedVMs := map[string][]string{
		"evm":  model.EVMChainIDs,
		"tron": model.TVMChainIDs,
	}

	vmSupportedChainIDs := func(vm string) []string {
		supportedChainIDs := make([]string, 0)
		for _, chainID := range mSupportedVMs[vm] {
			if _, ok := mSupportedChainIDs[chainID]; ok {
				supportedChainIDs = append(supportedChainIDs, chainID)
			}
		}
		return supportedChainIDs
	}

	respWallet := make([]wallet, 0)
	for i := range wallets {
		if wallets[i].WalletType != "evm" &&
			wallets[i].WalletType != "tron" {
			continue
		}

		supportedChainIDs := vmSupportedChainIDs(wallets[i].WalletType)
		for _, chainID := range supportedChainIDs {
			respWallet = append(respWallet, wallet{
				ChainID: chainID,
				Address: wallets[i].WalletAddress,
			})
		}
	}
	response.OK(c, respWallet)
}
