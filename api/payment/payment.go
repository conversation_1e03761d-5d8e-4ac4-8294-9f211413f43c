package payment

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/payment"
	"github.com/shopspring/decimal"
)

type CreatePaymentIntentReq struct {
	// Legacy fields - kept for backward compatibility

	// Deprecated: Use Amount instead
	FiatAmount string `json:"fiat_amount"`
	// Deprecated: Use Currency instead
	FiatCurrency string `json:"fiat_currency"`
	// New unified fields
	PricingMode payment.PricingMode `json:"pricing_mode"` // "fiat" or "crypto"
	Amount      string              `json:"amount"`       // Amount in either fiat or crypto depending on pricing_mode

	Currency string `json:"currency"` // Fiat currency code (used only in fiat mode)

	PayToken string `json:"pay_token"` // Token to use for payment (USDC or USDT)
	Chain    string `json:"chain"`     // blockchain network to use (supports "arbitrum", "optimism", "base")

	// Common fields
	OrderData           map[string]interface{} `json:"order_data"`
	CallbackURL         *string                `json:"callback_url"`
	GroupKey            *string                `json:"group_key"`
	PayoutTargetAddress *string                `json:"payout_target_address"`
}

// CreatePaymentIntent handles the creation of a new payment intent
func CreatePaymentIntent(c *gin.Context) {
	ctx := c.Request.Context()

	clientID := c.GetString("client_id")
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing client ID (X-Client-ID header)")
		return
	}
	origin := c.GetString("origin")

	var req CreatePaymentIntentReq
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	// Default chain to Arbitrum
	var chain domain.Chain = domain.Arbitrum
	if req.Chain != "" {
		chain = domain.IDToChain(req.Chain)
		if chain == nil {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid chain. supported chains: arb, optimism, base")
			return
		}

		// Ensure only supported payment chains are allowed
		if chain != domain.Arbitrum && chain != domain.Optimism && chain != domain.BaseChain {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid chain. supported chains: arb, optimism, base")
			return
		}
	}

	// Check if we're using new parameters (amount, pricing_mode, pay_token)
	usingNewParams := req.Amount != ""
	// Check if we're using legacy parameters (fiat_amount, fiat_currency)
	usingLegacyParams := req.FiatAmount != ""

	var createParams payment.CreateIntentParams
	createParams.ClientID = clientID
	createParams.Origin = origin
	createParams.OrderData = req.OrderData
	createParams.CallbackURL = req.CallbackURL
	createParams.GroupKey = req.GroupKey
	createParams.PayoutTargetAddress = *req.PayoutTargetAddress

	if usingNewParams {
		// --- New Parameters Flow ---

		// Validate pricing mode
		if req.PricingMode == "" {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "when using 'amount', 'pricing_mode' must also be specified")
			return
		}

		if !req.PricingMode.IsValid() {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid pricing_mode, must be either 'fiat' or 'crypto'")
			return
		}

		// Validate PayToken
		if req.PayToken == "" {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "pay_token is required")
			return
		}

		payToken := strings.ToUpper(req.PayToken)
		if payToken != "USDC" && payToken != "USDT" {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "pay_token must be either USDC or USDT")
			return
		}

		createParams.Amount = req.Amount
		createParams.PricingMode = req.PricingMode
		createParams.PayToken = payToken

		if req.PricingMode == payment.PricingModeFiat {
			// Fiat mode requires a currency
			if req.Currency == "" {
				response.BadRequestWithMsg(c, code.ParamIncorrect, "currency is required for fiat pricing mode")
				return
			}
			currencyStr := req.Currency
			createParams.Currency = &currencyStr
		} else {
			// In crypto mode, currency should be nil
			// This ensures we don't save inapplicable data
			createParams.Currency = nil
		}
	} else if usingLegacyParams {
		// --- Legacy Parameters Flow ---

		// Validate required parameters
		if req.FiatCurrency == "" {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "when using 'fiat_amount', 'fiat_currency' must also be specified")
			return
		}

		// For legacy compatibility, if no PayToken is specified, default to USDT
		payToken := "USDT"
		if req.PayToken != "" {
			payToken = strings.ToUpper(req.PayToken)
			if payToken != "USDC" && payToken != "USDT" {
				response.BadRequestWithMsg(c, code.ParamIncorrect, "pay_token must be either USDC or USDT")
				return
			}
		}

		// Legacy flow always uses fiat pricing mode
		createParams.Amount = req.FiatAmount
		fiatCurrency := req.FiatCurrency
		createParams.Currency = &fiatCurrency
		createParams.PricingMode = payment.PricingModeFiat
		createParams.PayToken = payToken
	} else {
		// Neither set of parameters is being used
		response.BadRequestWithMsg(c, code.ParamIncorrect, "either both 'amount'+'pricing_mode'+'pay_token' or both 'fiat_amount'+'fiat_currency' must be specified")
		return
	}

	// Create the payment intent
	createParams.Chain = chain
	intent, kgErr := payment.CreateIntent(ctx, createParams)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, intent)
}

// GetPaymentIntent handles retrieving a payment intent by ID
func GetPaymentIntent(c *gin.Context) {
	ctx := c.Request.Context()

	clientID := c.GetString("client_id")
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing client ID (X-Client-ID header)")
		return
	}

	// Get payment intent ID from path
	id := c.Param("id")
	if id == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing payment intent id")
		return
	}

	intent, kgErr := payment.GetIntent(ctx, clientID, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, intent)
}

type RefundPaymentIntentReq struct {
	RefundCryptoAmount string `json:"refund_crypto_amount"`
	To                 string `json:"to"`
}

func RefundPaymentIntent(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "RefundPaymentIntent")
	defer span.End()

	uid := c.GetString("uid")
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing uid")
		return
	}

	id := c.Param("id")
	if id == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing payment intent id")
		return
	}

	var req RefundPaymentIntentReq
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	refundAmount, err := decimal.NewFromString(req.RefundCryptoAmount)
	if err != nil {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil))
		return
	}

	kgErr := payment.RefundIntent(ctx, uid, &payment.RefundIntentRequest{
		IntentID:           id,
		RefundCryptoAmount: refundAmount,
		To:                 req.To,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// GetPaymentIntents handles retrieving all payment intents for a client
func GetPaymentIntents(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "GetPaymentIntents")
	defer span.End()

	// Must have org ID
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing org ID")
		return
	}

	// Get pagination parameters from context
	pageNum, pageSizeNum, _ := middleware.GetPaginationFromContext(c)

	clientID := c.Query("client_id")
	statusValues := c.QueryArray("status")
	groupKey := c.Query("group_key")
	chainID := c.Query("chain_id")

	// Create params for GetIntents
	params := domain.GetPaymentIntentsParams{
		Page:     pageNum,
		PageSize: pageSizeNum,
		OrgID:    orgID,
	}

	// Handle status filtering
	if len(statusValues) > 0 {
		statuses := make([]domain.PaymentIntentStatus, 0, len(statusValues))

		for _, status := range statusValues {
			statusEnum := domain.PaymentIntentStatus(status)
			if !statusEnum.IsValid() {
				response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid status parameter: "+status)
				return
			}
			statuses = append(statuses, statusEnum)
		}

		params.Status = statuses
	}

	if clientID != "" {
		params.ClientID = &clientID
	}

	if groupKey != "" {
		params.GroupKey = &groupKey
	}

	if chainID != "" {
		params.ChainID = &chainID
	}

	intents, totalCount, kgErr := payment.GetIntents(ctx, params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Create paging info
	paging := response.Paging{
		PageNumber: pageNum,
		PageSize:   pageSizeNum,
		TotalCount: totalCount,
	}

	response.OKWithPaging(c, intents, paging)
}

func CheckAndUpdatePendingIntent(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "CheckAndUpdateIntent")
	defer span.End()

	var req struct {
		Timeout int `json:"timeout" binding:"required"`
	}

	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Create a context with timeout based on requestBody.Timeout
	ctxWithTimeout, cancel := tracing.WithTimeoutAndTrace(ctx, time.Duration(req.Timeout)*time.Second)
	defer cancel()

	err := payment.CheckAndUpdatePendingIntentsUntilTimeout(ctxWithTimeout)
	if err != nil && err != context.DeadlineExceeded {
		c.String(http.StatusInternalServerError, fmt.Sprintf("failed to check and update pending intent: %v", err))
		return
	}

	c.String(http.StatusOK, "done")
}
