{"permissions": {"allow": ["Bash(find:*)", "Bash(make lint)", "Bash(go generate:*)", "Bash(go install:*)", "Bash(go build:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(go test:*)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)", "Bash(rm:*)", "<PERSON><PERSON>(timeout 60 go test -run TestIntentE2E)", "<PERSON><PERSON>(golangci-lint run:*)"], "deny": []}}