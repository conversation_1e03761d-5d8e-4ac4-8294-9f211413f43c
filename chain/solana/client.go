package solana

import (
	"bytes"
	"context"
	"encoding/binary"
	"fmt"
	"math/big"
	"sort"
	"time"

	solanago "github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/jpillora/backoff"
	"github.com/kryptogo/kg-wallet-backend/domain"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/mr-tron/base58"
	"github.com/portto/solana-go-sdk/common"
	"github.com/shopspring/decimal"
)

const (
	PUMP_FUN_PROGRAM = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"
)

func (c *clientImpl) BlockNumber(ctx context.Context) (uint64, error) {
	return solanaapi.Get().GetLatestBlockNumber(ctx)
}

func (c *clientImpl) NativeBalance(ctx context.Context, address domain.Address) (*big.Int, error) {
	balance, err := solanaapi.Get().GetSolBalance(ctx, address.String())
	if err != nil {
		return nil, err
	}
	return balance.Mul(decimal.NewFromInt(**********)).BigInt(), nil
}

func getTokenAccount(owner, mint string) (string, error) {
	ownerPubKey := common.PublicKeyFromString(owner)
	mintPubKey := common.PublicKeyFromString(mint)
	ata, _, err := common.FindAssociatedTokenAddress(ownerPubKey, mintPubKey)
	if err != nil {
		return "", err
	}
	return ata.ToBase58(), nil
}

func (c *clientImpl) TokenBalance(ctx context.Context, address domain.Address, tokenID string) (*big.Int, error) {
	tokenAccount, err := getTokenAccount(address.String(), tokenID)
	if err != nil {
		return nil, err
	}
	// Fetch token accounts by owner and filter by token mint
	amount, err := solanaapi.Get().GetTokenAccountBalance(ctx, tokenAccount)
	if err != nil {
		return nil, fmt.Errorf("failed to get token accounts: %w", err)
	}

	return amount, nil
}

func (c *clientImpl) GetTransactionStatus(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	txResp, err := solanaapi.Get().GetTransaction(ctx, txHash)
	if err != nil {
		return domain.TransactionStatusUnknown, fmt.Errorf("failed to get transaction: %w", err)
	}

	// tx not found
	if txResp.Result.BlockTime == 0 {
		return domain.TransactionStatusUnknown, fmt.Errorf("transaction not found")
	}

	if txResp.Result.Meta.Err == nil {
		return domain.TransactionStatusSuccess, nil
	}

	return domain.TransactionStatusFailed, nil
}

func (c *clientImpl) WaitUntilTransactionConfirmed(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	// Configure backoff parameters based on the average block time.
	minWait := c.chain.BlockTime() / 2
	maxWait := c.chain.BlockTime() * 2
	b := &backoff.Backoff{
		Min:    minWait,
		Max:    maxWait,
		Factor: 2,
		Jitter: true,
	}

	for {
		select {
		case <-ctx.Done():
			return domain.TransactionStatusUnknown, ctx.Err()
		default:
			status, err := c.GetTransactionStatus(ctx, txHash)
			if err != nil {
				if ctx.Err() != nil {
					return domain.TransactionStatusUnknown, ctx.Err()
				}
			} else if status != domain.TransactionStatusUnknown {
				return status, nil
			}

			timer := time.NewTimer(b.Duration())
			select {
			case <-ctx.Done():
				// return as soon as the context is done
				timer.Stop()
				return domain.TransactionStatusUnknown, ctx.Err()
			case <-timer.C:
				// Continue with the next iteration
			}
		}
	}
}

// TransactionDetail fetches transaction details for a given transaction hash.
func (c *clientImpl) TransactionDetail(ctx context.Context, txHash string) (*domain.TransactionDetail, error) {
	txResp, err := solanaapi.Get().GetTransaction(ctx, txHash)
	if err != nil {
		return nil, err
	}

	if txResp.Result.BlockTime == 0 {
		kglog.DebugWithDataCtx(ctx, "Transaction not found", map[string]interface{}{"result": txResp.Result})
		return nil, fmt.Errorf("transaction not found")
	}

	// Parse transaction details
	timestamp := time.Unix(txResp.Result.BlockTime, 0)
	gasUsed := big.NewInt(int64(txResp.Result.Meta.Fee))

	// Get the fee payer (first account) as the from address
	from := domain.NewAddressByChain(c.chain, txResp.Result.Transaction.Message.AccountKeys[0].Pubkey)

	// Parse transfers
	internalTransfers := c.parseInternalTransfers(
		txResp.Result.Transaction.Message.Instructions,
		txResp.Result.Meta.InnerInstructions,
		txResp.Result.Meta.PreTokenBalances,
		txResp.Result.Meta.PostTokenBalances,
		txResp.Result.Transaction.Message.AccountKeys,
	)
	tokenTransfers := c.parseTokenTransfers(
		txResp.Result.Transaction.Message.AccountKeys,
		txResp.Result.Transaction.Message.Instructions,
		txResp.Result.Meta.InnerInstructions,
		txResp.Result.Meta.PreTokenBalances,
		txResp.Result.Meta.PostTokenBalances,
	)

	// Determine main transaction value and to address
	var to domain.Address

	// For simple transfers (1-to-1), set the main to/value
	if len(internalTransfers) == 1 && len(tokenTransfers) == 0 {
		// Simple SOL transfer - set to as System Program
		to = domain.NewAddressByChain(c.chain, "11111111111111111111111111111111")
	} else if len(internalTransfers) == 0 && len(tokenTransfers) == 1 {
		// Simple token transfer - set to as Token Program
		to = domain.NewAddressByChain(c.chain, "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")
	} else {
		// Complex transaction - use the first instruction's program ID
		// Filter out some instructions that are not relevant
		programPriority := func(pid string) int {
			switch pid {
			case "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA":
				return 1
			case "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb":
				return 2
			case "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL":
				return 3
			case "11111111111111111111111111111111":
				return 4
			case "ComputeBudget111111111111111111111111111111":
				return 5
			default:
				return 0
			}
		}

		instructions := txResp.Result.Transaction.Message.Instructions
		sort.Slice(instructions, func(a, b int) bool {
			return programPriority(instructions[a].ProgramId) < programPriority(instructions[b].ProgramId)
		})
		if len(instructions) > 0 {
			to = domain.NewAddressByChain(c.chain, instructions[0].ProgramId)
		}
	}

	detail := &domain.TransactionDetail{
		Chain:     c.chain,
		Hash:      txHash,
		BlockNum:  uint32(txResp.Result.Slot),
		IsError:   txResp.Result.Meta.Err != nil,
		From:      from,
		To:        to,
		Value:     nil,           // All solana transactions are perceived as interaction with programs, not direct transfer of value
		GasPrice:  big.NewInt(0), // Solana doesn't have gas price
		GasUsed:   gasUsed,
		Timestamp: timestamp,
		TransactionTransfers: domain.TransactionTransfers{
			InternalTransfers: internalTransfers,
			TokenTransfers:    tokenTransfers,
		},
	}

	return detail, nil
}

// Helper function to get all instructions including inner instructions
func getAllInstructions(instructions []solanaapi.Instruction, innerInstructions []solanaapi.InnerInstruction) []solanaapi.Instruction {
	allInstructions := append([]solanaapi.Instruction{}, instructions...)
	for _, inner := range innerInstructions {
		allInstructions = append(allInstructions, inner.Instructions...)
	}
	return allInstructions
}

// accountInfo holds information about a token account
type accountInfo struct {
	owner string
	mint  string
}

// Helper function to track token account owners and mints
func getTokenAccountInfo(accountKeys []solanaapi.AccountKey, preBalances, postBalances []solanaapi.TokenBalance) map[string]accountInfo {
	tokenAccounts := make(map[string]accountInfo)

	// Build token account mapping from pre/post balances
	for _, balance := range preBalances {
		tokenAccount := accountKeys[balance.AccountIndex].Pubkey
		tokenAccounts[tokenAccount] = accountInfo{
			owner: balance.Owner,
			mint:  balance.Mint,
		}
	}
	for _, balance := range postBalances {
		tokenAccount := accountKeys[balance.AccountIndex].Pubkey
		tokenAccounts[tokenAccount] = accountInfo{
			owner: balance.Owner,
			mint:  balance.Mint,
		}
	}
	return tokenAccounts
}

// Helper function to get WSOL account owners
func getWSOLAccountOwners(accountKeys []solanaapi.AccountKey, instructions []solanaapi.Instruction, innerInstructions []solanaapi.InnerInstruction, preBalances, postBalances []solanaapi.TokenBalance) map[string]string {
	wSolAccountOwners := make(map[string]string)

	// Track WSOL token account owners from pre-balances and post-balances
	for _, balance := range preBalances {
		if balance.Mint == "So11111111111111111111111111111111111111112" {
			accountPubkey := accountKeys[balance.AccountIndex].Pubkey
			wSolAccountOwners[accountPubkey] = balance.Owner
		}
	}
	for _, balance := range postBalances {
		if balance.Mint == "So11111111111111111111111111111111111111112" {
			accountPubkey := accountKeys[balance.AccountIndex].Pubkey
			wSolAccountOwners[accountPubkey] = balance.Owner
		}
	}

	// Then check token program initialization instructions
	allInstructions := getAllInstructions(instructions, innerInstructions)
	for _, instruction := range allInstructions {
		if instruction.ProgramId == "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" {
			// Check if it's an initialize account instruction
			if instruction.Parsed.Type == "initializeAccount3" {
				info := instruction.Parsed.Info
				if info.Mint == "So11111111111111111111111111111111111111112" {
					wSolAccountOwners[info.Account] = info.Owner
				}
			}
		}
	}

	return wSolAccountOwners
}

// parseInternalTransfers analyzes system program instructions for native SOL transfers
func (c *clientImpl) parseInternalTransfers(instructions []solanaapi.Instruction, innerInstructions []solanaapi.InnerInstruction, preBalances, postBalances []solanaapi.TokenBalance, accountKeys []solanaapi.AccountKey) []*domain.NativeTokenTransfer {
	var transfers []*domain.NativeTokenTransfer
	wSolAccountOwners := getWSOLAccountOwners(accountKeys, instructions, innerInstructions, preBalances, postBalances)
	allInstructions := getAllInstructions(instructions, innerInstructions)

	for _, instruction := range allInstructions {
		if instruction.ProgramId == "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" {
			info := instruction.Parsed.Info

			// Track WSOL account initialization
			if instruction.Parsed.Type == "initializeAccount" && info.Mint == "So11111111111111111111111111111111111111112" {
				wSolAccountOwners[info.Account] = info.Owner
			}

			// Handle WSOL transfers as internal transfers
			if instruction.Parsed.Type == "transfer" || instruction.Parsed.Type == "transferChecked" {
				var amount *big.Int
				var ok bool

				// Handle both transfer and transferChecked amount formats
				if info.TokenAmount.Amount != "" {
					amount, ok = new(big.Int).SetString(info.TokenAmount.Amount, 10)
				} else {
					amount, ok = new(big.Int).SetString(info.Amount, 10)
				}
				if !ok {
					continue
				}

				// Get the owner of the source and destination token accounts
				if srcOwner, ok := wSolAccountOwners[info.Source]; ok {
					if destOwner, ok := wSolAccountOwners[info.Destination]; ok {
						// Only add transfer if it's between different owners
						if srcOwner != destOwner {
							transfers = append(transfers, &domain.NativeTokenTransfer{
								From:   domain.NewAddressByChain(c.chain, srcOwner),
								To:     domain.NewAddressByChain(c.chain, destOwner),
								Amount: amount,
							})
						}
					}
				}
			}
		} else if instruction.ProgramId == "11111111111111111111111111111111" && instruction.Parsed.Type == "transfer" {
			info := instruction.Parsed.Info

			source := info.Source
			destination := info.Destination
			lamports := info.Lamports

			if source == "" || destination == "" {
				continue
			}

			// Skip if it's transferring to its own WSOL account
			if wSol, ok := wSolAccountOwners[destination]; ok && wSol == source {
				continue
			}

			transfers = append(transfers, &domain.NativeTokenTransfer{
				From:   domain.NewAddressByChain(c.chain, source),
				To:     domain.NewAddressByChain(c.chain, destination),
				Amount: big.NewInt(int64(lamports)),
			})
		} else if instruction.ProgramId == PUMP_FUN_PROGRAM && len(instruction.Data) > 0 {
			transfer := c.parsePumpFunNativeTransfer(instruction.Data)
			if transfer != nil {
				transfers = append(transfers, transfer)
			}
		}
	}

	// Sort transfers by value in descending order
	sort.Slice(transfers, func(i, j int) bool {
		return transfers[i].Amount.Cmp(transfers[j].Amount) > 0
	})

	return transfers
}

// parsePumpFunNativeTransfer parses a Pump Fun program instruction to extract SOL transfer details
func (c *clientImpl) parsePumpFunNativeTransfer(data string) *domain.NativeTokenTransfer {
	decodedData, err := base58.Decode(data)
	if err != nil {
		return nil
	}

	// Check if the decoded data is a pump fun sell log, and the first 16 bytes match the expected signature
	if len(decodedData) != 137 || decodedData[64] != 0 {
		return nil
	}

	expectedSignature := []byte{0xe4, 0x45, 0xa5, 0x2e, 0x51, 0xcb, 0x9a, 0x1d, 0xbd, 0xdb, 0x7f, 0xd3, 0x4e, 0xe6, 0x61, 0xee}
	if !bytes.Equal(decodedData[:16], expectedSignature) {
		return nil
	}

	// Parse SOL amount from bytes 48-56 (little endian)
	solAmountBytes := decodedData[48:56]
	solAmount := binary.LittleEndian.Uint64(solAmountBytes)

	// Parse user Solana public key from bytes 65-97
	userPubKeyBytes := decodedData[65:97]
	userPubKey := base58.Encode(userPubKeyBytes)

	// Create transfer object. This data can only be found in program log
	return &domain.NativeTokenTransfer{
		From:   domain.NewAddressByChain(c.chain, "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"),
		To:     domain.NewAddressByChain(c.chain, userPubKey),
		Amount: big.NewInt(int64(solAmount * 99 / 100)), // pump fun has 1% fee
	}
}

// parseTokenTransfers analyzes token program instructions and balances to detect transfers
func (c *clientImpl) parseTokenTransfers(accountKeys []solanaapi.AccountKey, instructions []solanaapi.Instruction, innerInstructions []solanaapi.InnerInstruction, preBalances, postBalances []solanaapi.TokenBalance) []*domain.TokenTransfer {
	var transfers []*domain.TokenTransfer
	processedTransfers := make(map[string]bool)
	tokenAccounts := getTokenAccountInfo(accountKeys, preBalances, postBalances)
	allInstructions := getAllInstructions(instructions, innerInstructions)

	for _, instruction := range allInstructions {
		if instruction.ProgramId != "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" && instruction.ProgramId != "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb" {
			continue
		}

		info := instruction.Parsed.Info
		switch instruction.Parsed.Type {
		case "transfer", "transferChecked":
			amount := info.TokenAmount.Amount
			if amount == "" {
				amount = info.Amount
			}
			source := info.Source
			destination := info.Destination
			authority := info.Authority
			if authority == "" {
				authority = info.MultisigAuthority
			}
			if source == "" || destination == "" || authority == "" {
				continue
			}

			// Get mint from instruction or account info
			var mint string
			if info.Mint != "" {
				mint = info.Mint
			} else if info, ok := tokenAccounts[destination]; ok {
				mint = info.mint
			} else if info, ok := tokenAccounts[source]; ok {
				mint = info.mint
			} else {
				continue
			}

			// Skip WSOL transfers as they're handled as internal transfers
			if mint == "So11111111111111111111111111111111111111112" {
				continue
			}

			transferAmount, ok := new(big.Int).SetString(amount, 10)
			if !ok {
				continue
			}

			transferKey := fmt.Sprintf("%s-%s-%s-%s",
				mint,
				authority,
				destination,
				amount)

			if !processedTransfers[transferKey] {
				transfers = append(transfers, &domain.TokenTransfer{
					Contract: domain.NewAddressByChain(c.chain, mint),
					From:     domain.NewAddressByChain(c.chain, authority),
					To:       domain.NewAddressByChain(c.chain, destination),
					Amount:   transferAmount,
				})
				processedTransfers[transferKey] = true
			}
		}
	}

	// Transform address in token transfer to be owner address if applicable
	for _, transfer := range transfers {
		if info, ok := tokenAccounts[transfer.From.String()]; ok {
			transfer.From = domain.NewAddressByChain(c.chain, info.owner)
		}
		if info, ok := tokenAccounts[transfer.To.String()]; ok {
			transfer.To = domain.NewAddressByChain(c.chain, info.owner)
		}
	}

	// Sort transfers to maintain consistent order
	sort.Slice(transfers, func(i, j int) bool {
		if transfers[i].Contract.String() != transfers[j].Contract.String() {
			return transfers[i].Contract.String() < transfers[j].Contract.String()
		}
		if transfers[i].From.String() != transfers[j].From.String() {
			return transfers[i].From.String() < transfers[j].From.String()
		}
		return transfers[i].Amount.Cmp(transfers[j].Amount) < 0
	})

	return transfers
}

func (c *clientImpl) BroadcastTransaction(ctx context.Context, tx *solanago.Transaction) (string, error) {
	client := rpc.New(rpc.MainNetBeta.RPC)
	sig, err := client.SendTransactionWithOpts(ctx, tx, rpc.TransactionOpts{
		SkipPreflight:       false,
		PreflightCommitment: rpc.CommitmentConfirmed,
	})
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}
	return sig.String(), nil
}

func (c *clientImpl) BroadcastRawTransaction(ctx context.Context, rawTx string) (string, error) {
	client := rpc.New(rpc.MainNetBeta.RPC)
	txBytes, err := base58.Decode(rawTx)
	if err != nil {
		return "", fmt.Errorf("failed to decode transaction: %w", err)
	}
	sig, err := client.SendRawTransactionWithOpts(ctx, txBytes, rpc.TransactionOpts{
		SkipPreflight:       false,
		PreflightCommitment: rpc.CommitmentConfirmed,
	})
	if err != nil {
		return "", fmt.Errorf("failed to send raw transaction: %w", err)
	}
	return sig.String(), nil
}
